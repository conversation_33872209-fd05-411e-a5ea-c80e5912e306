stages:
  - test
  - build
  - integration-tests
  - deploy
  - upload-static

variables:
  ALLURE_LAUNCH_NAME: "${CI_PROJECT_NAME} - ${CI_COMMIT_SHORT_SHA}"
  ALLURE_LAUNCH_TAGS: "regular, ${CI_COMMIT_REF_NAME}"
  ALLURE_TESTPLAN_PATH: testplan.json
  ALLURE_RESULTS: "target/allure-results/"

.job-test-template:
  image: registry.oskelly.tech/oskelly.ru/images/maven:3.8.5-jdk-8
  stage: test
  only:
    - merge_requests
    - master
  tags:
    - qa
  allow_failure: true
  services:
    - name: registry.oskelly.tech/oskelly.ru/images/rabbitmq:3-management-alpine
      alias: rabbitmq
    - name: registry.oskelly.tech/oskelly.ru/oskelly-test-database:latest
      alias: postgres
    - name: registry.oskelly.tech/oskelly.ru/images/localstack/localstack:3.4.0
      alias: localstack
    - name: registry.oskelly.tech/oskelly.ru/payments-service:latest
      alias: payments
  variables:
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: postgres
    RABBITMQ_DEFAULT_USER: oskelly
    RABBITMQ_DEFAULT_PASS: qwerty
    PAYMENTS_API_USERNAME: "payments"
    PAYMENTS_API_PASSWORD: "pay-pass"
  before_script:
    - 'apt-get update && apt-get install -y curl imagemagick-6.q16'
    - 'curl -sSL -o /usr/local/bin/allurectl https://github.com/allure-framework/allurectl/releases/latest/download/allurectl_linux_386'
    - 'chmod +x /usr/local/bin/allurectl'
  artifacts:
    reports:
      junit:
        - target/surefire-reports/TEST-*.xml
        - target/checkout/oskelly-auto-tests/target/surefire-reports/TEST-*.xml
  cache:
    key: m2-cache
    paths:
      - $CI_PROJECT_DIR/.m2/*
    policy: pull-push
  when: manual

testsuite-integration:
  extends: .job-test-template
  tags:
    - testops
  stage: integration-tests
  variables:
    GIT_STRATEGY: none
    SELENOID_URL: "http://testops.internal-dev.oskelly.tech:4444/wd/hub"
    DATABASELINK: *******************************************
    RABBITMQHOST: rabbitmq_int
    FEEDDATABASELINK: *******************************************
    MONOLITH_URL: http://monolith:8080
  services:
    - name: rabbitmq:3-management-alpine
      alias: rabbitmq_int
    - name: registry.oskelly.tech/oskelly.ru/oskelly-test-database
      alias: postgres_int
    - name: localstack/localstack
      alias: localstack_int
    - name: ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
      alias: monolith
      entrypoint: [ "java" , "-jar", "/build/oskelly-0.0.1-SNAPSHOT.jar" , "-Xmx4128m" ,"--spring.profiles.active=base,master,development,testing,debug", "-Dlogging.config=/build/logback.xml" ]
  before_script:
    - 'curl -sSL -o /usr/local/bin/allurectl https://github.com/allure-framework/allurectl/releases/latest/download/allurectl_linux_386'
    - 'chmod +x /usr/local/bin/allurectl'
  script:
    - timeout 300 bash -c 'while [[ "$(curl -s -o /dev/null -w ''%{http_code}'' http://monolith:8080)" != "200" ]]; do sleep 5; done' || false
    - 'export CLONE_REPO=oskelly.ru/oskelly-auto-tests'
    - 'mkdir -p /tmp/git/${CLONE_REPO}'
    - 'git clone --branch ${CI_COMMIT_BRANCH} ${CI_SERVER_PROTOCOL}://gitlab-ci-token:${CI_JOB_TOKEN}@${CI_SERVER_HOST}:${CI_SERVER_PORT}/${CLONE_REPO}.git /tmp/git/${CLONE_REPO} || (echo "download tests from master branch" && git clone --branch master ${CI_SERVER_PROTOCOL}://gitlab-ci-token:${CI_JOB_TOKEN}@${CI_SERVER_HOST}:${CI_SERVER_PORT}/${CLONE_REPO}.git /tmp/git/${CLONE_REPO})'
    - 'mkdir -p ${CI_PROJECT_DIR}/target/checkout/oskelly-auto-tests'
    - 'cp -aT /tmp/git/${CLONE_REPO} ${CI_PROJECT_DIR}/target/checkout/oskelly-auto-tests'
    - 'cd ${CI_PROJECT_DIR}/target/checkout/oskelly-auto-tests'
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn test -Dlogback.configurationFile=logback.xml -Dselenide.remote=${SELENOID_URL} -Dbase.stand=http://monolith:8080/ -Doskelly.monolith.db.url=******************************************* -Dstand.config.locale=ru_RU -Dstand.config.type=ru "-Dgroups=ru | !int"'

testsuite-00-0:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.build.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-00-1:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.build_01.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-01-1:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite1_1.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-01-2:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite1_2.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-01-3:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite1_3.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-02:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite2.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-02-2:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite2_2.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-03:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite3.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-04:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite4.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-05:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite5.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-06-1:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite6_1.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-06-2:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite6_2.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-06-3:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite6_3.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'


testsuite-07:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite7.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-08:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite8.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

testsuite-09:
  extends: .job-test-template
  script:
    - 'allurectl job-run plan --output-file testplan.json'
    - 'allurectl watch --silent -- mvn -B test --file pom.xml -s ci_settings.xml -Dtest="ru.oskelly.tests.pr.suite9.**" -DDATABASELINK="***************************************" -DRABBITMQHOST="rabbitmq" -DFEEDDATABASELINK="***************************************"'

build-jar:
  stage: build
  image: registry.oskelly.tech/oskelly.ru/images/maven:3.8.5-jdk-8
  before_script:
    - 'apt-get update && apt-get install -y curl'
  script:
    - 'mvn -B clean package --file pom.xml -DskipTests -s ci_settings.xml'
  artifacts:
    paths:
      - target/oskelly-0.0.1-SNAPSHOT.jar
    expire_in: 2h
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: never
    - if: $CI_COMMIT_TAG =~ /^release-.+$/ || $CI_COMMIT_TAG =~ /^stage-.+$/ || $CI_COMMIT_TAG =~ /^beta-.+$/      
    - if: $CI_COMMIT_BRANCH != "master" 
  cache:
    key: m2-cache
    paths:
      - $CI_PROJECT_DIR/.m2/*
    policy: pull-push
  

.copy-to-prod: &copy-to-prod
  stage: deploy
  image: registry.oskelly.tech/oskelly.ru/images/maven:3.8.5-jdk-8
  allow_failure: true
  only:
    - master
  before_script:
    - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
  artifacts:
    paths:
      - target/oskelly-0.0.1-SNAPSHOT.jar
    expire_in: 2h
  when: manual
  needs: ["build-jar"]

copy-to-prod-yandex:
  <<: *copy-to-prod
  script: # upload to path like "/home/<USER>/builds/master/51-4d9e23f8-2021-11-25T09-24-23Z"
    - ssh-keyscan *********** >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - TARGETPATH=$CI_COMMIT_REF_NAME/$CI_PIPELINE_IID-$CI_COMMIT_SHORT_SHA-${CI_JOB_STARTED_AT//:/-}
    - ssh gitlab-client@*********** mkdir -p /home/<USER>/builds/$TARGETPATH
    - scp target/oskelly-0.0.1-SNAPSHOT.jar gitlab-client@***********:/home/<USER>/builds/$TARGETPATH
  tags:
    - yandex
  only:
    - /^release-.+$/

include:
  - project: 'oskelly.ru/devops/oskelly-gitlab-devops'
    ref: master
    file: 'app_templates/oskelly-ext-dev.yaml'
