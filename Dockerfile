FROM reg.oskelly.tech/dockerhub-mirror/maven:3.8.5-openjdk-8-slim

WORKDIR /build
RUN mkdir -p /build/configs \
    && apt-get update \
    && apt-get install -y imagemagick-6.q16 curl
RUN mkdir -p /build/maxmind
ADD target/oskelly-0.0.1-SNAPSHOT.jar /build/oskelly-0.0.1-SNAPSHOT.jar
ADD logback.xml /build/logback.xml
ADD test-key.p12 /build/test-key.p12
ADD src/main/resources/application-base.yaml /build/configs/application-base.yaml
ADD src/main/resources/application-master.yaml /build/configs/application-master.yaml
ADD src/main/resources/maxmind/GeoLite2-Country.mmdb /build/maxmind/geo.mmdb

EXPOSE 8080
ENTRYPOINT ["java" , "-jar", "/build/oskelly-0.0.1-SNAPSHOT.jar" , "-Xmx4128m" ,"--spring.profiles.active=base,master,stand", "--spring.config.location=/build/configs/", "-Dlogging.config=/build/logback.xml"]
