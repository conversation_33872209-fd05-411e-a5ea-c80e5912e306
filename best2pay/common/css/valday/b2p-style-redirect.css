html,
body {
    height: 100%;
}

.outer {
}

.wrapper {
    width: 100%;
}

.info-field {
    text-align: left;
    min-height: 54px;
    width: 272px;
    border-bottom: 1px solid #eceff1;
}

#order-info {
    margin-top: 25px;
}

.info-field label {
    font-size: 12px;
    line-height: 12px;
    letter-spacing: -0.1px;
    color: #7f96a2;
    margin: 12px 0 4px 0;
}

.info-field p {
    font-size: 16px;
    letter-spacing: -0.1px;
    color: #263238;
}

#order-amount {
    height: 30px;
    margin-top: 24px;
    margin-bottom: 12px;
}

#order-amount span.table-cell-middle:first-child {
    padding-right: 9px;
}

/*--SECURE ICON--*/
.secure {
    padding: 12px 0 5px 0;
}

/*--END SECURE ICON--*/

/*--FEEDBACK--*/
#feedback-wrapper {
    height: auto;
}

#feedback-form {
    display: flex;
    align-items: center;
}

#feedback-form > div {
    width: 100%;
}

/*--<PERSON><PERSON> FEEDBACK--*/

/*----ME<PERSON>AGE SENDING STATUS----*/

.message-sent-result div {
    margin-top: 16px;
    text-align: center;
}

.message-sent-result p {
    font-size: 12px;
    margin-top: 8px;
}

/*----END MESSAGE SENDING STATUS----*/

/* iPhone portrait and landscape 2G-4S, 5-5S, 6S */
@media (min-device-width: 320px) and (max-device-width: 767px) {

    /*-- HEADER--*/
    .header {
        height: 48px;
        padding: 24px 24px 0 24px;
        margin-bottom: -48px;
    }

    .header-desc {
        height: 13px;
        font-size: 11px;
        text-align: right;
    }

    #breadcrumbs {
        display: none;
    }

    /*-- END HEADER--*/
    .wrapper {
        height: 100%;
    }

    #content {
        padding: 48px 0 36px 0;
    }

    button[type="submit"] {
        width: 100%;
    }

    /*-- FOOTER --*/
    #footer {
        height: 24px;
        margin-top: -32px;
        line-height: 24px;
    }

    #footer .footer-links a {
        font-size: 9px;
    }

    #footer .footer-icons img:nth-child(1) {
        margin-left: 16px;
    }

    #footer .footer-icons img {
        margin-left: 8px;
        width: 24px;
        height: 24px;
    }

    #footer .footer-links {
        margin-right: 16px;
    }

    #footer .footer-links img {
        width: 16px;
        height: 16px;
    }

    /*-- END FOOTER --*/
    /*--FEEDBACK--*/
    #feedback {
        padding: 28px 28px 0 28px;
    }

    #feedback-wrapper {
        padding: 0;
    }

    /*--END FEEDBACK--*/
    /*----MESSAGE SENDING STATUS----*/
    .message-sent-result {
        padding: 125px 78px 0 78px;
    }

    .message-sent-result img {
        height: 32px;
        width: 32px;
        float: left;
    }

    .message-sent-result div {
        float: left;
        margin: 5px 0 0 12px;
        width: 160px;
        text-align: left;
    }

    .message-sent-result h3 {
        font-size: 15px;
        font-weight: 500;
        letter-spacing: 0.3px;
        text-transform: none;
    }

    /*----END MESSAGE SENDING STATUS----*/
    /*-- CARD --*/
    .cardBack {
        background: #eceff1;
        padding-bottom: 16px;
        margin-top: 24px;
    }

    .card {
        width: auto;
        margin: 0 28px;
    }

    #amount-n-buttons {
        padding: 0 28px;
    }

    button[type=submit] {
        height: 48px;
        line-height: 48px;
        font-size: 18px;
    }

    /*-- END CARD --*/
}

/* iPhone portrait */
@media (min-device-width: 320px) and (max-device-width: 767px) and (max-aspect-ratio: 13/9) {

}

/* iPhone landscape */
@media (min-device-width: 320px) and (max-device-width: 767px) and (min-aspect-ratio: 13/9) {

}

/* Desktop and tablets */
@media (min-device-width: 768px) {
    .vertical-align-center > * {
        margin: auto;
    }

    .outer {
        display: flex;
        align-items: center;
    }

    .wrapper {
        width: 960px;
        max-width: 960px;
        height: 600px;
        padding: 0 144px 0 144px;
        border: 1px solid #dae2e6;
    }

    /*-- HEADER--*/
    .header {
        height: 140px;
        padding: 0;
        margin-bottom: -140px;
    }

    .header-logo {
        height: 70px;
        padding-top: 25px;
        padding-bottom: 25px;
    }

    .header-logo img {
        max-height: 45px;
    }

    .header-desc {
        font-size: 20px;
        height: 24px;
        line-height: 24px;
    }

    #breadcrumbs {
        margin-top: 16px;
        height: 16px;
        line-height: 14px;
        color: #90a4ae;
        font-size: 14px;
    }

    #breadcrumbs span {
        margin: 0 12px 0 12px;
        display: inline-block;
        height: 16px;
    }

    #breadcrumbs span:first-child {
        margin-left: 0;
    }

    #breadcrumbs span:last-child {
        margin-right: 0;
    }

    #breadcrumbs span.current {
        color: #00bcd4;
    }

    #breadcrumbs img.arrow {
        margin: 0;
        display: inline-block;
        width: 16px;
        height: 16px;
    }

    /*--END HEADER--*/
    #content {
        padding: 140px 0 63px 0;
    }

    /*--FOOTER--*/
    #footer {
        height: 63px;
        margin-top: -63px;
        line-height: 63px;
        /*border-top: 1px solid #eceff1;*/
    }

    #footer .footer-links a {
        font-size: 13px;
    }

    #footer .footer-icons img {
        margin-right: 8px;
        width: 32px;
        height: 32px;
    }

    #footer .footer-links img {
        width: 24px;
        height: 24px;
    }

    /*--END FOOTER--*/
    /*----MESSAGE SENDING STATUS----*/
    .message-sent-result {
        padding: 125px 140px 0 140px;
    }

    .message-sent-result img {
        height: 80px;
        width: 80px;
    }

    .message-sent-result div {
        margin-top: 16px;
        text-align: center;
    }

    .message-sent-result h3 {
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 1.8px;
        text-transform: uppercase;
    }

    /*----END MESSAGE SENDING STATUS----*/

}