@font-face {
    font-family: 'currency-b2p-3';
    src: url('../../fonts/icomoon/currency-b2p-3.eot?xz4dj4');
    src: url('../../fonts/icomoon/currency-b2p-3.eot?xz4dj4#iefix') format('embedded-opentype'),
    url('../../fonts/icomoon/currency-b2p-3.ttf?xz4dj4') format('truetype'),
    url('../../fonts/icomoon/currency-b2p-3.woff?xz4dj4') format('woff'),
    url('../../fonts/icomoon/currency-b2p-3.svg?xz4dj4#currency-b2p-3') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'currency-b2p-3' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-dollar:before {
    content: "\e900";
}

.icon-euro:before {
    content: "\e901";
}

.icon-rouble:before {
    content: "\e902";
}
