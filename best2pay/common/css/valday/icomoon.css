@font-face {
    font-family: 'currency-b2p';
    src: url('../../fonts/icomoon/currency-b2p.eot?aecpz9');
    src: url('../../fonts/icomoon/currency-b2p.eot?aecpz9#iefix') format('embedded-opentype'),
    url('../../fonts/icomoon/currency-b2p.ttf?aecpz9') format('truetype'),
    url('../../fonts/icomoon/currency-b2p.woff?aecpz9') format('woff'),
    url('../../fonts/icomoon/currency-b2p.svg?aecpz9#currency-b2p') format('svg');
    font-weight: normal;
    font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'currency-b2p' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-dollar:before {
    content: "\e900";
}

.icon-euro:before {
    content: "\e901";
}

.icon-rouble:before {
    content: "\23f4";
}
