#content-data {
    display: flex;
    align-items: center;
}

#content-data > div {
    height: 100%;
}

.operation-result-title h2 {
    letter-spacing: 1.7px;
    color: #263238;
    width: 210px;
}

#operation-details {
    padding: 0 25px;
    align-items: center;
}

#operation-details .operation-detail {
    text-align: left;
}

button.notify-button {
    margin: 8px 0 0 0;
}

#notify {
    height: 100%;
}

/* iPhone portrait and landscape 2G-4S, 5-5S, 6S */
@media (min-device-width: 320px) and (max-device-width: 767px) {

    #content-data {
        padding: 0 3px; /*underlying class=row has 25 padding while we need 28*/
    }

    #operation-details {
        padding-top: 112px;
    }

    .operation-result-title {
        margin-bottom: 52px;
    }

    .operation-result-title img {
        margin-bottom: 12px;
    }

    .operation-result-title h2 {
        font-size: 13px;
        text-align: center;
        margin: auto;
    }

    button.notify-button {
        width: 100%;
        height: 40px;
        line-height: 40px;
        font-size: 12px;
    }

    #operation-details .operation-detail {
        height: 29px;
        line-height: 29px;
        font-size: 11px;
        letter-spacing: -0.1px;
    }

    .email-img {
        width: 48px;
        height: 48px;
        margin-bottom: 17px;
    }

    .content-wrapper {
        padding: 0 28px;
    }

    #notify-form {
        margin-top: 124px;
    }

    .email-title {
        height: 26px;
        font-size: 11px;
        line-height: 14px;
        font-weight: 500;
        letter-spacing: 1.2px;
        text-align: center;
        color: #263238;
        margin-bottom: 27px;
        text-transform: uppercase;
    }

    #send-email-button {
        width: 256px;
        margin: 16px 0 0 0;
        color: #fff;
        background: #00bcd4;
    }
}

/* iPhone portrait */
@media (min-device-width: 320px) and (max-device-width: 767px) and (max-aspect-ratio: 13/9) {

}

/* iPhone landscape */
@media (min-device-width: 320px) and (max-device-width: 767px) and (min-aspect-ratio: 13/9) {

}

/* Desktop and tablets */
@media (min-device-width: 768px) {

    .operation-result-title img {
        float: left;
        margin-right: 16px;
    }

    .operation-result-title h2 {
        float: left;
        text-align: left;
        font-size: 16px;
        margin: 5px 0 0 0;
    }

    #operation-details {
        display: flex;
    }

    button.notify-button {
        width: 232px;
        margin: 8px 0 0 0;
    }

    #operation-details .operation-detail {
        height: 42px;
        line-height: 42px;
        font-size: 14px;
        text-align: left;
    }
}