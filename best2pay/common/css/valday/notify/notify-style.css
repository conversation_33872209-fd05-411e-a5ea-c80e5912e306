.operation-result-title {
    padding-bottom: 40px;
}

.operation-result-title.operation-result-title--padding-bottom-10 {
    padding-bottom: 10px;
}

.operation-result-title img {
    margin-bottom: 12px;
}

.operation-result-title h2 {
    font-size: 11px;
    height: 26px;
    margin: 0;
}

.operation-result-title p {
    margin: 0;
    font-weight: 500;
    color: #263238;
    text-align: center;
}

#operation-details {
    padding: 78px 44px 0 44px;
}

#operation-details .operation-detail {
    height: 29px;
    line-height: 29px;
    font-size: 11px;
    letter-spacing: -0.1px;
}

button#send-email-button {
    width: 144px;
    height: 40px;
    border-radius: 4px;
    background-color: #00bcd4;
    font-size: 14px;
    letter-spacing: 1.2px;
    color: #ffffff;
    margin: 0;
}

#notify {
    height: 100%;
}

#notify-wrapper {
    padding-top: 80px;
}

.message-sent-result {
    padding-top: 165px;
}

#notify-form {
}

#notify-form .input-field {
    margin-bottom: 16px;
}

.email-img {
    width: 45px;
    height: 45px;
    margin-bottom: 17px;
}
.email-title {
    height: 26px;
    font-size: 11px;
    line-height: 14px;
    font-weight: 500;
    letter-spacing: 1.2px;
    text-align: center;
    color: #263238;
    margin-bottom: 27px;
    text-transform: uppercase;
}

.ordinal-form#notify input {
    width: 100%;
    height: 28px;
    border: solid 1px rgba(33, 43, 47, 0.15);
    padding: 7px 8px 6px 8px;
    margin-bottom: 16px;
    text-transform: none;
}
input:focus {
    color: #455a64;
}

button.notify-button {
    width: 256px;
    margin: 16px 0 0 0;
}

/* iPhone portrait and landscape 2G-4S, 5-5S, 6S */
@media (min-device-width: 320px) and (max-device-width: 767px) {

}

/* iPhone portrait */
@media (min-device-width: 320px) and (max-device-width: 767px) and (max-aspect-ratio: 13/9) {

}

/* iPhone landscape */
@media (min-device-width: 320px) and (max-device-width: 767px) and (min-aspect-ratio: 13/9) {

}

/* Desktop and tablets */
@media (min-device-width: 768px) {

}