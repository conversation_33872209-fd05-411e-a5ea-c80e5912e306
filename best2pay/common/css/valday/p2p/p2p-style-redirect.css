/* iPhone portrait and landscape 2G-4S, 5-5S, 6S */
@media (min-device-width: 320px) and (max-device-width: 767px) {

    .cardBack {
        background: #eceff1;
        padding-bottom: 16px;
        margin-top: 24px;
    }

    .card1 {
        width: auto;
        margin: 0 28px;
    }

    #amount-n-buttons {
        padding: 0 28px;
    }

    button[type=submit] {
        height: 48px;
        line-height: 48px;
        font-size: 18px;
    }
}

/* Desktop and tablets */
@media (min-device-width: 768px) {
    .wrapper {
        height: 662px;
    }

    #amount-n-buttons {
        width: 344px;
        margin: auto;
    }

    #amount-n-buttons input {
        height: 56px;
        color: #364448;
        border-radius: 2px;
        border: solid 1px #90a4ae;
    }
}