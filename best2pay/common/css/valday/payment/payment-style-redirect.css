/* iPhone portrait and landscape 2G-4S, 5-5S, 6S */
@media (min-device-width: 320px) and (max-device-width: 767px) {

}

/* iPhone portrait */
@media (min-device-width: 320px) and (max-device-width: 767px) and (max-aspect-ratio: 13/9) {

}

/* iPhone landscape */
@media (min-device-width: 320px) and (max-device-width: 767px) and (min-aspect-ratio: 13/9) {

}

/* Desktop and tablets */
@media (min-device-width: 768px) {

    #amount-n-buttons {
        height: 130px;
    }

    #order-amount {
        margin-top: 10px;
    }
}
@media (min-device-width: 767px) {
    #apple-pay-mobile {
        display: none;
    }

    #apple-pay-desktop {
        display: none;
    }
}
@media (max-device-width: 767px) and (min-device-width: 320px) {
    #apple-pay-desktop {
        display: none;
    }

    #apple-pay-mobile {
        display: none;
        text-align: center;
        margin: 20px;
    }
    .apple-pay-button {
        width: 100%;
        max-width: 100%;
        height: 40px;
    }
}
/*Стили для Apple Pay*/
.apple-pay {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

.apple-pay-button {
    -webkit-appearance: -apple-pay-button;
    -apple-pay-button-type: buy;
    visibility: hidden;
    display: inline-block;
    max-width: 200px;
    min-height: 30px;
    border: 1px solid black;
    background-image: -webkit-named-image(apple-pay-logo-black);
    background-size: 100% calc(60% + 2px);
    background-repeat: no-repeat;
    background-color: white;
    background-position: 50% 50%;
    border-radius: 5px;
    padding: 0;
    margin: 5px auto;
    transition: background-color .15s;
}

.apple-pay-button.visible {
    visibility: visible;
}

.apple-pay-button:active {
    background-color: rgb(152, 152, 152);
}

.appleTitle {
    font-weight: bold;
}

.check-amount-apple {
    font-size: 32px;
    font-weight: normal;
    line-height: 1.5;
    margin-bottom: 6px;
}

.apple-red {
    color: red;
}

#top-part {
    width: 320px;
    margin: auto;
    position: relative;
}

.apple-green {
    color: green;
}

.amount-left {
    float: left;
}

.apple-pay-right {
    margin-top: 8px;
}

#full-description {
    position: absolute;
    display: none;
    padding: 5px;
    margin: -10px 0 0 -15px;
    background: #fff;
    border: 1px solid #dae2e6;
}