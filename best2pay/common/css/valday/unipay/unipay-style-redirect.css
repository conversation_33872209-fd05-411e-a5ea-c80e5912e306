#pageForm > .content-wrapper {
    padding: 0 140px;
    margin: auto;
}

#pageForm > .content-wrapper > div {
    width: 100%;
}

.ordinal-form .input-field {
    width: 100%;
}

/* iPhone portrait and landscape 2G-4S, 5-5S, 6S */
@media (min-device-width: 320px) and (max-device-width: 767px) {
    #content {
        padding-top: 122px;
    }

    #pageForm > .content-wrapper {
        padding: 0 28px;
    }
}

/* Desktop and tablets */
@media (min-device-width: 768px) {
    #pageForm > .content-wrapper {
        display: flex;
        align-items: center;
    }
}