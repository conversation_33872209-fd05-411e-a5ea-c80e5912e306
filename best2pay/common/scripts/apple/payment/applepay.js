var sector;
var amount;
var fee;
var amountLong;
var feeLong;
var currency;
var resultType;
var preauth;
var description;
var reference;
var language;
var id;

$(document).ready(function () {
    sector = Number($("[name=sector]").val());
    id = Number($("[name=id]").val());
    amount = Number($("[name=amountApple]").val());
    fee = Number($("[name=feeApple]").val());
    amountLong = Number($("[name=amount]").val());
    feeLong = Number($("[name=fee]").val());
    currency = 'RUB';
    resultType = 'Json';
    preauth = $("[name=preauth]").val();
    description = 'Test Apple Pay';
    reference = 'N67589';
    var browser_language = $("[name=browser_language]").val();
    if (browser_language == 'ru-RU') {
        language = "RUS";
    } else {
        language = "ENG";
    }
});


function getApplePaySession(url, session) {
    return new Promise(function (resolve, reject) {
        $.ajax({
            url: 'https://test.best2pay.net/webapi/GetApplePaySession',
            type: "POST",
            data: {
                url: url,
                sector: sector
            },
            dataType: 'xml',
            success: function (xml) {
                var tokenBody;
                var result = false;
                $(xml).find('token').each(function () {
                    result = true;
                    tokenBody = $(this).find('tokenBody').text();
                });
                if (result) {
                    resolve(JSON.parse(tokenBody));
                } else {
                    session.abort();
                }
            },
            error: function () {
                session.abort();
            }
        });
    });
}

document.addEventListener('DOMContentLoaded', () => {
    if (window.ApplePaySession
    ) {
        if (ApplePaySession.canMakePayments) {
            if (showAppleButton) {
                showApplePayButton();
            }
        }
    }
})
;

function showApplePayButton() {
    HTMLCollection.prototype[Symbol.iterator] = Array.prototype[Symbol.iterator];
    const buttons = document.getElementsByClassName("apple-pay-button");
    for (let button of buttons) {
        button.className += " visible";
    }
    $('#container-amount').attr('class', 'amount-left');

    var ua = navigator.userAgent;
    if (ua.match(/iPhone/i) || ua.match(/iPad/i) || ua.match(/iPod/i) || ua.match(/Android/i) || ua.match(/BlackBerry/i) || ua.match(/symbianos/i) || ua.match(/symbian/i) || ua.match(/samsung/i) || ua.match(/nokia/i) || ua.match(/windows ce/i) || ua.match(/sonyericsson/i) || ua.match(/webos/i) || ua.match(/wap/i) || ua.match(/motor/i)) {
        document.getElementById('apple-pay-mobile').style.display = 'block';
    } else {
        document.getElementById('apple-pay-desktop').style.display = 'block';
    }
}

function applePayButtonClicked() {
    const paymentRequest = {
        // requiredShippingContactFields: ['email', 'phone'],
        requiredShippingContactFields: [],
        countryCode: 'RU',
        currencyCode: currency,
        supportedNetworks: ['visa', 'masterCard'],
        merchantCapabilities: ['supports3DS'],
        lineItems: [
            {
                type: 'final',
                label: 'Amount',
                amount: amount
            },
            {
                type: 'final',
                label: 'Fee',
                amount: fee
            },
        ],
        total: {
            label: 'Total',
            amount: amount + fee
        }
    };


    const session = new ApplePaySession(1, paymentRequest);

    session.onvalidatemerchant = (event) => {
        getApplePaySession(event.validationURL, session).then(function (response) {
            session.completeMerchantValidation(response);
        });
    };

    session.onpaymentauthorized = (event) => {

        var paymentToken = JSON.stringify(event.payment.token);
        var shippingContact = JSON.stringify(event.payment.shippingContact);

        var status;
        $.ajax({
            url: 'https://test.best2pay.net/webapi/MakeApplePay',
            type: "POST",
            data: {
                sector: sector,
                preauth: preauth,
                amount: amountLong,
                id: id,
                paymentToken: paymentToken,
                shippingContact: shippingContact,
                resultType: resultType,
                description: description,
                fee: feeLong,
                reference: reference,
                language: language
            },
            dataType: 'json',
            success: function (data) {
                if (data.hasOwnProperty('operation')) {
                    var state = data.operation.state;
                    var approved = (state == 'APPROVED');
                    if (approved) {
                        status = ApplePaySession.STATUS_SUCCESS;
                    } else {
                        status = ApplePaySession.STATUS_FAILURE;
                    }
                    session.completePayment(status);
                    showOperationInfo(data.operation);
                } else if (data.hasOwnProperty('error')) {
                    status = ApplePaySession.STATUS_FAILURE;
                    session.completePayment(status);
                    showError();
                }
            },
            error: function (num) {
                status = ApplePaySession.STATUS_FAILURE;
                session.completePayment(status);
                showError();
            }
        });
    };
    session.begin();
}


function showOperationInfo(operation) {
    var pan = $('#cardFrom');
    pan.removeAttr('required');
    $('#cardFrom').rules('add', {
        validatePan: false,
        blackBins: false,
        ownBins: false,
        sectorBinsList1: false
    });
    var cardDate = $('#cardDate');
    cardDate.removeAttr('required');
    var cvc = $('#cvc');
    cvc.removeAttr('required');
    $('#pageForm').attr('action', '/webapi/Notify?apple=true&operation_id=' + operation.id);
    $('#pageForm').submit();
}

function showError() {
    alert('Error');
}