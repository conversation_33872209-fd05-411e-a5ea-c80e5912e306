/**
 * Internationalization core
 * (C) Best2Pay LTD. 2018
 */
function lang() {
    if (language != undefined && typeof(language) != typeof(undefined)) {
        return language;
    } else {
        return 'RUSSIAN';
    }
}

function i18nMessage(key) {
    return messages[lang()][key];
}

function i18nMessage(key, defaultString) {
    var msg = messages[lang()][key];
    return msg != undefined && typeof(msg) != typeof(undefined) ? msg : defaultString;
}

var messages = [];
messages['RUSSIAN'] = [];
messages['ENGLISH'] = [];

messages['RUSSIAN']['requiredField'] = 'Поле обязательно';
messages['RUSSI<PERSON>']['missingNumber'] = 'Неверное число';
messages['RUSSIAN']['amountMustBeFrom'] = 'Сумма должна быть от ';
messages['RUSSIAN']['till'] = ' до ';
messages['RUSSIAN']['incorrectAmount'] = 'Сумма введена неверно';
messages['RUSSIAN']['incorrectEmail'] = 'Неверный email';
messages['RUSSIAN']['incorrectSecurityCode'] = 'Неверный код безопасности';
messages['RUSSIAN']['incorrectPan'] = 'Неверный номер карты';
messages['RUSSIAN']['cardProhibited'] = 'Операция с использованием указанных карточных реквизитов запрещена';
messages['RUSSIAN']['cardDoesntMatchSelected'] = 'Указанная карта не соответствует категории карты, выбранной на первой странице';
messages['RUSSIAN']['requestErrorTryLater'] = 'При выполнении запроса возникла ошибка. Пожалуйста, повторите операцию позже.';
messages['RUSSIAN']['phoneFormatXXX'] = 'Телефон должен быть в формате XXX0000000';
messages['RUSSIAN']['phoneFormat8XXX'] = 'Телефон должен быть в формате 8XXX0000000';
messages['RUSSIAN'][''] = '';

messages['ENGLISH']['requiredField'] = 'Required field';
messages['ENGLISH']['missingNumber'] = 'Incorrect number';
messages['ENGLISH']['amountMustBeFrom'] = 'Amount must be from ';
messages['ENGLISH']['till'] = ' till ';
messages['ENGLISH']['incorrectAmount'] = 'Incorrect amount';
messages['ENGLISH']['incorrectEmail'] = 'Incorrect email';
messages['ENGLISH']['incorrectSecurityCode'] = 'Incorrect security code';
messages['ENGLISH']['incorrectPan'] = 'Incorrect card number';
messages['ENGLISH']['cardProhibited'] = 'Operation with given card is not permitted';
messages['ENGLISH']['cardDoesntMatchSelected'] = 'Given card does not match category selected at first page';
messages['ENGLISH']['requestErrorTryLater'] = 'There was an error during request. Please, try again later.';
messages['ENGLISH']['phoneFormatXXX'] = 'Phone must be in XXX0000000 format';
messages['ENGLISH']['phoneFormat8XXX'] = 'Phone must be in 8XXX0000000 format';
messages['ENGLISH'][''] = '';

var translit = [];
translit['RU'] = [];
translit['RU']['EN'] = [];

translit['RU']['EN']['А'] = 'A';
translit['RU']['EN']['Б'] = 'B';
translit['RU']['EN']['В'] = 'V';
translit['RU']['EN']['Г'] = 'G';
translit['RU']['EN']['Д'] = 'D';
translit['RU']['EN']['Е'] = 'E';
translit['RU']['EN']['Ё'] = 'Yo';
translit['RU']['EN']['Ж'] = 'Zh';
translit['RU']['EN']['З'] = 'Z';
translit['RU']['EN']['И'] = 'I';
translit['RU']['EN']['Й'] = 'J';
translit['RU']['EN']['К'] = 'K';
translit['RU']['EN']['Л'] = 'L';
translit['RU']['EN']['М'] = 'M';
translit['RU']['EN']['Н'] = 'N';
translit['RU']['EN']['О'] = 'O';
translit['RU']['EN']['П'] = 'P';
translit['RU']['EN']['Р'] = 'R';
translit['RU']['EN']['С'] = 'S';
translit['RU']['EN']['Т'] = 'T';
translit['RU']['EN']['У'] = 'u';
translit['RU']['EN']['Ф'] = 'F';
translit['RU']['EN']['Х'] = 'H';
translit['RU']['EN']['Ц'] = 'Ts';
translit['RU']['EN']['Ч'] = 'Ch';
translit['RU']['EN']['Ш'] = 'Sh';
translit['RU']['EN']['Щ'] = 'Sch';
translit['RU']['EN']['Ь'] = '';
translit['RU']['EN']['Ъ'] = '';
translit['RU']['EN']['Ы'] = 'Y';
translit['RU']['EN']['Э'] = 'E';
translit['RU']['EN']['Ю'] = 'Yu';
translit['RU']['EN']['Я'] = 'Ya';