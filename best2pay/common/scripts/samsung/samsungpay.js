$(document).ready(function () {

    if (SamsungPay.checkMobileAndRunnable()) {
        $('.samsungpay-button').show();
    } else {
        $('.samsungpay-button').hide();
    }

    $('.samsungpay-button').on('click', function () {

        samsungPayData.id = $('input[name=id]').val();
        samsungPayData.sector = $('input[name=sector]').val();

        $.ajax({
            url: '/webapi/SamsungPay/transactionPost',
            data: samsungPayData,
            dataType: 'json',
            method: 'post',
            success: function (data) {
                console.log(data);
                if (data.success == false) {
                    console.log(data.error.message);
                    alert('Ошибка оплаты с использованием Samsung Pay');
                    return false;
                }
                SamsungPay.connect(data.data.id, data.data.href, samsungServiceId,
                    serverUrl + '/webapi/SamsungPay/Return?id=' + samsungPayData.id + '&sector=' + samsungPayData.sector,
                    serverUrl + '/webapi/SamsungPay/Cancel?id=' + samsungPayData.id + '&sector=' + samsungPayData.sector + '&preauth=' + preauth + '&urlReferer=' + urlReferer,
                    'ru', data.data.encInfo.mod, data.data.encInfo.exp, data.data.encInfo.keyId);
            },
            error: function (data) {
                console.log(data);
            }
        })
        return false;
    })
})