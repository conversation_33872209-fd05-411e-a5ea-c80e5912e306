var missingNumber = 'Неверное число';

$(document).ready(function () {
    $.extend($.validator.messages, {
        required: i18nMessage('requiredField', 'Поле обязательно'),
        number: i18nMessage('missingNumber', 'Неверное число')
    });

    $.validator.addMethod("phoneCustom", function (value, element) {
        return /^\d{10}$/.test($(element).val());
    }, i18nMessage("phoneFormatXXX", "Телефон должен быть в формате XXX0000000"));
    $.validator.addMethod("limits", function (value, element) {
        var summAll = parseFloat($('#amountControl').val().replace(",", "."));
        return limits.min == undefined || limits.max == undefined || (summAll >= limits.min / 100 && summAll <= limits.max / 100);
    }, i18nMessage("amountMustBeFrom", "Сумма должна быть от ") + getFloat(limits.min / 100) + i18nMessage("till", " до ") + getFloat(limits.max / 100) + cur);
    $.validator.addMethod("amount", function (value, element) {
        return /^\d*([,.]{1}\d{1,2}){0,1}$/.test(value);
    }, i18nMessage("incorrectAmount", "Сумма введена неверно"));

    $('#payForm').validate({
        errorElement: "span",
        errorClass: 'has-error',
        errorPlacement: function (error, element) {
            if (!element.is('.selectized')) {
                element.tooltip('destroy');
                element.tooltip({
                    title: error.text(),
                    placement: 'bottom',
                    trigger: 'manual'
                }).tooltip('show');
            }
        },
        showErrors: function (errorMap, errorList) {
            this.defaultShowErrors();

            $('.selectize-control').removeClass('has-error');
            $('.selectize-control').tooltip('destroy');
            $.each(errorList, function (key, item) {
                var element = $(item.element);
                if (element.is('.selectized')) {
                    var selectized = element.next('.selectize-control');
                    selectized.addClass('has-error');
                    selectized.tooltip('destroy');
                    selectized.tooltip({
                        title: $(this)[0].message,
                        placement: 'bottom',
                        trigger: 'manual'
                    }).tooltip('show');
                }
            })
        },
        ignore: ':hidden:not([class~=selectized]),:hidden > .selectized, .selectize-control .selectize-input input',
        success: function (error, element) {
            $(element).tooltip('destroy');
        },
        rules: {
            amount: {
                number: true
            }
        },
        submitHandler: function (form) {
            if (customSubmitHandler !== undefined) {
                return customSubmitHandler(form);
            } else {
                return true;
            }
        }
    });
    $('#amountControl').rules('add', {
        limits: true,
        amount: true
    });

    if ($('#phone') != undefined && $('#phone').length > 0) {
        $('#phone').rules('add', {
            phoneCustom: true
        });
    }

    $('.ps').each(function () {
        var self = $(this);
        var amountKopEl = $('#amount_kop');
        var feeKopEl = $('#fee_kop');
        var amountControlEl = $('#amountControl');
        var commEl = $('#comm');
        var totEl = $('#tot');

        function paymentSystem() {
            if ($("#inlineRadio1").is(':checked')) {
                return 'VISA';
            } else if ($("#inlineRadio2").is(':checked')) {
                return 'WEBMONEY';
            } else if ($("#inlineRadio3").is(':checked')) {
                return 'YANDEXDENGI';
            } else if ($("#inlineRadio4").is(':checked')) {
                return 'QIWI';
            } else if ($("#inlineRadio5").is(':checked')) {
                return 'GOROD';
            } else if ($("#inlineRadio6").is(':checked')) {
                return 'MOBILE';
            }
        }

        function handler() {
            var amount = getFloat(amountControlEl.val().replace(',', '.'));
            var ps = paymentSystem();
            var ia = ps + '_IN_AMOUNT';
            var t = tax[ps];
            var a = trunc(amount.toFixed(2) * 100);
            var c;
            if (tax[ia] === true) {
                var a2 = a / (1 - t / 100.0);
                c = trunc(a2 - a);
                commEl.text(((c / 100.0).toFixed(2)) + cur);
                totEl.text((((a + c) / 100.0).toFixed(2)) + cur);
                feeKopEl.val(c);
            } else {
                c = trunc(a * (t / 100.0));
                commEl.text(((c / 100.0).toFixed(2)) + cur);
                totEl.text((((a + c) / 100.0).toFixed(2)) + cur);
                feeKopEl.val(c);
            }
            amountKopEl.val(a);
        }

        self.on('input', function () {
            handler();
        }).trigger('input');

        self.click(function () {
            handler();
        });

        if (self.is(':checked')) {
            handler();
        }
    });
});