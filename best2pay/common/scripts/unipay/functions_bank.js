var CREDIT_ACC = '42301810';
var CARD_ACC = '40817810';
var HAS_SERVICE_TAX = undefined;
var needsRecals = undefined;
/*Фиксированная комиссия в копейках*/
var FIXED_TAX = 0;

var FIXED_TAX_OLD = 0;
var VisaTaxOld = tax['VISA'];
var MasterTaxOld = tax['MASTERCARD'];
var MirTaxOld = tax['MIR'];
//дополнительная омиссия которая добавляется к основной
var clientFee;

function clearFee(){
	FIXED_TAX = 0;
	tax['VISA'] = '0.0';
	tax['MASTERCARD'] = '0.0';
	if(tax['MIR'] != undefined){
		tax['MIR'] = '0.0';
	}
}

function restoreFee(){
	FIXED_TAX = FIXED_TAX_OLD;
	tax['VISA'] = VisaTaxOld;
	tax['MASTERCARD'] = MasterTaxOld;
	if(tax['MIR'] != undefined){
		tax['MIR'] = MirTaxOld;
	}
}

function taxHandler(){
	/* Выводим комиссию только после нажатия кнопки "Рассчитать комиссию/Продолжить"*/
	if(HAS_SERVICE_TAX == true){
		$('#submitButton').val('Рассчитать');
		clearFee();
		needsRecals = true;
	}
}

$(document).ready(function () {

	var ownCard = $('#ownCard');

	$.extend($.validator.messages, {
		required: 'Поле обязательно',
		email: 'Неверный email',
		number: 'Неверное число'
	});

	$.validator.addMethod("emailCustom", function(value, element) {
		return $(element).val() == '' || isValidEmail($(element).val());
	}, "Неверный email");
	$.validator.addMethod("accountNumber", function(value, element) {
		return /^\d{20}$/.test($(element).val());
	}, "Номер счета должен состоять из 20 цифр");
	if(HAS_BIK){
		$.validator.addMethod("accountNumberCorrect", function(value, element) {
			return $(element).val().length == 20 && checkAccountCorrect($(element).val(), BIK);
		}, "Указан неправильный номер счета");
	}
	$.validator.addMethod("checkFIO", function(value, element) {
        return $(element).val() == '' || /^[А-ЯЁа-яё-]+[ ]+[А-ЯЁа-яё-]+([ А-ЯЁа-яё-]+)*$/.test($(element).val());
	}, "Укажите фамилию, имя и отчество полностью");
	$.validator.addMethod("limits", function (value, element) {
    	var summAll = parseFloat($('#amountControl').val().replace(",","."));
    	return limits.min == undefined || limits.max == undefined || (summAll >= limits.min/100 && summAll <= limits.max/100);
    }, "Сумма должна быть от " + getFloat(limits.min/100) + " до " + getFloat(limits.max/100) + cur);
    $.validator.addMethod("amount", function (value, element) {
        return /^\d*([,.]{1}\d{1,2}){0,1}$/.test(value);
    }, "Сумма введена неверно");

	$('#payForm').validate({
		errorElement: "span",
		errorClass: 'has-error',
		errorPlacement: function(error, element) {
			if(!element.is('.selectized') ) {
				element.tooltip('destroy');
				element.tooltip({title:error.text(),
					placement:'bottom',
					trigger:'manual'}).tooltip('show');
			}
		},
		showErrors: function(errorMap, errorList) {
			this.defaultShowErrors();

		    $('.selectize-control').removeClass('has-error');
		    $('.selectize-control').tooltip('destroy');
		    $.each(errorList, function(key, item){
		        var element = $(item.element);
		        if ( element.is('.selectized') ) {
		        	var selectized = element.next('.selectize-control');
		        	selectized.addClass('has-error');
		        	selectized.tooltip('destroy');
		        	selectized.tooltip({
		        		title:$(this)[0].message,
						placement:'bottom',
						trigger:'manual'
					}).tooltip('show');
		        }
		    })
		},
		ignore: ':hidden:not([class~=selectized]),:hidden > .selectized, .selectize-control .selectize-input input',
		success: function(error, element){
			$(element).tooltip('destroy');
		},
		rules: {
			acc_number: {
				accountNumber: true,
				accountNumberCorrect: true && HAS_BIK
			},
			email: {
				emailCustom: true
			},
			amount: {
				number: true
			},
			fio: {
				checkFIO: true
			}
		},
		submitHandler: function(form){
			if(customSubmitHandler !== undefined){
				return customSubmitHandler(form);
			} else {
				return true;
			}
		}
	});
	$('#amountControl').rules('add', {
        limits: true,
        amount: true
    });

	/*
	 * Обработчик переключателей "номер счета - номер договора"
	 */
	$('input.payment-type-control').each(function(){
		var self = $(this);
		var accountNumber = $('#accountNumberControl');

		function handler(control){
			accountNumber.removeAttr('disabled');
			if (control.val() == 'credit'){
				accountNumber.val(CREDIT_ACC);
			} else {
				accountNumber.val(CARD_ACC);
			}
		}

		self.click(function(){
			handler($(this));
		});

		if (self.is(':checked')){
			handler(self);
        }
    });

	ownCard.on('input', function(){
		$('.ps').trigger('input');
	});
	ownCard.on('click', function(){
		$('.ps').trigger('input');
	});

	$('.ps').each(function() {

		var self = $(this);
		var amountKopEl = $('#amount_kop');
		var feeKopEl = $('#fee_kop');
		var amountControlEl = $('#amountControl');
		var commEl = $('#totalCommission');
		var totEl = $('#totalAmount');

		function paymentSystem() {
			if ($("#cardTypeVisa").is(':checked')) {
				return 'VISA';
			} else
			if ($("#cardTypeMasterCard").is(':checked')) {
				return 'MASTERCARD';
			}
            if ($("#cardTypeMir").is(':checked')) {
                return 'MIR';
            }
		}

		function handler() {
            var amount = getFloat(amountControlEl.val().replace(',', '.'));
			var ps = paymentSystem();
			var ia = ps + '_IN_AMOUNT';
			var t = tax[ps];
			var a = trunc(amount.toFixed(2)*100);
			var c;
			if(ownCard != undefined && ownCard.is(':checked')){
				c = 0.0;
			} else {
				if (tax[ia] === true) {
					var a2 = a / (1 - t/100.0);
					c = trunc(a2 - a);
					if(c != 0 && c < FIXED_TAX){
						c = FIXED_TAX;
					}
				} else {
					c = trunc(a * (t / 100.0));
					if(c != 0 && c < FIXED_TAX){
						c = FIXED_TAX;
					}
				}
			}
            if (clientFee != undefined){
                c = c + clientFee;
            }
			if(isNaN(c)){
				commEl.text('--');
				totEl.text('--');
                feeKopEl.val('--');
			} else {
				commEl.text((c / 100.0).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1 '));
				totEl.text(((a + c) / 100.0).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1 '));
				feeKopEl.val(c);
			}
			amountKopEl.val(a);
		}

		self.on('input', function(){
			handler();
		}).trigger('input');

		self.click(function(){
			handler();
		});

		if (self.is(':checked')){
			handler();
        }
    });

	/*
	 * Обработчик заполнения поля Description
	 */
	$('input.desc-render').each(function(){

		function descHandler(){
			var accountNumberControlEl = $('#accountNumberControl');
			var contractControlEl = $('#contractControl');
			var totalAmountEl = $('#totalAmount');
			var commEl = $('#totalCommission');
			var fioEl = $('#fio');
			var descEl = $('#desc');
			var amountControl = $('#amountControl');
			var amountAddition = '';
			if((totalAmountEl === undefined || totalAmountEl.length == 0) && (commEl === undefined || commEl.length == 0) ){
				amountAddition = " в сумме "+amountControl.val() + cur;
			} else {
				amountAddition = " в сумме "+totalAmountEl.text()+ cur + " с комиссией "+commEl.text() + cur;
			}
			if(accountNumberControlEl != undefined && accountNumberControlEl.val() != undefined && accountNumberControlEl.val() != ''){
				descEl.val('Перевод денежных средств на счет N'+accountNumberControlEl.val()+'. Получатель '+fioEl.val()+ amountAddition);
			} else if(contractControlEl != undefined && contractControlEl.val() != undefined && contractControlEl.val() != ''){
				descEl.val('Перевод денежных средств по договору N'+contractControlEl.val()+'. Получатель '+fioEl.val()+ amountAddition);
        	}
		}

		var self = $(this);
		self.on('input', function(){
			descHandler();
		}).trigger('input');

		self.click(function(){
			descHandler();
		});

		if (self.is(':checked')){
			descHandler();
        }
    });

	/* Для прослушивания полей, от которых зависит размер комиссии - навешиваем на них особый слушатель*/
	$('.tax-dependency').on('input', function(){
		taxHandler();
	}).trigger('input');
});