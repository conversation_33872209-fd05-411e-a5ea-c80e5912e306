$(document).ready(function () {

	var payForm = $('#payForm');
	payForm.data('page', 1);
	var next = $('#next');
	var prev = $('#prev');
	var submit = $('#submitButton');
	var maxHeight = undefined;

	function calcBlank(tab){
		var contentHeight = tab.find('.tab-content').height();
		var blankTop = (maxHeight - contentHeight) / 2;
		tab.find('.blank-top').height(blankTop);
		tab.find('.blank-bottom').height(maxHeight - contentHeight - blankTop);
	}

	$('#bik').on('input', function(){
		var self = $(this);
		if(self.val().trim().length >= 9){
			$('#popup_loader').show();
			$.ajax({
                url: '/webapi/BikInfo',
                method: 'post',
                data: {
                	'bik': self.val()
				},
                dataType: 'json',
                success: function (data) {
                    $('#popup_loader').hide();
					if(data == undefined || data.success == undefined || data.data == undefined || data.success == false){
                        $('#bank-name').val('Н/д');
                        $('#bank-account').val('Н/д');
						alert('Указанный БИК не найден в справочнике');
						return;
					}
                    $('#bik').val(data.data.bik);
					$('#bank-name').val(data.data.name);
                    $('#bank-account').val(data.data.account);
                },
				error: function(data){
                    $('#popup_loader').hide();
                    $('#bik').val('');
                    $('#bank-name').val('');
                    $('#bank-account').val('');
                    alert('Ошибка получения информации о банке. Пожалуйста, повторите попытку позже.');
				}
            })
		}
    });

	next.on('click', function(){
		//TODO
		//maxHeight = Math.max(Math.max($('#tab-1').height(),$('#tab-2').height()), $('#tab-3').height());
		maxHeight = $('#tab-1').height();
		//Trick: jquery.validate валидирует только видимые части формы (баг или фича - хз)
		//ПРоверяем форму (а точнее - ее видимую часть) перед отрисовкой следующей страницы, и если есть
		//ошибки - то просто не переходим на следующую страницу. ПРи этом, переход на предыдущую страницу возможен.
		payForm.validate();
		if(!payForm.valid()){
			return false;
		}
		var page = $('#payForm').data('page');
		var nextPage = page + 1;
		$('#tab-'+page).hide();
		$('#tab-'+nextPage).height(maxHeight).show();
		calcBlank($('#tab-'+nextPage));
		if(nextPage > 1){
			prev.show();
		}
		if(nextPage == 3){
			next.hide();
			submit.show();
		}
		$('#payForm').data('page', nextPage)
    });

	prev.on('click', function(){
		var page = $('#payForm').data('page');
		var prevPage = page - 1;
		$('#tab-'+page).hide();
		$('#tab-'+prevPage).height(maxHeight).show();
		calcBlank($('#tab-'+prevPage));
		if(prevPage < 3){
			next.show();
			submit.hide();
		}
		if(prevPage == 1){
			prev.hide();
		}
		$('#payForm').data('page', prevPage)
    });

	$.extend($.validator.messages, {
		'maxlength' : 'Пожалуйста, введите не более {0} знаков.'
	});

	$.validator.addMethod("bik", function(value, element) {
		return /^\d{9}$/.test($(element).val());
	}, "БИК должен состоять из 9 цифр");
	$.validator.addMethod("accountNumber", function(value, element) {
		return /^\d{20}$/.test($(element).val());
	}, "Номер счета должен состоять из 20 цифр");
	$.validator.addMethod("checkFIO", function(value, element) {
		return $(element).val() == '' || /^[А-ЯЁа-яё-]+[ ]+[А-ЯЁа-яё-]+([ А-ЯЁа-яё-]+)*$/.test($(element).val());
	}, "Укажите фамилию, имя и отчество полностью");
	$.validator.addMethod("checkINNPrivate", function(value, element) {
		return /^\d{12}$/.test($(element).val());
	}, "ИНН должен состоять из 12 цифр");
	$.validator.addMethod("checkINNCompany", function(value, element) {
		return /^\d{10}$/.test($(element).val());
	}, "ИНН должен состоять из 10 цифр");
	$.validator.addMethod("limits", function (value, element) {
    	var summAll = parseFloat($('#amountControl').val().replace(",","."));
    	return limits.min == undefined || limits.max == undefined || (summAll >= limits.min/100 && summAll <= limits.max/100);
    }, "Сумма должна быть от " + getFloat(limits.min/100) + " до " + getFloat(limits.max/100) + cur);
    $.validator.addMethod("amount", function (value, element) {
        return /^\d*([,.]{1}\d{1,2}){0,1}$/.test(value);
    }, "Сумма введена неверно");

	$('#amountControl').rules('add', {
        limits: true,
        amount: true
    });
	$('#bik').rules('add', {
		bik: true
	});
	$('#P017').rules('add', {
		accountNumber: true
	});
	$('#P008-1').rules('add', {
		checkFIO: true
	});
	$('#P060').rules('add', {
		checkINNPrivate: true
	});

	$('input[name=receiver_type]').each(function(){
		var self = $(this);
		function handler(){
			if(self.attr('id') == 'receiverTypePrivatePerson'){
				$('#desc').val('Перевод в пользу физического лица');

				$('#P016').prop('onkeypress', 'onkeypress="return checkLettersUnicodeRus(event);"').rules('add', {
					checkFIO: true
				});
				$('#P061').prop('maxlength', '12').rules('add', {
					checkINNPrivate: false
				});
				$('#P061').rules('remove', 'checkINNCompany');
			} else {
				$('#desc').val('Перевод в пользу организации');

				$('#P016').prop('onkeypress', '').rules('remove', 'checkFIO');
				$('#P061').prop('maxlength', '10').rules('add', {
					checkINNCompany: true
				});
				$('#P061').rules('remove', 'checkINNPrivate');
			}
		}
		self.on('input', function(){
			handler();
		});
		self.on('click', function(){
			handler();
		});
		if (self.is(':checked')){
			handler();
        }
    });
});