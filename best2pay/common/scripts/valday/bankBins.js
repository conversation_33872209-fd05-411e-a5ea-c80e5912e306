var allBankBins = new Map();

// 'AO RAIFFEISENBANK'
var raiffeisenLogo = {
    url: 'template/common/img/valday/bank-logo/raiffeisen.svg',
    color: '#ffdd2d',
    textColor: '#000000'
};
allBankBins.set('402177', raiffeisenLogo);
allBankBins.set('402178', raiffeisenLogo);
allBankBins.set('402179', raiffeisenLogo);
allBankBins.set('404807', raiffeisenLogo);
allBankBins.set('404885', raiffeisenLogo);
allBankBins.set('420705', raiffeisenLogo);
allBankBins.set('422287', raiffeisenLogo);
allBankBins.set('425620', raiffeisenLogo);
allBankBins.set('425884', raiffeisen<PERSON>ogo);
allBankBins.set('425885', raiffeisenLogo);
allBankBins.set('432498', raiffeisenLogo);
allBankBins.set('446916', raiffeisenLogo);
allBankBins.set('446917', raiffeisenLogo);
allBankBins.set('447603', raiffeisenLogo);
allBankBins.set('447624', raiffeisenLogo);
allBankBins.set('462729', raiffeisenLogo);
allBankBins.set('462730', raiffeisenLogo);
allBankBins.set('462758', raiffeisenLogo);
allBankBins.set('465578', raiffeisenLogo);
allBankBins.set('510069', raiffeisenLogo);
allBankBins.set('510070', raiffeisenLogo);
allBankBins.set('5100700', raiffeisenLogo);
allBankBins.set('5100705', raiffeisenLogo);
allBankBins.set('5100706', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('515876', raiffeisenLogo);
allBankBins.set('5158760', raiffeisenLogo);
allBankBins.set('5158764', raiffeisenLogo);
allBankBins.set('5158765', raiffeisenLogo);
allBankBins.set('*********', raiffeisenLogo);
allBankBins.set('*********', raiffeisenLogo);
allBankBins.set('*********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('528053', raiffeisenLogo);
allBankBins.set('528808', raiffeisenLogo);
allBankBins.set('528809', raiffeisenLogo);
allBankBins.set('530867', raiffeisenLogo);
allBankBins.set('533594', raiffeisenLogo);
allBankBins.set('533616', raiffeisenLogo);
allBankBins.set('536392', raiffeisenLogo);
allBankBins.set('537965', raiffeisenLogo);
allBankBins.set('542772', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('544237', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('545115', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('553496', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('558273', raiffeisenLogo);
allBankBins.set('558536', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('********', raiffeisenLogo);
allBankBins.set('676625', raiffeisenLogo);

// 'B&N BANK PUBLIC JOINT STOCK CO'
var binbankLogo = {url: 'template/common/img/valday/bank-logo/binbank.svg', color: '#69AEDF', textColor: '#ffffff'};
allBankBins.set('512276', binbankLogo);
allBankBins.set('514093', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('515785', binbankLogo);
allBankBins.set('515853', binbankLogo);
allBankBins.set('518286', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('518788', binbankLogo);
allBankBins.set('518960', binbankLogo);
allBankBins.set('518961', binbankLogo);
allBankBins.set('519317', binbankLogo);
allBankBins.set('520328', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('521814', binbankLogo);
allBankBins.set('524558', binbankLogo);
allBankBins.set('524660', binbankLogo);
allBankBins.set('524862', binbankLogo);
allBankBins.set('525744', binbankLogo);
allBankBins.set('527450', binbankLogo);
allBankBins.set('529403', binbankLogo);
allBankBins.set('531351', binbankLogo);
allBankBins.set('531425', binbankLogo);
allBankBins.set('532063', binbankLogo);
allBankBins.set('532835', binbankLogo);
allBankBins.set('533151', binbankLogo);
allBankBins.set('533614', binbankLogo);
allBankBins.set('534199', binbankLogo);
allBankBins.set('539036', binbankLogo);
allBankBins.set('539600', binbankLogo);
allBankBins.set('540194', binbankLogo);
allBankBins.set('540642', binbankLogo);
allBankBins.set('541152', binbankLogo);
allBankBins.set('541294', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('542504', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('543366', binbankLogo);
allBankBins.set('544117', binbankLogo);
allBankBins.set('545149', binbankLogo);
allBankBins.set('545181', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('547243', binbankLogo);
allBankBins.set('547801', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('548092', binbankLogo);
allBankBins.set('548265', binbankLogo);
allBankBins.set('548270', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('549349', binbankLogo);
allBankBins.set('549512', binbankLogo);
allBankBins.set('552866', binbankLogo);
allBankBins.set('554372', binbankLogo);
allBankBins.set('554373', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('558386', binbankLogo);
allBankBins.set('558636', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('********', binbankLogo);
allBankBins.set('676428', binbankLogo);
allBankBins.set('676653', binbankLogo);
allBankBins.set('677058', binbankLogo);
allBankBins.set('677276', binbankLogo);
allBankBins.set('677406', binbankLogo);
allBankBins.set('677592', binbankLogo);

/*allBankBins.set('457894', 'CB RUSSIAN MORTGAGE BANK LLC');
 allBankBins.set('471409', 'CB RUSSIAN MORTGAGE BANK LLC');
 allBankBins.set('471410', 'CB RUSSIAN MORTGAGE BANK LLC');
 allBankBins.set('471411', 'CB RUSSIAN MORTGAGE BANK LLC');
 allBankBins.set('471412', 'CB RUSSIAN MORTGAGE BANK LLC');*/

// 'CJSC UNICREDIT BANK'
var unicreditLogo = {url: 'template/common/img/valday/bank-logo/unicredit.svg', color: '#e71c23', textColor: '#ffffff'};
allBankBins.set('413047', unicreditLogo);
allBankBins.set('430323', unicreditLogo);
allBankBins.set('432560', unicreditLogo);
allBankBins.set('433300', unicreditLogo);
allBankBins.set('433336', unicreditLogo);
allBankBins.set('462776', unicreditLogo);
allBankBins.set('469362', unicreditLogo);
allBankBins.set('472446', unicreditLogo);
allBankBins.set('485608', unicreditLogo);
allBankBins.set('489042', unicreditLogo);
allBankBins.set('489099', unicreditLogo);
allBankBins.set('490818', unicreditLogo);
allBankBins.set('490855', unicreditLogo);
allBankBins.set('510074', unicreditLogo);
allBankBins.set('518996', unicreditLogo);
allBankBins.set('518997', unicreditLogo);
allBankBins.set('522458', unicreditLogo);
allBankBins.set('530172', unicreditLogo);
allBankBins.set('531236', unicreditLogo);
allBankBins.set('531344', unicreditLogo);
allBankBins.set('547728', unicreditLogo);
allBankBins.set('549302', unicreditLogo);
allBankBins.set('676672', unicreditLogo);

// 'CLOSED JOINT STOCK COMPANY COMMERCIAL BANK CITIBANK'
var cityLogo = {url: 'template/common/img/valday/bank-logo/city.svg', color: '#21254E', textColor: '#ffffff'};
allBankBins.set('419293', cityLogo);
allBankBins.set('419349', cityLogo);
allBankBins.set('419351', cityLogo);
allBankBins.set('421179', cityLogo);
allBankBins.set('427760', cityLogo);
allBankBins.set('427761', cityLogo);
allBankBins.set('469360', cityLogo);
allBankBins.set('473849', cityLogo);
allBankBins.set('473850', cityLogo);
allBankBins.set('485467', cityLogo);
allBankBins.set('486666', cityLogo);
allBankBins.set('525689', cityLogo);
allBankBins.set('539726', cityLogo);

/*allBankBins.set('403780', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('419292', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('443223', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('443271', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('443272', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('443273', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('471358', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('515770', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('521801', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('524655', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('532184', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('542033', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('543211', 'CREDIT BANK OF MOSCOW (CBM)');
 allBankBins.set('552680', 'CREDIT BANK OF MOSCOW (CBM)');*/

// 'GAZPROMBANK (OJSC)'
var gazprombankLogo = {
    url: 'template/common/img/valday/bank-logo/gazprombank.svg',
    color: '#1E5378',
    textColor: '#ffffff'
};
allBankBins.set('404136', gazprombankLogo);
allBankBins.set('404270', gazprombankLogo);
allBankBins.set('424917', gazprombankLogo);
allBankBins.set('424974', gazprombankLogo);
allBankBins.set('424975', gazprombankLogo);
allBankBins.set('424976', gazprombankLogo);
allBankBins.set('426890', gazprombankLogo);
allBankBins.set('427326', gazprombankLogo);
allBankBins.set('487415', gazprombankLogo);
allBankBins.set('487416', gazprombankLogo);
allBankBins.set('487417', gazprombankLogo);
allBankBins.set('489354', gazprombankLogo);
allBankBins.set('510162', gazprombankLogo);
allBankBins.set('512347', gazprombankLogo);
allBankBins.set('512643', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('516454', gazprombankLogo);
allBankBins.set('517375', gazprombankLogo);
allBankBins.set('517593', gazprombankLogo);
allBankBins.set('517807', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('518365', gazprombankLogo);
allBankBins.set('518816', gazprombankLogo);
allBankBins.set('518902', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('521155', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('522193', gazprombankLogo);
allBankBins.set('522477', gazprombankLogo);
allBankBins.set('522826', gazprombankLogo);
allBankBins.set('522988', gazprombankLogo);
allBankBins.set('522989', gazprombankLogo);
allBankBins.set('525740', gazprombankLogo);
allBankBins.set('***********', gazprombankLogo);
allBankBins.set('***********', gazprombankLogo);
allBankBins.set('***********', gazprombankLogo);
allBankBins.set('***********', gazprombankLogo);
allBankBins.set('525833', gazprombankLogo);
allBankBins.set('526483', gazprombankLogo);
allBankBins.set('527444', gazprombankLogo);
allBankBins.set('529278', gazprombankLogo);
allBankBins.set('529488', gazprombankLogo);
allBankBins.set('530114', gazprombankLogo);
allBankBins.set('530993', gazprombankLogo);
allBankBins.set('532684', gazprombankLogo);
allBankBins.set('533327', gazprombankLogo);
allBankBins.set('534130', gazprombankLogo);
allBankBins.set('534196', gazprombankLogo);
allBankBins.set('537627', gazprombankLogo);
allBankBins.set('539839', gazprombankLogo);
allBankBins.set('539864', gazprombankLogo);
allBankBins.set('539865', gazprombankLogo);
allBankBins.set('540014', gazprombankLogo);
allBankBins.set('540664', gazprombankLogo);
allBankBins.set('540708', gazprombankLogo);
allBankBins.set('542247', gazprombankLogo);
allBankBins.set('542255', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('543367', gazprombankLogo);
allBankBins.set('543672', gazprombankLogo);
allBankBins.set('543762', gazprombankLogo);
allBankBins.set('544026', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('544561', gazprombankLogo);
allBankBins.set('5445610', gazprombankLogo);
allBankBins.set('5445616', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('545101', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('547348', gazprombankLogo);
allBankBins.set('548027', gazprombankLogo);
allBankBins.set('548328', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('548753', gazprombankLogo);
allBankBins.set('548791', gazprombankLogo);
allBankBins.set('548999', gazprombankLogo);
allBankBins.set('*********', gazprombankLogo);
allBankBins.set('*********', gazprombankLogo);
allBankBins.set('*********', gazprombankLogo);
allBankBins.set('*********', gazprombankLogo);
allBankBins.set('*********', gazprombankLogo);
allBankBins.set('5489992', gazprombankLogo);
allBankBins.set('5489993', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('***********', gazprombankLogo);
allBankBins.set('**********', gazprombankLogo);
allBankBins.set('**********', gazprombankLogo);
allBankBins.set('**********', gazprombankLogo);
allBankBins.set('**********', gazprombankLogo);
allBankBins.set('*********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('549098', gazprombankLogo);
allBankBins.set('549470', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('549600', gazprombankLogo);
allBankBins.set('549654', gazprombankLogo);
allBankBins.set('552702', gazprombankLogo);
allBankBins.set('556052', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('558355', gazprombankLogo);
allBankBins.set('559255', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('********', gazprombankLogo);
allBankBins.set('559992', gazprombankLogo);
allBankBins.set('6233710', gazprombankLogo);
allBankBins.set('6233711', gazprombankLogo);
allBankBins.set('6233712', gazprombankLogo);
allBankBins.set('6233720', gazprombankLogo);
allBankBins.set('6233721', gazprombankLogo);
allBankBins.set('6233722', gazprombankLogo);
allBankBins.set('6233723', gazprombankLogo);
allBankBins.set('6233730', gazprombankLogo);
allBankBins.set('6233731', gazprombankLogo);
allBankBins.set('6233732', gazprombankLogo);
allBankBins.set('6233760', gazprombankLogo);
allBankBins.set('6233761', gazprombankLogo);
allBankBins.set('6233762', gazprombankLogo);
allBankBins.set('6243650', gazprombankLogo);
allBankBins.set('6243651', gazprombankLogo);
allBankBins.set('6243652', gazprombankLogo);
allBankBins.set('671122', gazprombankLogo);
allBankBins.set('676454', gazprombankLogo);
allBankBins.set('676979', gazprombankLogo);
allBankBins.set('676990', gazprombankLogo);
allBankBins.set('677257', gazprombankLogo);
allBankBins.set('677338', gazprombankLogo);
allBankBins.set('677478', gazprombankLogo);
allBankBins.set('677484', gazprombankLogo);
allBankBins.set('677585', gazprombankLogo);

/*allBankBins.set('403896', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('403897', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('403898', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('419163', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('470673', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('470674', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('470675', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');
 allBankBins.set('478273', 'JOINT STOCK COMMERCIAL BANK "AVANGARD"');*/

// 'JOINT STOCK COMPANY "ALFA-BANK"'
var alfabankLogo = {url: 'template/common/img/valday/bank-logo/alfabank.svg', color: '#ED3523', textColor: '#ffffff'};
allBankBins.set('408396', alfabankLogo);
allBankBins.set('408397', alfabankLogo);
allBankBins.set('415400', alfabankLogo);
allBankBins.set('415428', alfabankLogo);
allBankBins.set('415429', alfabankLogo);
allBankBins.set('415481', alfabankLogo);
allBankBins.set('415482', alfabankLogo);
allBankBins.set('419539', alfabankLogo);
allBankBins.set('419540', alfabankLogo);
allBankBins.set('426101', alfabankLogo);
allBankBins.set('426102', alfabankLogo);
allBankBins.set('426113', alfabankLogo);
allBankBins.set('426114', alfabankLogo);
allBankBins.set('427714', alfabankLogo);
allBankBins.set('428804', alfabankLogo);
allBankBins.set('428905', alfabankLogo);
allBankBins.set('428906', alfabankLogo);
allBankBins.set('431417', alfabankLogo);
allBankBins.set('439000', alfabankLogo);
allBankBins.set('439077', alfabankLogo);
allBankBins.set('458279', alfabankLogo);
allBankBins.set('458410', alfabankLogo);
allBankBins.set('458411', alfabankLogo);
allBankBins.set('458443', alfabankLogo);
allBankBins.set('475791', alfabankLogo);
allBankBins.set('477714', alfabankLogo);
allBankBins.set('477960', alfabankLogo);
allBankBins.set('477964', alfabankLogo);
allBankBins.set('479004', alfabankLogo);
allBankBins.set('479087', alfabankLogo);
allBankBins.set('510126', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('519747', alfabankLogo);
allBankBins.set('5197470', alfabankLogo);
allBankBins.set('5197477', alfabankLogo);
allBankBins.set('519778', alfabankLogo);
allBankBins.set('5197780', alfabankLogo);
allBankBins.set('5197785', alfabankLogo);
allBankBins.set('5197786', alfabankLogo);
allBankBins.set('5197787', alfabankLogo);
allBankBins.set('521178', alfabankLogo);
allBankBins.set('522828', alfabankLogo);
allBankBins.set('523701', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('530331', alfabankLogo);
allBankBins.set('530827', alfabankLogo);
allBankBins.set('531237', alfabankLogo);
allBankBins.set('536423', alfabankLogo);
allBankBins.set('537643', alfabankLogo);
allBankBins.set('541190', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('545036', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('547450', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('548601', alfabankLogo);
allBankBins.set('548655', alfabankLogo);
allBankBins.set('548673', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('548674', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('552175', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('**********', alfabankLogo);
allBankBins.set('552186', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('555156', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('555921', alfabankLogo);
allBankBins.set('555928', alfabankLogo);
allBankBins.set('555933', alfabankLogo);
allBankBins.set('555947', alfabankLogo);
allBankBins.set('555949', alfabankLogo);
allBankBins.set('555957', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('558334', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('********', alfabankLogo);
allBankBins.set('676248', alfabankLogo);

/*allBankBins.set('446955', 'JOINT-STOCK COMERCIAL BANK DEFENCE INDUSTRIAL BANK (VPB)');
 allBankBins.set('446956', 'JOINT-STOCK COMERCIAL BANK DEFENCE INDUSTRIAL BANK (VPB)');
 allBankBins.set('446957', 'JOINT-STOCK COMERCIAL BANK DEFENCE INDUSTRIAL BANK (VPB)');
 allBankBins.set('446958', 'JOINT-STOCK COMERCIAL BANK DEFENCE INDUSTRIAL BANK (VPB)');
 allBankBins.set('459255', 'JOINT-STOCK COMERCIAL BANK DEFENCE INDUSTRIAL BANK (VPB)');*/

// 'JSC RUSSIAN STANDARD BANK'
var russtandardLogo = {
    url: 'template/common/img/valday/bank-logo/russtandart.svg',
    color: '#61553F',
    textColor: '#ffffff'
};
allBankBins.set('411790', russtandardLogo);
allBankBins.set('417250', russtandardLogo);
allBankBins.set('417251', russtandardLogo);
allBankBins.set('417252', russtandardLogo);
allBankBins.set('417291', russtandardLogo);
allBankBins.set('419718', russtandardLogo);
allBankBins.set('444429', russtandardLogo);
allBankBins.set('457647', russtandardLogo);
allBankBins.set('483175', russtandardLogo);
allBankBins.set('483176', russtandardLogo);
allBankBins.set('483177', russtandardLogo);
allBankBins.set('486065', russtandardLogo);
allBankBins.set('510047', russtandardLogo);
allBankBins.set('510092', russtandardLogo);
allBankBins.set('513691', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('522455', russtandardLogo);
allBankBins.set('522588', russtandardLogo);
allBankBins.set('524381', russtandardLogo);
allBankBins.set('533469', russtandardLogo);
allBankBins.set('533689', russtandardLogo);
allBankBins.set('534266', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('542048', russtandardLogo);
allBankBins.set('544429', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('545160', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('547262', russtandardLogo);
allBankBins.set('5472620', russtandardLogo);
allBankBins.set('5472623', russtandardLogo);
allBankBins.set('548235', russtandardLogo);
allBankBins.set('548996', russtandardLogo);
allBankBins.set('548997', russtandardLogo);
allBankBins.set('558354', russtandardLogo);
allBankBins.set('********', russtandardLogo);
allBankBins.set('6250386', russtandardLogo);
allBankBins.set('676565', russtandardLogo);

// 'KHANTY-MANSIYSK BANK OTKRITIE PJSC'
/*allBankBins.set('404586', otkrytieLogo);
 allBankBins.set('405870', otkrytieLogo);
 allBankBins.set('406790', otkrytieLogo);
 allBankBins.set('406791', otkrytieLogo);
 allBankBins.set('407178', otkrytieLogo);
 allBankBins.set('409701', otkrytieLogo);
 allBankBins.set('409755', otkrytieLogo);
 allBankBins.set('409756', otkrytieLogo);
 allBankBins.set('414076', otkrytieLogo);
 allBankBins.set('425656', otkrytieLogo);
 allBankBins.set('426896', otkrytieLogo);
 allBankBins.set('429037', otkrytieLogo);
 allBankBins.set('429038', otkrytieLogo);
 allBankBins.set('429039', otkrytieLogo);
 allBankBins.set('429040', otkrytieLogo);
 allBankBins.set('437351', otkrytieLogo);
 allBankBins.set('446065', otkrytieLogo);
 allBankBins.set('458493', otkrytieLogo);
 allBankBins.set('464843', otkrytieLogo);
 allBankBins.set('467485', otkrytieLogo);
 allBankBins.set('467487', otkrytieLogo);
 allBankBins.set('472840', otkrytieLogo);
 allBankBins.set('472841', otkrytieLogo);
 allBankBins.set('472842', otkrytieLogo);
 allBankBins.set('472843', otkrytieLogo);
 allBankBins.set('479302', otkrytieLogo);
 allBankBins.set('484800', otkrytieLogo);
 allBankBins.set('518629', otkrytieLogo);
 allBankBins.set('531013', otkrytieLogo);
 allBankBins.set('541023', otkrytieLogo);
 allBankBins.set('550164', otkrytieLogo);*/

// 'LLC HOME CREDIT AND FINANCE BANK'
var homecreditLogo = {
    url: 'template/common/img/valday/bank-logo/homecreditbank.svg',
    color: '#E21836',
    textColor: '#ffffff'
};
allBankBins.set('445433', homecreditLogo);
allBankBins.set('445434', homecreditLogo);
allBankBins.set('445435', homecreditLogo);
allBankBins.set('446098', homecreditLogo);
allBankBins.set('446915', homecreditLogo);
allBankBins.set('472445', homecreditLogo);

// 'OJSC ICB SOVCOMBANK'
/*allBankBins.set('402532', 'OJSC ICB SOVCOMBANK');
allBankBins.set('402534', 'OJSC ICB SOVCOMBANK');
allBankBins.set('418362', 'OJSC ICB SOVCOMBANK');
allBankBins.set('418363', 'OJSC ICB SOVCOMBANK');
allBankBins.set('418364', 'OJSC ICB SOVCOMBANK');
allBankBins.set('432170', 'OJSC ICB SOVCOMBANK');
 allBankBins.set('432171', 'OJSC ICB SOVCOMBANK');*/

// 'OJSC OTP BANK'
var otpbankLogo = {url: 'template/common/img/valday/bank-logo/otpbank.svg', color: '#56BD09', textColor: '#ffffff'};
allBankBins.set('402313', otpbankLogo);
allBankBins.set('409681', otpbankLogo);
allBankBins.set('432947', otpbankLogo);
allBankBins.set('432948', otpbankLogo);
allBankBins.set('432949', otpbankLogo);
allBankBins.set('459230', otpbankLogo);
allBankBins.set('515774', otpbankLogo);
allBankBins.set('516009', otpbankLogo);
allBankBins.set('517202', otpbankLogo);
allBankBins.set('521330', otpbankLogo);
allBankBins.set('522470', otpbankLogo);
allBankBins.set('526839', otpbankLogo);
allBankBins.set('529968', otpbankLogo);
allBankBins.set('531428', otpbankLogo);
allBankBins.set('533685', otpbankLogo);
allBankBins.set('545214', otpbankLogo);
allBankBins.set('552140', otpbankLogo);

/*allBankBins.set('418384', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('418385', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('418386', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('418387', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('422608', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('524448', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('525446', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('534162', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('536409', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('538010', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('547601', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('548048', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('549715', 'OJSC RUSSIAN AGRICULTURAL BANK');
 allBankBins.set('6234460', 'OJSC RUSSIAN AGRICULTURAL BANK');

 allBankBins.set('406334', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('425534', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('425535', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('443886', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('443887', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('443888', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('444094', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('472933', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('472934', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('476804', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('621647', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');
 allBankBins.set('625104', 'ORIENT EXPRESS BANK OJSC (OAO KB ORIENT)');*/

// 'PJSC ROSBANK'
var rosbankLogo = {url: 'template/common/img/valday/bank-logo/rosbank.svg', color: '#E60028', textColor: '#ffffff'};
allBankBins.set('404891', rosbankLogo);
allBankBins.set('404893', rosbankLogo);
allBankBins.set('406767', rosbankLogo);
allBankBins.set('407564', rosbankLogo);
allBankBins.set('423169', rosbankLogo);
allBankBins.set('427715', rosbankLogo);
allBankBins.set('438933', rosbankLogo);
allBankBins.set('438970', rosbankLogo);
allBankBins.set('440503', rosbankLogo);
allBankBins.set('440505', rosbankLogo);
allBankBins.set('440540', rosbankLogo);
allBankBins.set('440541', rosbankLogo);
allBankBins.set('474218', rosbankLogo);
allBankBins.set('477908', rosbankLogo);
allBankBins.set('477986', rosbankLogo);
allBankBins.set('477987', rosbankLogo);
allBankBins.set('499932', rosbankLogo);
allBankBins.set('510038', rosbankLogo);
allBankBins.set('510039', rosbankLogo);
allBankBins.set('510082', rosbankLogo);
allBankBins.set('510098', rosbankLogo);
allBankBins.set('510125', rosbankLogo);
allBankBins.set('510166', rosbankLogo);
allBankBins.set('510170', rosbankLogo);
allBankBins.set('510461', rosbankLogo);
allBankBins.set('510846', rosbankLogo);
allBankBins.set('511735', rosbankLogo);
allBankBins.set('512003', rosbankLogo);
allBankBins.set('512082', rosbankLogo);
allBankBins.set('512413', rosbankLogo);
allBankBins.set('512428', rosbankLogo);
allBankBins.set('512704', rosbankLogo);
allBankBins.set('512736', rosbankLogo);
allBankBins.set('512756', rosbankLogo);
allBankBins.set('512771', rosbankLogo);
allBankBins.set('512808', rosbankLogo);
allBankBins.set('513022', rosbankLogo);
allBankBins.set('514529', rosbankLogo);
allBankBins.set('514930', rosbankLogo);
allBankBins.set('515605', rosbankLogo);
allBankBins.set('515859', rosbankLogo);
allBankBins.set('516243', rosbankLogo);
allBankBins.set('516551', rosbankLogo);
allBankBins.set('516696', rosbankLogo);
allBankBins.set('517538', rosbankLogo);
allBankBins.set('517583', rosbankLogo);
allBankBins.set('517647', rosbankLogo);
allBankBins.set('517822', rosbankLogo);
allBankBins.set('518092', rosbankLogo);
allBankBins.set('518331', rosbankLogo);
allBankBins.set('518406', rosbankLogo);
allBankBins.set('518580', rosbankLogo);
allBankBins.set('518598', rosbankLogo);
allBankBins.set('518642', rosbankLogo);
allBankBins.set('518714', rosbankLogo);
allBankBins.set('518795', rosbankLogo);
allBankBins.set('518864', rosbankLogo);
allBankBins.set('********', rosbankLogo);
allBankBins.set('********', rosbankLogo);
allBankBins.set('********', rosbankLogo);
allBankBins.set('518882', rosbankLogo);
allBankBins.set('518889', rosbankLogo);
allBankBins.set('518977', rosbankLogo);
allBankBins.set('518981', rosbankLogo);
allBankBins.set('519630', rosbankLogo);
allBankBins.set('520348', rosbankLogo);
allBankBins.set('520902', rosbankLogo);
allBankBins.set('521374', rosbankLogo);
allBankBins.set('521508', rosbankLogo);
allBankBins.set('522511', rosbankLogo);
allBankBins.set('522513', rosbankLogo);
allBankBins.set('522622', rosbankLogo);
allBankBins.set('522705', rosbankLogo);
allBankBins.set('522710', rosbankLogo);
allBankBins.set('522711', rosbankLogo);
allBankBins.set('522735', rosbankLogo);
allBankBins.set('522863', rosbankLogo);
allBankBins.set('523787', rosbankLogo);
allBankBins.set('523922', rosbankLogo);
allBankBins.set('524001', rosbankLogo);
allBankBins.set('524614', rosbankLogo);
allBankBins.set('524861', rosbankLogo);
allBankBins.set('525245', rosbankLogo);
allBankBins.set('525247', rosbankLogo);
allBankBins.set('525741', rosbankLogo);
allBankBins.set('525776', rosbankLogo);
allBankBins.set('525778', rosbankLogo);
allBankBins.set('525781', rosbankLogo);
allBankBins.set('525787', rosbankLogo);
allBankBins.set('525794', rosbankLogo);
allBankBins.set('5257940', rosbankLogo);
allBankBins.set('5257947', rosbankLogo);
allBankBins.set('525936', rosbankLogo);
allBankBins.set('526462', rosbankLogo);
allBankBins.set('526777', rosbankLogo);
allBankBins.set('526981', rosbankLogo);
allBankBins.set('526984', rosbankLogo);
allBankBins.set('527393', rosbankLogo);
allBankBins.set('527443', rosbankLogo);
allBankBins.set('527640', rosbankLogo);
allBankBins.set('527643', rosbankLogo);
allBankBins.set('527663', rosbankLogo);
allBankBins.set('528015', rosbankLogo);
allBankBins.set('528090', rosbankLogo);
allBankBins.set('528270', rosbankLogo);
allBankBins.set('528589', rosbankLogo);
allBankBins.set('528701', rosbankLogo);
allBankBins.set('528819', rosbankLogo);
allBankBins.set('528933', rosbankLogo);
allBankBins.set('529100', rosbankLogo);
allBankBins.set('529101', rosbankLogo);
allBankBins.set('529247', rosbankLogo);
allBankBins.set('529325', rosbankLogo);
allBankBins.set('529437', rosbankLogo);
allBankBins.set('529813', rosbankLogo);
allBankBins.set('529862', rosbankLogo);
allBankBins.set('529934', rosbankLogo);
allBankBins.set('530027', rosbankLogo);
allBankBins.set('530174', rosbankLogo);
allBankBins.set('530412', rosbankLogo);
allBankBins.set('530416', rosbankLogo);
allBankBins.set('530527', rosbankLogo);
allBankBins.set('530800', rosbankLogo);
allBankBins.set('531222', rosbankLogo);
allBankBins.set('532058', rosbankLogo);
allBankBins.set('532299', rosbankLogo);
allBankBins.set('532336', rosbankLogo);
allBankBins.set('***********', rosbankLogo);
allBankBins.set('***********', rosbankLogo);
allBankBins.set('***********', rosbankLogo);
allBankBins.set('533684', rosbankLogo);
allBankBins.set('533795', rosbankLogo);
allBankBins.set('533879', rosbankLogo);
allBankBins.set('533925', rosbankLogo);
allBankBins.set('534251', rosbankLogo);
allBankBins.set('534297', rosbankLogo);
allBankBins.set('534449', rosbankLogo);
allBankBins.set('534577', rosbankLogo);
allBankBins.set('534645', rosbankLogo);
allBankBins.set('534921', rosbankLogo);
allBankBins.set('535067', rosbankLogo);
allBankBins.set('536109', rosbankLogo);
allBankBins.set('536569', rosbankLogo);
allBankBins.set('539102', rosbankLogo);
allBankBins.set('539664', rosbankLogo);
allBankBins.set('539705', rosbankLogo);
allBankBins.set('539712', rosbankLogo);
allBankBins.set('540035', rosbankLogo);
allBankBins.set('540053', rosbankLogo);
allBankBins.set('540149', rosbankLogo);
allBankBins.set('541031', rosbankLogo);
allBankBins.set('541512', rosbankLogo);
allBankBins.set('541903', rosbankLogo);
allBankBins.set('541904', rosbankLogo);
allBankBins.set('541997', rosbankLogo);
allBankBins.set('542058', rosbankLogo);
allBankBins.set('542774', rosbankLogo);
allBankBins.set('542963', rosbankLogo);
allBankBins.set('543127', rosbankLogo);
allBankBins.set('543326', rosbankLogo);
allBankBins.set('543334', rosbankLogo);
allBankBins.set('544067', rosbankLogo);
allBankBins.set('544263', rosbankLogo);
allBankBins.set('544286', rosbankLogo);
allBankBins.set('544374', rosbankLogo);
allBankBins.set('544491', rosbankLogo);
allBankBins.set('544899', rosbankLogo);
allBankBins.set('545022', rosbankLogo);
allBankBins.set('545151', rosbankLogo);
allBankBins.set('545204', rosbankLogo);
allBankBins.set('545261', rosbankLogo);
allBankBins.set('545364', rosbankLogo);
allBankBins.set('545378', rosbankLogo);
allBankBins.set('545379', rosbankLogo);
allBankBins.set('********', rosbankLogo);
allBankBins.set('***********', rosbankLogo);
allBankBins.set('***********', rosbankLogo);
allBankBins.set('545547', rosbankLogo);
allBankBins.set('545572', rosbankLogo);
allBankBins.set('545575', rosbankLogo);
allBankBins.set('545746', rosbankLogo);
allBankBins.set('545951', rosbankLogo);
allBankBins.set('546737', rosbankLogo);
allBankBins.set('547070', rosbankLogo);
allBankBins.set('547228', rosbankLogo);
allBankBins.set('547251', rosbankLogo);
allBankBins.set('547548', rosbankLogo);
allBankBins.set('547681', rosbankLogo);
allBankBins.set('548164', rosbankLogo);
allBankBins.set('548198', rosbankLogo);
allBankBins.set('548225', rosbankLogo);
allBankBins.set('548249', rosbankLogo);
allBankBins.set('548409', rosbankLogo);
allBankBins.set('548796', rosbankLogo);
allBankBins.set('548921', rosbankLogo);
allBankBins.set('549068', rosbankLogo);
allBankBins.set('549081', rosbankLogo);
allBankBins.set('549268', rosbankLogo);
allBankBins.set('549445', rosbankLogo);
allBankBins.set('549467', rosbankLogo);
allBankBins.set('549475', rosbankLogo);
allBankBins.set('549478', rosbankLogo);
allBankBins.set('549822', rosbankLogo);
allBankBins.set('549829', rosbankLogo);
allBankBins.set('549855', rosbankLogo);
allBankBins.set('549865', rosbankLogo);
allBankBins.set('549884', rosbankLogo);
allBankBins.set('550064', rosbankLogo);
allBankBins.set('550143', rosbankLogo);
allBankBins.set('550165', rosbankLogo);
allBankBins.set('550210', rosbankLogo);
allBankBins.set('550212', rosbankLogo);
allBankBins.set('550229', rosbankLogo);
allBankBins.set('550467', rosbankLogo);
allBankBins.set('551979', rosbankLogo);
allBankBins.set('551985', rosbankLogo);
allBankBins.set('551989', rosbankLogo);
allBankBins.set('551993', rosbankLogo);
allBankBins.set('551994', rosbankLogo);
allBankBins.set('551996', rosbankLogo);
allBankBins.set('552151', rosbankLogo);
allBankBins.set('552856', rosbankLogo);
allBankBins.set('553069', rosbankLogo);
allBankBins.set('553128', rosbankLogo);
allBankBins.set('553690', rosbankLogo);
allBankBins.set('553909', rosbankLogo);
allBankBins.set('553964', rosbankLogo);
allBankBins.set('553976', rosbankLogo);
allBankBins.set('554317', rosbankLogo);
allBankBins.set('554324', rosbankLogo);
allBankBins.set('554326', rosbankLogo);
allBankBins.set('554364', rosbankLogo);
allBankBins.set('554365', rosbankLogo);
allBankBins.set('554549', rosbankLogo);
allBankBins.set('554713', rosbankLogo);
allBankBins.set('554733', rosbankLogo);
allBankBins.set('554761', rosbankLogo);
allBankBins.set('554782', rosbankLogo);
allBankBins.set('554844', rosbankLogo);
allBankBins.set('555079', rosbankLogo);
allBankBins.set('557646', rosbankLogo);
allBankBins.set('557724', rosbankLogo);
allBankBins.set('557841', rosbankLogo);
allBankBins.set('557842', rosbankLogo);
allBankBins.set('557955', rosbankLogo);
allBankBins.set('557977', rosbankLogo);
allBankBins.set('557980', rosbankLogo);
allBankBins.set('558416', rosbankLogo);
allBankBins.set('558480', rosbankLogo);
allBankBins.set('558504', rosbankLogo);
allBankBins.set('558605', rosbankLogo);
allBankBins.set('558673', rosbankLogo);
allBankBins.set('559448', rosbankLogo);
allBankBins.set('559476', rosbankLogo);
allBankBins.set('559488', rosbankLogo);
allBankBins.set('559596', rosbankLogo);
allBankBins.set('559598', rosbankLogo);
allBankBins.set('559615', rosbankLogo);
allBankBins.set('5596150', rosbankLogo);
allBankBins.set('5596156', rosbankLogo);
allBankBins.set('5596157', rosbankLogo);
allBankBins.set('559811', rosbankLogo);
allBankBins.set('559814', rosbankLogo);
allBankBins.set('559899', rosbankLogo);
allBankBins.set('559969', rosbankLogo);
allBankBins.set('670520', rosbankLogo);
allBankBins.set('670521', rosbankLogo);
allBankBins.set('670567', rosbankLogo);
allBankBins.set('670575', rosbankLogo);
allBankBins.set('670607', rosbankLogo);
allBankBins.set('670652', rosbankLogo);
allBankBins.set('670674', rosbankLogo);
allBankBins.set('670694', rosbankLogo);
allBankBins.set('670819', rosbankLogo);
allBankBins.set('670828', rosbankLogo);
allBankBins.set('670839', rosbankLogo);
allBankBins.set('670849', rosbankLogo);
allBankBins.set('670851', rosbankLogo);
allBankBins.set('671137', rosbankLogo);
allBankBins.set('676347', rosbankLogo);
allBankBins.set('676450', rosbankLogo);
allBankBins.set('676533', rosbankLogo);
allBankBins.set('676946', rosbankLogo);
allBankBins.set('677076', rosbankLogo);
allBankBins.set('677112', rosbankLogo);
allBankBins.set('677222', rosbankLogo);
allBankBins.set('677234', rosbankLogo);
allBankBins.set('677240', rosbankLogo);
allBankBins.set('677302', rosbankLogo);
allBankBins.set('677305', rosbankLogo);
allBankBins.set('677309', rosbankLogo);
allBankBins.set('6773090', rosbankLogo);
allBankBins.set('6773094', rosbankLogo);
allBankBins.set('6773098', rosbankLogo);
allBankBins.set('677315', rosbankLogo);
allBankBins.set('677349', rosbankLogo);
allBankBins.set('677359', rosbankLogo);
allBankBins.set('677360', rosbankLogo);
allBankBins.set('677372', rosbankLogo);
allBankBins.set('677401', rosbankLogo);
allBankBins.set('677464', rosbankLogo);
allBankBins.set('677467', rosbankLogo);
allBankBins.set('677468', rosbankLogo);
allBankBins.set('677495', rosbankLogo);
allBankBins.set('677507', rosbankLogo);
allBankBins.set('677579', rosbankLogo);
allBankBins.set('677617', rosbankLogo);

// 'PUBLIC JOINT STOCK COMPANY "BANK URALSIB"'
var uralsibLogo = {url: 'template/common/img/valday/bank-logo/ural-sib.svg', color: '#1E398D', textColor: '#ffffff'};
allBankBins.set('409356', uralsibLogo);
allBankBins.set('409357', uralsibLogo);
allBankBins.set('409358', uralsibLogo);
allBankBins.set('409682', uralsibLogo);
allBankBins.set('419370', uralsibLogo);
allBankBins.set('419632', uralsibLogo);
allBankBins.set('419633', uralsibLogo);
allBankBins.set('419634', uralsibLogo);
allBankBins.set('419804', uralsibLogo);
allBankBins.set('419805', uralsibLogo);
allBankBins.set('419810', uralsibLogo);
allBankBins.set('424204', uralsibLogo);
allBankBins.set('424205', uralsibLogo);
allBankBins.set('424206', uralsibLogo);
allBankBins.set('424207', uralsibLogo);
allBankBins.set('424290', uralsibLogo);
allBankBins.set('424291', uralsibLogo);
allBankBins.set('424473', uralsibLogo);
allBankBins.set('424475', uralsibLogo);
allBankBins.set('426837', uralsibLogo);
allBankBins.set('426838', uralsibLogo);
allBankBins.set('426839', uralsibLogo);
allBankBins.set('427827', uralsibLogo);
allBankBins.set('427828', uralsibLogo);
allBankBins.set('428155', uralsibLogo);
allBankBins.set('428156', uralsibLogo);
allBankBins.set('428157', uralsibLogo);
allBankBins.set('429197', uralsibLogo);
allBankBins.set('429811', uralsibLogo);
allBankBins.set('432259', uralsibLogo);
allBankBins.set('432260', uralsibLogo);
allBankBins.set('440610', uralsibLogo);
allBankBins.set('440664', uralsibLogo);
allBankBins.set('440665', uralsibLogo);
allBankBins.set('440666', uralsibLogo);
allBankBins.set('440668', uralsibLogo);
allBankBins.set('440680', uralsibLogo);
allBankBins.set('440682', uralsibLogo);
allBankBins.set('440683', uralsibLogo);
allBankBins.set('440689', uralsibLogo);
allBankBins.set('440690', uralsibLogo);
allBankBins.set('446502', uralsibLogo);
allBankBins.set('446503', uralsibLogo);
allBankBins.set('447372', uralsibLogo);
allBankBins.set('447373', uralsibLogo);
allBankBins.set('447374', uralsibLogo);
allBankBins.set('466047', uralsibLogo);
allBankBins.set('466048', uralsibLogo);
allBankBins.set('466049', uralsibLogo);
allBankBins.set('466050', uralsibLogo);
allBankBins.set('467811', uralsibLogo);
allBankBins.set('467812', uralsibLogo);
allBankBins.set('470342', uralsibLogo);
allBankBins.set('471439', uralsibLogo);
allBankBins.set('471440', uralsibLogo);
allBankBins.set('471441', uralsibLogo);
allBankBins.set('471499', uralsibLogo);
allBankBins.set('472479', uralsibLogo);
allBankBins.set('483973', uralsibLogo);
allBankBins.set('483974', uralsibLogo);
allBankBins.set('483975', uralsibLogo);
allBankBins.set('483976', uralsibLogo);
allBankBins.set('483977', uralsibLogo);
allBankBins.set('483979', uralsibLogo);
allBankBins.set('483980', uralsibLogo);
allBankBins.set('522965', uralsibLogo);
allBankBins.set('540687', uralsibLogo);
allBankBins.set('548393', uralsibLogo);

// 'PUBLIC JOINT STOCK COMPANY PROMSVYAZBANK'
var promsvyazLogo = {
    url: 'template/common/img/valday/bank-logo/promsvyazbank.svg',
    color: '#E15D29',
    textColor: '#ffffff'
};
allBankBins.set('411791', promsvyazLogo);
allBankBins.set('424563', promsvyazLogo);
allBankBins.set('426803', promsvyazLogo);
allBankBins.set('426804', promsvyazLogo);
allBankBins.set('436752', promsvyazLogo);
allBankBins.set('436753', promsvyazLogo);
allBankBins.set('436754', promsvyazLogo);
allBankBins.set('447817', promsvyazLogo);
allBankBins.set('447818', promsvyazLogo);
allBankBins.set('447824', promsvyazLogo);
allBankBins.set('451382', promsvyazLogo);
allBankBins.set('472345', promsvyazLogo);
allBankBins.set('472346', promsvyazLogo);
allBankBins.set('472347', promsvyazLogo);
allBankBins.set('472348', promsvyazLogo);
allBankBins.set('476206', promsvyazLogo);
allBankBins.set('476207', promsvyazLogo);
allBankBins.set('476208', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('515848', promsvyazLogo);
allBankBins.set('516080', promsvyazLogo);
allBankBins.set('516473', promsvyazLogo);
allBankBins.set('518586', promsvyazLogo);
allBankBins.set('518876', promsvyazLogo);
allBankBins.set('518946', promsvyazLogo);
allBankBins.set('518970', promsvyazLogo);
allBankBins.set('520085', promsvyazLogo);
allBankBins.set('520088', promsvyazLogo);
allBankBins.set('520373', promsvyazLogo);
allBankBins.set('521124', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('523688', promsvyazLogo);
allBankBins.set('523942', promsvyazLogo);
allBankBins.set('525494', promsvyazLogo);
allBankBins.set('526280', promsvyazLogo);
allBankBins.set('526868', promsvyazLogo);
allBankBins.set('529160', promsvyazLogo);
allBankBins.set('530441', promsvyazLogo);
allBankBins.set('531534', promsvyazLogo);
allBankBins.set('531943', promsvyazLogo);
allBankBins.set('532421', promsvyazLogo);
allBankBins.set('534462', promsvyazLogo);
allBankBins.set('534495', promsvyazLogo);
allBankBins.set('535023', promsvyazLogo);
allBankBins.set('535058', promsvyazLogo);
allBankBins.set('539621', promsvyazLogo);
allBankBins.set('539704', promsvyazLogo);
allBankBins.set('539861', promsvyazLogo);
allBankBins.set('541269', promsvyazLogo);
allBankBins.set('542340', promsvyazLogo);
allBankBins.set('543874', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('544800', promsvyazLogo);
allBankBins.set('545350', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('546766', promsvyazLogo);
allBankBins.set('547329', promsvyazLogo);
allBankBins.set('548172', promsvyazLogo);
allBankBins.set('548429', promsvyazLogo);
allBankBins.set('549425', promsvyazLogo);
allBankBins.set('549439', promsvyazLogo);
allBankBins.set('549524', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('554279', promsvyazLogo);
allBankBins.set('554759', promsvyazLogo);
allBankBins.set('5547590', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('554781', promsvyazLogo);
allBankBins.set('5547810', promsvyazLogo);
allBankBins.set('5547817', promsvyazLogo);
allBankBins.set('5547818', promsvyazLogo);
allBankBins.set('557981', promsvyazLogo);
allBankBins.set('5582540', promsvyazLogo);
allBankBins.set('***********', promsvyazLogo);
allBankBins.set('***********', promsvyazLogo);
allBankBins.set('558268', promsvyazLogo);
allBankBins.set('558516', promsvyazLogo);
allBankBins.set('558672', promsvyazLogo);
allBankBins.set('********', promsvyazLogo);
allBankBins.set('670508', promsvyazLogo);
allBankBins.set('670583', promsvyazLogo);
allBankBins.set('670611', promsvyazLogo);
allBankBins.set('670654', promsvyazLogo);
allBankBins.set('676444', promsvyazLogo);
allBankBins.set('677209', promsvyazLogo);
allBankBins.set('677358', promsvyazLogo);
allBankBins.set('677370', promsvyazLogo);
allBankBins.set('677371', promsvyazLogo);
allBankBins.set('677461', promsvyazLogo);
allBankBins.set('677462', promsvyazLogo);
allBankBins.set('677506', promsvyazLogo);

// 'PUBLIC JOINT-STOCK COMPANY "BANK OTKRITIE FINANCIAL CORPORATION"'
var otkrytieLogo = {url: 'template/common/img/valday/bank-logo/otkrytie.svg', color: '#00BAE3', textColor: '#ffffff'};
allBankBins.set('416038', otkrytieLogo);
allBankBins.set('420574', otkrytieLogo);
allBankBins.set('425181', otkrytieLogo);
allBankBins.set('425182', otkrytieLogo);
allBankBins.set('425183', otkrytieLogo);
allBankBins.set('425184', otkrytieLogo);
allBankBins.set('425185', otkrytieLogo);
allBankBins.set('427856', otkrytieLogo);
allBankBins.set('428165', otkrytieLogo);
allBankBins.set('428166', otkrytieLogo);
allBankBins.set('428228', otkrytieLogo);
allBankBins.set('430291', otkrytieLogo);
allBankBins.set('434146', otkrytieLogo);
allBankBins.set('434147', otkrytieLogo);
allBankBins.set('434148', otkrytieLogo);
allBankBins.set('443884', otkrytieLogo);
allBankBins.set('443885', otkrytieLogo);
allBankBins.set('479715', otkrytieLogo);
allBankBins.set('479716', otkrytieLogo);
allBankBins.set('479718', otkrytieLogo);
allBankBins.set('485649', otkrytieLogo);
allBankBins.set('514017', otkrytieLogo);
allBankBins.set('514082', otkrytieLogo);
allBankBins.set('514515', otkrytieLogo);
allBankBins.set('515243', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('515668', otkrytieLogo);
allBankBins.set('515758', otkrytieLogo);
allBankBins.set('516354', otkrytieLogo);
allBankBins.set('516387', otkrytieLogo);
allBankBins.set('517508', otkrytieLogo);
allBankBins.set('**********', otkrytieLogo);
allBankBins.set('**********', otkrytieLogo);
allBankBins.set('518796', otkrytieLogo);
allBankBins.set('518803', otkrytieLogo);
allBankBins.set('520324', otkrytieLogo);
allBankBins.set('520349', otkrytieLogo);
allBankBins.set('520634', otkrytieLogo);
allBankBins.set('521386', otkrytieLogo);
allBankBins.set('522459', otkrytieLogo);
allBankBins.set('524429', otkrytieLogo);
allBankBins.set('524838', otkrytieLogo);
allBankBins.set('525719', otkrytieLogo);
allBankBins.set('5257840', otkrytieLogo);
allBankBins.set('5257842', otkrytieLogo);
allBankBins.set('5257843', otkrytieLogo);
allBankBins.set('525828', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('529260', otkrytieLogo);
allBankBins.set('529294', otkrytieLogo);
allBankBins.set('530183', otkrytieLogo);
allBankBins.set('530403', otkrytieLogo);
allBankBins.set('531305', otkrytieLogo);
allBankBins.set('531674', otkrytieLogo);
allBankBins.set('532130', otkrytieLogo);
allBankBins.set('532301', otkrytieLogo);
allBankBins.set('532837', otkrytieLogo);
allBankBins.set('534469', otkrytieLogo);
allBankBins.set('534661', otkrytieLogo);
allBankBins.set('534669', otkrytieLogo);
allBankBins.set('535027', otkrytieLogo);
allBankBins.set('535108', otkrytieLogo);
allBankBins.set('536095', otkrytieLogo);
allBankBins.set('536825', otkrytieLogo);
allBankBins.set('539714', otkrytieLogo);
allBankBins.set('539718', otkrytieLogo);
allBankBins.set('539896', otkrytieLogo);
allBankBins.set('542289', otkrytieLogo);
allBankBins.set('542475', otkrytieLogo);
allBankBins.set('542501', otkrytieLogo);
allBankBins.set('543019', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('544092', otkrytieLogo);
allBankBins.set('544218', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('544343', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('544499', otkrytieLogo);
allBankBins.set('544573', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('544901', otkrytieLogo);
allBankBins.set('544962', otkrytieLogo);
allBankBins.set('545200', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('5471140', otkrytieLogo);
allBankBins.set('5471142', otkrytieLogo);
allBankBins.set('547286', otkrytieLogo);
allBankBins.set('547449', otkrytieLogo);
allBankBins.set('547616', otkrytieLogo);
allBankBins.set('548106', otkrytieLogo);
allBankBins.set('548688', otkrytieLogo);
allBankBins.set('548764', otkrytieLogo);
allBankBins.set('549024', otkrytieLogo);
allBankBins.set('549025', otkrytieLogo);
allBankBins.set('549848', otkrytieLogo);
allBankBins.set('550446', otkrytieLogo);
allBankBins.set('552219', otkrytieLogo);
allBankBins.set('552671', otkrytieLogo);
allBankBins.set('552681', otkrytieLogo);
allBankBins.set('552708', otkrytieLogo);
allBankBins.set('552721', otkrytieLogo);
allBankBins.set('557946', otkrytieLogo);
allBankBins.set('557948', otkrytieLogo);
allBankBins.set('558620', otkrytieLogo);
allBankBins.set('558675', otkrytieLogo);
allBankBins.set('559264', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('********', otkrytieLogo);
allBankBins.set('670518', otkrytieLogo);
allBankBins.set('670587', otkrytieLogo);
allBankBins.set('670847', otkrytieLogo);
allBankBins.set('676231', otkrytieLogo);
allBankBins.set('676697', otkrytieLogo);
allBankBins.set('676877', otkrytieLogo);
allBankBins.set('676951', otkrytieLogo);
allBankBins.set('676968', otkrytieLogo);
allBankBins.set('676996', otkrytieLogo);
allBankBins.set('677268', otkrytieLogo);
allBankBins.set('677390', otkrytieLogo);
allBankBins.set('677392', otkrytieLogo);
allBankBins.set('677425', otkrytieLogo);
allBankBins.set('677512', otkrytieLogo);

// 'SBERBANK OF RUSSIA'
var sberbankLogo = {url: 'template/common/img/valday/bank-logo/sberbank.svg', color: '#006533', textColor: '#ffffff'};
allBankBins.set('417398', sberbankLogo);
allBankBins.set('424428', sberbankLogo);
allBankBins.set('424490', sberbankLogo);
allBankBins.set('424491', sberbankLogo);
allBankBins.set('424492', sberbankLogo);
allBankBins.set('427404', sberbankLogo);
allBankBins.set('427420', sberbankLogo);
allBankBins.set('427427', sberbankLogo);
allBankBins.set('427428', sberbankLogo);
allBankBins.set('427430', sberbankLogo);
allBankBins.set('427431', sberbankLogo);
allBankBins.set('427432', sberbankLogo);
allBankBins.set('427433', sberbankLogo);
allBankBins.set('427436', sberbankLogo);
allBankBins.set('427438', sberbankLogo);
allBankBins.set('427440', sberbankLogo);
allBankBins.set('427442', sberbankLogo);
allBankBins.set('427444', sberbankLogo);
allBankBins.set('427445', sberbankLogo);
allBankBins.set('427446', sberbankLogo);
allBankBins.set('427449', sberbankLogo);
allBankBins.set('427450', sberbankLogo);
allBankBins.set('427451', sberbankLogo);
allBankBins.set('427452', sberbankLogo);
allBankBins.set('427453', sberbankLogo);
allBankBins.set('427454', sberbankLogo);
allBankBins.set('427455', sberbankLogo);
allBankBins.set('427456', sberbankLogo);
allBankBins.set('427459', sberbankLogo);
allBankBins.set('427460', sberbankLogo);
allBankBins.set('427462', sberbankLogo);
allBankBins.set('427466', sberbankLogo);
allBankBins.set('427467', sberbankLogo);
allBankBins.set('427468', sberbankLogo);
allBankBins.set('427470', sberbankLogo);
allBankBins.set('427472', sberbankLogo);
allBankBins.set('427475', sberbankLogo);
allBankBins.set('427477', sberbankLogo);
allBankBins.set('427600', sberbankLogo);
allBankBins.set('427601', sberbankLogo);
allBankBins.set('427602', sberbankLogo);
allBankBins.set('427603', sberbankLogo);
allBankBins.set('427604', sberbankLogo);
allBankBins.set('427605', sberbankLogo);
allBankBins.set('427606', sberbankLogo);
allBankBins.set('427607', sberbankLogo);
allBankBins.set('427608', sberbankLogo);
allBankBins.set('427609', sberbankLogo);
allBankBins.set('427610', sberbankLogo);
allBankBins.set('427611', sberbankLogo);
allBankBins.set('427612', sberbankLogo);
allBankBins.set('427613', sberbankLogo);
allBankBins.set('427616', sberbankLogo);
allBankBins.set('427617', sberbankLogo);
allBankBins.set('427618', sberbankLogo);
allBankBins.set('427620', sberbankLogo);
allBankBins.set('427622', sberbankLogo);
allBankBins.set('427625', sberbankLogo);
allBankBins.set('427626', sberbankLogo);
allBankBins.set('427627', sberbankLogo);
allBankBins.set('427628', sberbankLogo);
allBankBins.set('427629', sberbankLogo);
allBankBins.set('427630', sberbankLogo);
allBankBins.set('427631', sberbankLogo);
allBankBins.set('427632', sberbankLogo);
allBankBins.set('427633', sberbankLogo);
allBankBins.set('427635', sberbankLogo);
allBankBins.set('427636', sberbankLogo);
allBankBins.set('427637', sberbankLogo);
allBankBins.set('427638', sberbankLogo);
allBankBins.set('427639', sberbankLogo);
allBankBins.set('427640', sberbankLogo);
allBankBins.set('427641', sberbankLogo);
allBankBins.set('427642', sberbankLogo);
allBankBins.set('427643', sberbankLogo);
allBankBins.set('427644', sberbankLogo);
allBankBins.set('427645', sberbankLogo);
allBankBins.set('427646', sberbankLogo);
allBankBins.set('427648', sberbankLogo);
allBankBins.set('427649', sberbankLogo);
allBankBins.set('427650', sberbankLogo);
allBankBins.set('427651', sberbankLogo);
allBankBins.set('427652', sberbankLogo);
allBankBins.set('427653', sberbankLogo);
allBankBins.set('427654', sberbankLogo);
allBankBins.set('427655', sberbankLogo);
allBankBins.set('427656', sberbankLogo);
allBankBins.set('427659', sberbankLogo);
allBankBins.set('427660', sberbankLogo);
allBankBins.set('427661', sberbankLogo);
allBankBins.set('427662', sberbankLogo);
allBankBins.set('427663', sberbankLogo);
allBankBins.set('427664', sberbankLogo);
allBankBins.set('427666', sberbankLogo);
allBankBins.set('427667', sberbankLogo);
allBankBins.set('427668', sberbankLogo);
allBankBins.set('427669', sberbankLogo);
allBankBins.set('427670', sberbankLogo);
allBankBins.set('427672', sberbankLogo);
allBankBins.set('427674', sberbankLogo);
allBankBins.set('427675', sberbankLogo);
allBankBins.set('427676', sberbankLogo);
allBankBins.set('427677', sberbankLogo);
allBankBins.set('427680', sberbankLogo);
allBankBins.set('427681', sberbankLogo);
allBankBins.set('427682', sberbankLogo);
allBankBins.set('427683', sberbankLogo);
allBankBins.set('427684', sberbankLogo);
allBankBins.set('427685', sberbankLogo);
allBankBins.set('427686', sberbankLogo);
allBankBins.set('427687', sberbankLogo);
allBankBins.set('427688', sberbankLogo);
allBankBins.set('427689', sberbankLogo);
allBankBins.set('427699', sberbankLogo);
allBankBins.set('427901', sberbankLogo);
allBankBins.set('427902', sberbankLogo);
allBankBins.set('427903', sberbankLogo);
allBankBins.set('427904', sberbankLogo);
allBankBins.set('427905', sberbankLogo);
allBankBins.set('427906', sberbankLogo);
allBankBins.set('427907', sberbankLogo);
allBankBins.set('427908', sberbankLogo);
allBankBins.set('427909', sberbankLogo);
allBankBins.set('427910', sberbankLogo);
allBankBins.set('427911', sberbankLogo);
allBankBins.set('427912', sberbankLogo);
allBankBins.set('427913', sberbankLogo);
allBankBins.set('427916', sberbankLogo);
allBankBins.set('427917', sberbankLogo);
allBankBins.set('427918', sberbankLogo);
allBankBins.set('427920', sberbankLogo);
allBankBins.set('427922', sberbankLogo);
allBankBins.set('427925', sberbankLogo);
allBankBins.set('427926', sberbankLogo);
allBankBins.set('427927', sberbankLogo);
allBankBins.set('427928', sberbankLogo);
allBankBins.set('427929', sberbankLogo);
allBankBins.set('427930', sberbankLogo);
allBankBins.set('427931', sberbankLogo);
allBankBins.set('427932', sberbankLogo);
allBankBins.set('427933', sberbankLogo);
allBankBins.set('427935', sberbankLogo);
allBankBins.set('427936', sberbankLogo);
allBankBins.set('427937', sberbankLogo);
allBankBins.set('427938', sberbankLogo);
allBankBins.set('427939', sberbankLogo);
allBankBins.set('427940', sberbankLogo);
allBankBins.set('427941', sberbankLogo);
allBankBins.set('427942', sberbankLogo);
allBankBins.set('427943', sberbankLogo);
allBankBins.set('427944', sberbankLogo);
allBankBins.set('427945', sberbankLogo);
allBankBins.set('427946', sberbankLogo);
allBankBins.set('427948', sberbankLogo);
allBankBins.set('427949', sberbankLogo);
allBankBins.set('427950', sberbankLogo);
allBankBins.set('427951', sberbankLogo);
allBankBins.set('427952', sberbankLogo);
allBankBins.set('427953', sberbankLogo);
allBankBins.set('427954', sberbankLogo);
allBankBins.set('427955', sberbankLogo);
allBankBins.set('427956', sberbankLogo);
allBankBins.set('427959', sberbankLogo);
allBankBins.set('427960', sberbankLogo);
allBankBins.set('427961', sberbankLogo);
allBankBins.set('427962', sberbankLogo);
allBankBins.set('427963', sberbankLogo);
allBankBins.set('427964', sberbankLogo);
allBankBins.set('427966', sberbankLogo);
allBankBins.set('427967', sberbankLogo);
allBankBins.set('427968', sberbankLogo);
allBankBins.set('427969', sberbankLogo);
allBankBins.set('427970', sberbankLogo);
allBankBins.set('427972', sberbankLogo);
allBankBins.set('427974', sberbankLogo);
allBankBins.set('427975', sberbankLogo);
allBankBins.set('427977', sberbankLogo);
allBankBins.set('427999', sberbankLogo);
allBankBins.set('437435', sberbankLogo);
allBankBins.set('437518', sberbankLogo);
allBankBins.set('452401', sberbankLogo);
allBankBins.set('452434', sberbankLogo);
allBankBins.set('452435', sberbankLogo);
allBankBins.set('473838', sberbankLogo);
allBankBins.set('481776', sberbankLogo);
allBankBins.set('485463', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('515627', sberbankLogo);
allBankBins.set('515842', sberbankLogo);
allBankBins.set('516836', sberbankLogo);
allBankBins.set('516837', sberbankLogo);
allBankBins.set('516979', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('522860', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('527576', sberbankLogo);
allBankBins.set('530180', sberbankLogo);
allBankBins.set('531310', sberbankLogo);
allBankBins.set('531694', sberbankLogo);
allBankBins.set('532424', sberbankLogo);
allBankBins.set('532483', sberbankLogo);
allBankBins.set('533205', sberbankLogo);
allBankBins.set('533669', sberbankLogo);
allBankBins.set('535541', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('542577', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('54257744', sberbankLogo);
allBankBins.set('54257747', sberbankLogo);
allBankBins.set('54257748', sberbankLogo);
allBankBins.set('54257753', sberbankLogo);
allBankBins.set('54257756', sberbankLogo);
allBankBins.set('54257757', sberbankLogo);
allBankBins.set('54257767', sberbankLogo);
allBankBins.set('54257775', sberbankLogo);
allBankBins.set('54257778', sberbankLogo);
allBankBins.set('54257780', sberbankLogo);
allBankBins.set('54257783', sberbankLogo);
allBankBins.set('54257790', sberbankLogo);
allBankBins.set('54257791', sberbankLogo);
allBankBins.set('54257794', sberbankLogo);
allBankBins.set('542762', sberbankLogo);
allBankBins.set('54312110', sberbankLogo);
allBankBins.set('54372113', sberbankLogo);
allBankBins.set('54408383', sberbankLogo);
allBankBins.set('54433102', sberbankLogo);
allBankBins.set('54433107', sberbankLogo);
allBankBins.set('54433112', sberbankLogo);
allBankBins.set('54433121', sberbankLogo);
allBankBins.set('54433127', sberbankLogo);
allBankBins.set('54433128', sberbankLogo);
allBankBins.set('54433131', sberbankLogo);
allBankBins.set('54433140', sberbankLogo);
allBankBins.set('54433146', sberbankLogo);
allBankBins.set('54433154', sberbankLogo);
allBankBins.set('54433155', sberbankLogo);
allBankBins.set('54433157', sberbankLogo);
allBankBins.set('54433158', sberbankLogo);
allBankBins.set('54433161', sberbankLogo);
allBankBins.set('54433164', sberbankLogo);
allBankBins.set('54433165', sberbankLogo);
allBankBins.set('54433168', sberbankLogo);
allBankBins.set('54433169', sberbankLogo);
allBankBins.set('54433170', sberbankLogo);
allBankBins.set('54433172', sberbankLogo);
allBankBins.set('54433176', sberbankLogo);
allBankBins.set('54433177', sberbankLogo);
allBankBins.set('54433183', sberbankLogo);
allBankBins.set('54433189', sberbankLogo);
allBankBins.set('54433197', sberbankLogo);
allBankBins.set('544379', sberbankLogo);
allBankBins.set('54455948', sberbankLogo);
allBankBins.set('54458413', sberbankLogo);
allBankBins.set('54462683', sberbankLogo);
allBankBins.set('54469066', sberbankLogo);
allBankBins.set('54475685', sberbankLogo);
allBankBins.set('54480685', sberbankLogo);
allBankBins.set('545152', sberbankLogo);
allBankBins.set('54544218', sberbankLogo);
allBankBins.set('54560105', sberbankLogo);
allBankBins.set('54560148', sberbankLogo);
allBankBins.set('54584000', sberbankLogo);
allBankBins.set('54584002', sberbankLogo);
allBankBins.set('54584008', sberbankLogo);
allBankBins.set('54584014', sberbankLogo);
allBankBins.set('54584020', sberbankLogo);
allBankBins.set('54584026', sberbankLogo);
allBankBins.set('54584028', sberbankLogo);
allBankBins.set('54584037', sberbankLogo);
allBankBins.set('54584039', sberbankLogo);
allBankBins.set('54584045', sberbankLogo);
allBankBins.set('54584052', sberbankLogo);
allBankBins.set('54584053', sberbankLogo);
allBankBins.set('54584054', sberbankLogo);
allBankBins.set('54584055', sberbankLogo);
allBankBins.set('54584062', sberbankLogo);
allBankBins.set('54584064', sberbankLogo);
allBankBins.set('54584068', sberbankLogo);
allBankBins.set('54584071', sberbankLogo);
allBankBins.set('54584077', sberbankLogo);
allBankBins.set('54584079', sberbankLogo);
allBankBins.set('54584081', sberbankLogo);
allBankBins.set('54584089', sberbankLogo);
allBankBins.set('54584092', sberbankLogo);
allBankBins.set('54584094', sberbankLogo);
allBankBins.set('54584095', sberbankLogo);
allBankBins.set('54584096', sberbankLogo);
allBankBins.set('54584098', sberbankLogo);
allBankBins.set('54591553', sberbankLogo);
allBankBins.set('54603104', sberbankLogo);
allBankBins.set('54603107', sberbankLogo);
allBankBins.set('54603115', sberbankLogo);
allBankBins.set('54603129', sberbankLogo);
allBankBins.set('54603134', sberbankLogo);
allBankBins.set('54603136', sberbankLogo);
allBankBins.set('54603143', sberbankLogo);
allBankBins.set('54603144', sberbankLogo);
allBankBins.set('54603149', sberbankLogo);
allBankBins.set('54603150', sberbankLogo);
allBankBins.set('54603154', sberbankLogo);
allBankBins.set('54603158', sberbankLogo);
allBankBins.set('54603159', sberbankLogo);
allBankBins.set('54603163', sberbankLogo);
allBankBins.set('54603168', sberbankLogo);
allBankBins.set('54603171', sberbankLogo);
allBankBins.set('54603175', sberbankLogo);
allBankBins.set('54603183', sberbankLogo);
allBankBins.set('54603186', sberbankLogo);
allBankBins.set('54603191', sberbankLogo);
allBankBins.set('54603199', sberbankLogo);
allBankBins.set('54608454', sberbankLogo);
allBankBins.set('546901', sberbankLogo);
allBankBins.set('546902', sberbankLogo);
allBankBins.set('546903', sberbankLogo);
allBankBins.set('546904', sberbankLogo);
allBankBins.set('546905', sberbankLogo);
allBankBins.set('546906', sberbankLogo);
allBankBins.set('546907', sberbankLogo);
allBankBins.set('546908', sberbankLogo);
allBankBins.set('546909', sberbankLogo);
allBankBins.set('546910', sberbankLogo);
allBankBins.set('546911', sberbankLogo);
allBankBins.set('546912', sberbankLogo);
allBankBins.set('546913', sberbankLogo);
allBankBins.set('546916', sberbankLogo);
allBankBins.set('546917', sberbankLogo);
allBankBins.set('546918', sberbankLogo);
allBankBins.set('546920', sberbankLogo);
allBankBins.set('546922', sberbankLogo);
allBankBins.set('546925', sberbankLogo);
allBankBins.set('546926', sberbankLogo);
allBankBins.set('546927', sberbankLogo);
allBankBins.set('546928', sberbankLogo);
allBankBins.set('546929', sberbankLogo);
allBankBins.set('546930', sberbankLogo);
allBankBins.set('546931', sberbankLogo);
allBankBins.set('546932', sberbankLogo);
allBankBins.set('546933', sberbankLogo);
allBankBins.set('546935', sberbankLogo);
allBankBins.set('546936', sberbankLogo);
allBankBins.set('546937', sberbankLogo);
allBankBins.set('546938', sberbankLogo);
allBankBins.set('546939', sberbankLogo);
allBankBins.set('546940', sberbankLogo);
allBankBins.set('546941', sberbankLogo);
allBankBins.set('546942', sberbankLogo);
allBankBins.set('546943', sberbankLogo);
allBankBins.set('546944', sberbankLogo);
allBankBins.set('546945', sberbankLogo);
allBankBins.set('546946', sberbankLogo);
allBankBins.set('546947', sberbankLogo);
allBankBins.set('546948', sberbankLogo);
allBankBins.set('546949', sberbankLogo);
allBankBins.set('546950', sberbankLogo);
allBankBins.set('546951', sberbankLogo);
allBankBins.set('546952', sberbankLogo);
allBankBins.set('546953', sberbankLogo);
allBankBins.set('546954', sberbankLogo);
allBankBins.set('546955', sberbankLogo);
allBankBins.set('546956', sberbankLogo);
allBankBins.set('546959', sberbankLogo);
allBankBins.set('546960', sberbankLogo);
allBankBins.set('546961', sberbankLogo);
allBankBins.set('546962', sberbankLogo);
allBankBins.set('546963', sberbankLogo);
allBankBins.set('546964', sberbankLogo);
allBankBins.set('546966', sberbankLogo);
allBankBins.set('546967', sberbankLogo);
allBankBins.set('546968', sberbankLogo);
allBankBins.set('546969', sberbankLogo);
allBankBins.set('546970', sberbankLogo);
allBankBins.set('546972', sberbankLogo);
allBankBins.set('546974', sberbankLogo);
allBankBins.set('546975', sberbankLogo);
allBankBins.set('546976', sberbankLogo);
allBankBins.set('546977', sberbankLogo);
allBankBins.set('546998', sberbankLogo);
allBankBins.set('546999', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('547844', sberbankLogo);
allBankBins.set('547901', sberbankLogo);
allBankBins.set('547902', sberbankLogo);
allBankBins.set('547905', sberbankLogo);
allBankBins.set('547906', sberbankLogo);
allBankBins.set('547907', sberbankLogo);
allBankBins.set('547910', sberbankLogo);
allBankBins.set('547911', sberbankLogo);
allBankBins.set('547912', sberbankLogo);
allBankBins.set('547913', sberbankLogo);
allBankBins.set('547920', sberbankLogo);
allBankBins.set('547922', sberbankLogo);
allBankBins.set('547925', sberbankLogo);
allBankBins.set('547926', sberbankLogo);
allBankBins.set('547927', sberbankLogo);
allBankBins.set('547928', sberbankLogo);
allBankBins.set('547930', sberbankLogo);
allBankBins.set('547931', sberbankLogo);
allBankBins.set('547932', sberbankLogo);
allBankBins.set('547935', sberbankLogo);
allBankBins.set('547936', sberbankLogo);
allBankBins.set('547938', sberbankLogo);
allBankBins.set('547940', sberbankLogo);
allBankBins.set('547942', sberbankLogo);
allBankBins.set('547943', sberbankLogo);
allBankBins.set('547944', sberbankLogo);
allBankBins.set('547945', sberbankLogo);
allBankBins.set('547947', sberbankLogo);
allBankBins.set('547948', sberbankLogo);
allBankBins.set('547949', sberbankLogo);
allBankBins.set('547950', sberbankLogo);
allBankBins.set('547951', sberbankLogo);
allBankBins.set('547952', sberbankLogo);
allBankBins.set('547953', sberbankLogo);
allBankBins.set('547954', sberbankLogo);
allBankBins.set('547955', sberbankLogo);
allBankBins.set('547956', sberbankLogo);
allBankBins.set('547959', sberbankLogo);
allBankBins.set('547960', sberbankLogo);
allBankBins.set('547961', sberbankLogo);
allBankBins.set('547962', sberbankLogo);
allBankBins.set('547966', sberbankLogo);
allBankBins.set('547969', sberbankLogo);
allBankBins.set('547972', sberbankLogo);
allBankBins.set('547976', sberbankLogo);
allBankBins.set('547998', sberbankLogo);
allBankBins.set('547999', sberbankLogo);
allBankBins.set('548401', sberbankLogo);
allBankBins.set('548402', sberbankLogo);
allBankBins.set('548403', sberbankLogo);
allBankBins.set('548405', sberbankLogo);
allBankBins.set('548406', sberbankLogo);
allBankBins.set('548407', sberbankLogo);
allBankBins.set('548410', sberbankLogo);
allBankBins.set('548411', sberbankLogo);
allBankBins.set('548412', sberbankLogo);
allBankBins.set('548413', sberbankLogo);
allBankBins.set('548416', sberbankLogo);
allBankBins.set('548420', sberbankLogo);
allBankBins.set('548422', sberbankLogo);
allBankBins.set('548425', sberbankLogo);
allBankBins.set('548426', sberbankLogo);
allBankBins.set('548427', sberbankLogo);
allBankBins.set('548428', sberbankLogo);
allBankBins.set('548430', sberbankLogo);
allBankBins.set('548431', sberbankLogo);
allBankBins.set('548432', sberbankLogo);
allBankBins.set('548435', sberbankLogo);
allBankBins.set('548436', sberbankLogo);
allBankBins.set('548438', sberbankLogo);
allBankBins.set('548440', sberbankLogo);
allBankBins.set('548442', sberbankLogo);
allBankBins.set('548443', sberbankLogo);
allBankBins.set('548444', sberbankLogo);
allBankBins.set('548445', sberbankLogo);
allBankBins.set('548447', sberbankLogo);
allBankBins.set('548448', sberbankLogo);
allBankBins.set('548449', sberbankLogo);
allBankBins.set('548450', sberbankLogo);
allBankBins.set('548451', sberbankLogo);
allBankBins.set('548452', sberbankLogo);
allBankBins.set('548454', sberbankLogo);
allBankBins.set('548455', sberbankLogo);
allBankBins.set('548456', sberbankLogo);
allBankBins.set('548459', sberbankLogo);
allBankBins.set('548460', sberbankLogo);
allBankBins.set('548461', sberbankLogo);
allBankBins.set('548462', sberbankLogo);
allBankBins.set('548463', sberbankLogo);
allBankBins.set('548464', sberbankLogo);
allBankBins.set('548466', sberbankLogo);
allBankBins.set('548468', sberbankLogo);
allBankBins.set('548469', sberbankLogo);
allBankBins.set('548470', sberbankLogo);
allBankBins.set('548472', sberbankLogo);
allBankBins.set('548476', sberbankLogo);
allBankBins.set('548477', sberbankLogo);
allBankBins.set('548498', sberbankLogo);
allBankBins.set('548499', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('557000', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('********', sberbankLogo);
allBankBins.set('639002', sberbankLogo);
allBankBins.set('676195', sberbankLogo);
allBankBins.set('676196', sberbankLogo);
allBankBins.set('676280', sberbankLogo);
allBankBins.set('677128', sberbankLogo);
allBankBins.set('677650', sberbankLogo);

/*allBankBins.set('410462', 'ST. PETERSBURG SOCIAL COMMERCIAL BANK CJSC');
 allBankBins.set('422753', 'ST. PETERSBURG SOCIAL COMMERCIAL BANK CJSC');
 allBankBins.set('429429', 'ST. PETERSBURG SOCIAL COMMERCIAL BANK CJSC');
 allBankBins.set('429430', 'ST. PETERSBURG SOCIAL COMMERCIAL BANK CJSC');
 allBankBins.set('429431', 'ST. PETERSBURG SOCIAL COMMERCIAL BANK CJSC');
 allBankBins.set('429432', 'ST. PETERSBURG SOCIAL COMMERCIAL BANK CJSC');*/

// 'TINKOFF BANK'
var tinkoffLogo = {url: 'template/common/img/valday/bank-logo/tinkoff.svg', color: '#000000', textColor: '#ffffff'};
allBankBins.set('437772', tinkoffLogo);
allBankBins.set('437773', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('***********', tinkoffLogo);
allBankBins.set('***********', tinkoffLogo);
allBankBins.set('***********', tinkoffLogo);
allBankBins.set('***********', tinkoffLogo);
allBankBins.set('***********', tinkoffLogo);
allBankBins.set('521324', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('524468', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('548387', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('551960', tinkoffLogo);
allBankBins.set('553420', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('*********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);
allBankBins.set('********', tinkoffLogo);

// 'VTB BANK (PUBLIC JOINT-STOCK COMPANY)'
var vtbLogo = {url: 'template/common/img/valday/bank-logo/vtb.svg', color: '#09397C', textColor: '#ffffff'};
allBankBins.set('405676', vtbLogo);
allBankBins.set('413087', vtbLogo);
allBankBins.set('418868', vtbLogo);
allBankBins.set('418869', vtbLogo);
allBankBins.set('418870', vtbLogo);
allBankBins.set('418873', vtbLogo);
allBankBins.set('421191', vtbLogo);
allBankBins.set('425154', vtbLogo);
allBankBins.set('426375', vtbLogo);
allBankBins.set('490809', vtbLogo);
allBankBins.set('510880', vtbLogo);
allBankBins.set('511738', vtbLogo);
allBankBins.set('511781', vtbLogo);
allBankBins.set('512019', vtbLogo);
allBankBins.set('512303', vtbLogo);
allBankBins.set('512338', vtbLogo);
allBankBins.set('512378', vtbLogo);
allBankBins.set('512450', vtbLogo);
allBankBins.set('512788', vtbLogo);
allBankBins.set('514908', vtbLogo);
allBankBins.set('514925', vtbLogo);
allBankBins.set('515775', vtbLogo);
allBankBins.set('515893', vtbLogo);
allBankBins.set('516081', vtbLogo);
allBankBins.set('516082', vtbLogo);
allBankBins.set('516084', vtbLogo);
allBankBins.set('516451', vtbLogo);
allBankBins.set('516570', vtbLogo);
allBankBins.set('516587', vtbLogo);
allBankBins.set('517373', vtbLogo);
allBankBins.set('517390', vtbLogo);
allBankBins.set('518033', vtbLogo);
allBankBins.set('522327', vtbLogo);
allBankBins.set('522329', vtbLogo);
allBankBins.set('522332', vtbLogo);
allBankBins.set('522582', vtbLogo);
allBankBins.set('522775', vtbLogo);
allBankBins.set('522876', vtbLogo);
allBankBins.set('523481', vtbLogo);
allBankBins.set('523943', vtbLogo);
allBankBins.set('524477', vtbLogo);
allBankBins.set('524833', vtbLogo);
allBankBins.set('524895', vtbLogo);
allBankBins.set('525773', vtbLogo);
allBankBins.set('526823', vtbLogo);
allBankBins.set('526857', vtbLogo);
allBankBins.set('526869', vtbLogo);
allBankBins.set('526870', vtbLogo);
allBankBins.set('526873', vtbLogo);
allBankBins.set('527317', vtbLogo);
allBankBins.set('527349', vtbLogo);
allBankBins.set('527652', vtbLogo);
allBankBins.set('527785', vtbLogo);
allBankBins.set('527798', vtbLogo);
allBankBins.set('528096', vtbLogo);
allBankBins.set('528236', vtbLogo);
allBankBins.set('528285', vtbLogo);
allBankBins.set('528528', vtbLogo);
allBankBins.set('528586', vtbLogo);
allBankBins.set('529168', vtbLogo);
allBankBins.set('529426', vtbLogo);
allBankBins.set('529438', vtbLogo);
allBankBins.set('529449', vtbLogo);
allBankBins.set('529460', vtbLogo);
allBankBins.set('529884', vtbLogo);
allBankBins.set('530176', vtbLogo);
allBankBins.set('530229', vtbLogo);
allBankBins.set('530554', vtbLogo);
allBankBins.set('530775', vtbLogo);
allBankBins.set('530974', vtbLogo);
allBankBins.set('531317', vtbLogo);
allBankBins.set('531452', vtbLogo);
allBankBins.set('531692', vtbLogo);
allBankBins.set('531866', vtbLogo);
allBankBins.set('531996', vtbLogo);
allBankBins.set('532187', vtbLogo);
allBankBins.set('532330', vtbLogo);
allBankBins.set('534135', vtbLogo);
allBankBins.set('534152', vtbLogo);
allBankBins.set('534245', vtbLogo);
allBankBins.set('534299', vtbLogo);
allBankBins.set('534493', vtbLogo);
allBankBins.set('534526', vtbLogo);
allBankBins.set('534601', vtbLogo);
allBankBins.set('535849', vtbLogo);
allBankBins.set('536244', vtbLogo);
allBankBins.set('536250', vtbLogo);
allBankBins.set('536370', vtbLogo);
allBankBins.set('536393', vtbLogo);
allBankBins.set('536456', vtbLogo);
allBankBins.set('536997', vtbLogo);
allBankBins.set('537735', vtbLogo);
allBankBins.set('538802', vtbLogo);
allBankBins.set('539730', vtbLogo);
allBankBins.set('540032', vtbLogo);
allBankBins.set('540217', vtbLogo);
allBankBins.set('540849', vtbLogo);
allBankBins.set('540915', vtbLogo);
allBankBins.set('541159', vtbLogo);
allBankBins.set('541162', vtbLogo);
allBankBins.set('541715', vtbLogo);
allBankBins.set('541723', vtbLogo);
allBankBins.set('541913', vtbLogo);
allBankBins.set('541917', vtbLogo);
allBankBins.set('542049', vtbLogo);
allBankBins.set('542093', vtbLogo);
allBankBins.set('542104', vtbLogo);
allBankBins.set('542593', vtbLogo);
allBankBins.set('542601', vtbLogo);
allBankBins.set('544261', vtbLogo);
allBankBins.set('544320', vtbLogo);
allBankBins.set('544380', vtbLogo);
allBankBins.set('546565', vtbLogo);
allBankBins.set('546717', vtbLogo);
allBankBins.set('546816', vtbLogo);
allBankBins.set('547209', vtbLogo);
allBankBins.set('547214', vtbLogo);
allBankBins.set('547236', vtbLogo);
allBankBins.set('547238', vtbLogo);
allBankBins.set('547656', vtbLogo);
allBankBins.set('547779', vtbLogo);
allBankBins.set('547958', vtbLogo);
allBankBins.set('548003', vtbLogo);
allBankBins.set('548218', vtbLogo);
allBankBins.set('548226', vtbLogo);
allBankBins.set('548256', vtbLogo);
allBankBins.set('548257', vtbLogo);
allBankBins.set('548332', vtbLogo);
allBankBins.set('548565', vtbLogo);
allBankBins.set('548706', vtbLogo);
allBankBins.set('548792', vtbLogo);
allBankBins.set('548837', vtbLogo);
allBankBins.set('548842', vtbLogo);
allBankBins.set('549309', vtbLogo);
allBankBins.set('550132', vtbLogo);
allBankBins.set('550225', vtbLogo);
allBankBins.set('550481', vtbLogo);
allBankBins.set('550482', vtbLogo);
allBankBins.set('552216', vtbLogo);
allBankBins.set('553054', vtbLogo);
allBankBins.set('553055', vtbLogo);
allBankBins.set('554362', vtbLogo);
allBankBins.set('554363', vtbLogo);
allBankBins.set('554384', vtbLogo);
allBankBins.set('557748', vtbLogo);
allBankBins.set('557869', vtbLogo);
allBankBins.set('558447', vtbLogo);
allBankBins.set('558481', vtbLogo);
allBankBins.set('558621', vtbLogo);
allBankBins.set('558629', vtbLogo);
allBankBins.set('559066', vtbLogo);
allBankBins.set('670889', vtbLogo);
allBankBins.set('671109', vtbLogo);
allBankBins.set('671111', vtbLogo);
allBankBins.set('671172', vtbLogo);
allBankBins.set('676171', vtbLogo);
allBankBins.set('676237', vtbLogo);
allBankBins.set('676351', vtbLogo);
allBankBins.set('676425', vtbLogo);
allBankBins.set('677006', vtbLogo);
allBankBins.set('677211', vtbLogo);
allBankBins.set('677213', vtbLogo);
allBankBins.set('677292', vtbLogo);
allBankBins.set('677394', vtbLogo);
allBankBins.set('677403', vtbLogo);
allBankBins.set('677428', vtbLogo);
allBankBins.set('677455', vtbLogo);
allBankBins.set('677543', vtbLogo);
allBankBins.set('677598', vtbLogo);
allBankBins.set('677599', vtbLogo);
allBankBins.set('677681', vtbLogo);
allBankBins.set('677696', vtbLogo);
allBankBins.set('677717', vtbLogo);

// 'VTB24 (PJSC)'
allBankBins.set('405888', vtbLogo);
allBankBins.set('406744', vtbLogo);
allBankBins.set('409398', vtbLogo);
allBankBins.set('411966', vtbLogo);
allBankBins.set('415028', vtbLogo);
allBankBins.set('415039', vtbLogo);
allBankBins.set('415040', vtbLogo);
allBankBins.set('415041', vtbLogo);
allBankBins.set('415042', vtbLogo);
allBankBins.set('427229', vtbLogo);
allBankBins.set('427230', vtbLogo);
allBankBins.set('427868', vtbLogo);
allBankBins.set('429565', vtbLogo);
allBankBins.set('429749', vtbLogo);
allBankBins.set('448331', vtbLogo);
allBankBins.set('448343', vtbLogo);
allBankBins.set('448344', vtbLogo);
allBankBins.set('448346', vtbLogo);
allBankBins.set('462235', vtbLogo);
allBankBins.set('464842', vtbLogo);
allBankBins.set('467058', vtbLogo);
allBankBins.set('471487', vtbLogo);
allBankBins.set('489347', vtbLogo);
allBankBins.set('489348', vtbLogo);
allBankBins.set('489349', vtbLogo);
allBankBins.set('489350', vtbLogo);
allBankBins.set('498629', vtbLogo);
allBankBins.set('510144', vtbLogo);
allBankBins.set('510176', vtbLogo);
allBankBins.set('510177', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('518591', vtbLogo);
allBankBins.set('518640', vtbLogo);
allBankBins.set('519304', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('522598', vtbLogo);
allBankBins.set('524305', vtbLogo);
allBankBins.set('527883', vtbLogo);
allBankBins.set('529938', vtbLogo);
allBankBins.set('530184', vtbLogo);
allBankBins.set('531233', vtbLogo);
allBankBins.set('535082', vtbLogo);
allBankBins.set('536519', vtbLogo);
allBankBins.set('536829', vtbLogo);
allBankBins.set('540814', vtbLogo);
allBankBins.set('540989', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('545224', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('545620', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('547553', vtbLogo);
allBankBins.set('549223', vtbLogo);
allBankBins.set('549270', vtbLogo);
allBankBins.set('549866', vtbLogo);
allBankBins.set('550251', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('552629', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('554386', vtbLogo);
allBankBins.set('554393', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('558518', vtbLogo);
allBankBins.set('********', vtbLogo);
allBankBins.set('676421', vtbLogo);
allBankBins.set('676595', vtbLogo);
allBankBins.set('676907', vtbLogo);

function getBankFromPan(cardNumber) {

    var cardPart = cardNumber.substring(0, 12);
    if (allBankBins.get(cardPart) != undefined) {
        return allBankBins.get(cardPart);
    }
    cardPart = cardNumber.substring(0, 11);
    if (allBankBins.get(cardPart) != undefined) {
        return allBankBins.get(cardPart);
    }
    cardPart = cardNumber.substring(0, 10);
    if (allBankBins.get(cardPart) != undefined) {
        return allBankBins.get(cardPart);
    }
    cardPart = cardNumber.substring(0, 9);
    if (allBankBins.get(cardPart) != undefined) {
        return allBankBins.get(cardPart);
    }
    cardPart = cardNumber.substring(0, 8);
    if (allBankBins.get(cardPart) != undefined) {
        return allBankBins.get(cardPart);
    }
    cardPart = cardNumber.substring(0, 6);
    if (allBankBins.get(cardPart) != undefined) {
        return allBankBins.get(cardPart);
    }
    return undefined;
}

