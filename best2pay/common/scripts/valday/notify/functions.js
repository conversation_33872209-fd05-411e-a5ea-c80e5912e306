$(document).ready(function () {

    function sendReceipt() {
        sendInProgress = true;

        emailData.email = $('#email-input').val();
        emailData.signature = $("#signature").val();

        $('#notify > *').remove();
        $('#notify').append(
            $('<div class="message-sending-status">')
                .append($('<img src="/webapi/template/common/img/valday/feedback-sending.svg" alt=""/>'))
                .append($('<h3>Сообщение<br/> отправляется</h3>'))
        )

        $.ajax({
            url: '/webapi/mailer/SendEmailNotify',
            data: emailData,
            method: 'post',
            dataType: 'json',
            success: function (data) {
                sendInProgress = false;

                if (data.success != undefined && data.success) {
                    $('#showemail-button').remove();
                    showSendResult('/webapi/template/common/img/valday/message-send-done.svg', 'Письмо с квитанцией отправлено');
                    if (data.resultUrls !== undefined && data.resultUrls != null && data.resultUrls != '') {
                        window.setTimeout(function () {
                            window.location.href = data.resultUrls;
                        }, 5000);
                    }
                } else {
                    showSendResult('/webapi/template/common/img/valday/message-error.svg', 'Произошла ошибка');
                }
            },
            error: function () {
                sendInProgress = false;
                showSendResult('/webapi/template/common/img/valday/message-error.svg', 'Произошла ошибка');
            }
        })
        return false;
    }

    /**
     * Показать форму отправки email
     */
    var sendInProgress = false;
    $('#showemail-button').on('click', function () {
        $('#content-data').hide();

        var sendEmail = $('<div id="notify">')
            .append($('<div id="notify-wrapper" class="content-wrapper">')
                .append(
                    $('<form id="notify-form" class="ordinal-form">')
                        .append($('<img src="/webapi/template/common/img/valday/message-send.svg" class="email-img" />'))
                        .append($('<div class="email-title">Отправка квитанции <br> на email </div>'))
                        .append(
                            $('<div class="input-field">')
                                .append($('<label>Email</label>'))
                                .append($('<input id="email-input" name="email" type="text" onkeypress="return checkEmail(event);" placeholder="<EMAIL>" autocomplete="off" required />'))
                        )
                        .append($('<div id="notify-actions">')
                            .append($('<div class="send-container">').append($('<button class="notify-button" type="submit" id="send-email-button">Отправить</button>')))
                            .append($('<div class="cancel-container">').append($('<a href="#" id="cancel-link" title="Отменить">Отменить</a>')))
                        )
                )
            )

        $('#content').append(sendEmail);
        $('#notify').fadeIn();

        $('#notify-form').validate({
            errorElement: "span",
            errorClass: 'has-error',
            errorPlacement: errorPlacementFn,
            ignore: ':hidden:not([class~=selectized]),:hidden > .selectized, .selectize-control .selectize-input input, .ignore-validation, .ignore-validation-runtime',
            success: function (error, element) {
                $(element).tooltip('destroy');
            },
            rules: {
                'email': {
                    validateEmail: true
                }
            },
            submitHandler: function (form) {
                return sendReceipt();
            }
        })
        return false;
    });

    function showSendResult(imgUrl, title, text) {

        $('#notify > div').remove();
        $('#notify')
            .append(
                $('<div class="message-sent-result">')
                    .append($('<img src="' + imgUrl + '" alt="" />'))
                    .append($('<div>').append($('<h3>').html(title)))
            );

        window.setTimeout(function () {
            hideSendResult();
        }, 2500);
    }

    $('#content').on('click', '#cancel-link', function () {
        hideSendResult();
        return false;
    })

    $('#content').on('keypress', '#email-input', function (e) {
        if (e.keyCode == 13) {
            return sendReceipt();
        }
    })

    function hideSendResult() {
        $('#notify').remove();
        $('#content-data').fadeIn();
    }

});