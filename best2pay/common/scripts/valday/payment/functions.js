$(document).ready(function () {

    $('#resolution').attr('value', screen.width + 'x' + screen.height);
    $('#time_zone_offset').attr('value', -(new Date().getTimezoneOffset()) / 60);
    $('#browser_language').attr('value', window.navigator.userLanguage || window.navigator.language);
    $('#bits').attr('value', screen.colorDepth);

    $.validator.addMethod("ownBins", function (value, element) {
        var val = value.replace(/\s/g, '');
        return !HAS_OWN_CARD || (val.length < 6 || (val.length > 6 && $.inArray(val.substr(0, 6), ownBins) != -1));
    }, "Указанная карта не соответствует категории карты, выбранной на первой странице");

    $('#cardFrom').rules('add', {
        validatePan: true,
        blackBins: true,
        ownBins: true,
        sectorBinsList1: true
    });
    if ($('#emailFrom') !== undefined && $('#emailFrom').length !== 0) {
        $('#emailFrom').rules('add', {
            validateEmail: true
        });
    }
    $('#cardDate').rules('add', {
        validateCardDate: true
    });
    $('#cvc').rules('add', {
        validateCVC: true
    });

    $('#description').hover(
        function () {
            $('#full-description').show()
        },
        function () {
            $('#full-description').hide()
        }
    )

    submitHandler = function (form) {

        //Проверяем на разрешение платежа для МПС
        var ps = cardType($('#cardFrom').val());
        if (notifies[ps] != undefined && notifies[ps]['ENABLE'] != undefined && notifies[ps]['ENABLE']) {
            alert(notifies[ps]['MESSAGE']);
            return false;
        }

        //Вызываем дополнительную функцию проверки. Если функция возвращает false - то выходим из обработчика отправки формы, не отправляя форму.
        if (customAdditionalSubmitHandler !== undefined && customAdditionalSubmitHandler(form) == false) {
            return false;
        }
        return true;
    };
});