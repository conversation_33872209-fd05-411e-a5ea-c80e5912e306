var CREDIT_ACC = '********';
var CARD_ACC = '********';
var HAS_SERVICE_TAX = undefined;
var needsRecals = undefined;
/*Фиксированная комиссия в копейках*/
var FIXED_TAX = 0;

var FIXED_TAX_OLD = 0;
var VisaTaxOld = tax['VISA'];
var MasterTaxOld = tax['MASTERCARD'];
var MirTaxOld = tax['MIR'];

function clearFee() {
    FIXED_TAX = 0;
    tax['VISA'] = '0.0';
    tax['MASTERCARD'] = '0.0';
    if (tax['MIR'] != undefined) {
        tax['MIR'] = '0.0';
    }
}

function restoreFee() {
    FIXED_TAX = FIXED_TAX_OLD;
    tax['VISA'] = VisaTaxOld;
    tax['MASTERCARD'] = MasterTaxOld;
    if (tax['MIR'] != undefined) {
        tax['MIR'] = MirTaxOld;
    }
}

function taxHandler() {
    /* Выводим комиссию только после нажатия кнопки "Рассчитать комиссию/Продолжить"*/
    if (HAS_SERVICE_TAX == true) {
        $('#submitButton').val('Рассчитать');
        clearFee();
        needsRecals = true;
    }
}

$(document).ready(function () {

    var ownCard = $('#ownCard');

    $.validator.addMethod("accountNumberCorrect", function (value, element) {
        return BIK == undefined || $(element).val().length == 20 && checkAccountCorrect($(element).val(), BIK);
    }, "Указан неправильный номер счета");
    $.validator.addMethod("accountNumberCredit", function (value, element) {
        return value == undefined || value == '' || /^********\d{0,12}$/.test(value);
    }, "Номер счета для погашения кредита должен начинаться с ********");
    $.validator.addMethod("accountNumberСard", function (value, element) {
        return value == undefined || value == '' || /^********\d{0,12}$/.test(value);
    }, "Номер счета для пополнения карты должен начинаться с ********");

    if ($('#accNumber') !== undefined && $('#accNumber').length > 0) {
        $('#accNumber').rules('add', {
            accountNumberLength: true,
            accountNumberCorrect: true
        })
    }
    if ($('#email') !== undefined && $('#email').length > 0) {
        $('#email').rules('add', {
            validateEmail: true
        })
    }
    if ($('#fio') !== undefined && $('#fio').length > 0) {
        $('#fio').rules('add', {
            validateFio: true
        })
    }
    $('#amountControl').rules('add', {
        limits: true,
        amount: true
    });

    /*
     * Обработчик переключателей "номер счета - номер договора"
     */
    $('input.payment-type-control').each(function () {
        var self = $(this);
        var accountNumber = $('#accNumber');

        function handler(control) {
            accountNumber.removeAttr('disabled');
            if (control.val() == 'credit') {
                $('#accNumber').rules('add', {
                    accountNumberCredit: true
                });
                $('#accNumber').rules('remove', 'accountNumberСard');
                accountNumber.focus().val(CREDIT_ACC).attr('disabled', false);
            } else {
                $('#accNumber').rules('add', {
                    accountNumberСard: true
                });
                $('#accNumber').rules('remove', 'accountNumberCredit');
                accountNumber.focus().val(CARD_ACC).attr('disabled', false);
            }
            accountNumber.parents('.form-group').addClass('active');
        }

        self.on('click', function () {
            handler($(this));
        });

        if (self.is(':checked')) {
            handler(self);
        }
    });

    ownCard.on('input', function () {
        $('.ps').trigger('input');
    });
    ownCard.on('click', function () {
        $('.ps').trigger('input');
    });

    function paymentSystem() {
        if ($("#cardVisa").is(':checked')) {
            return 'VISA';
        } else if ($("#cardMasterCard").is(':checked')) {
            return 'MASTERCARD';
        } else if ($("#cardMaestro").is(':checked')) {
            return 'MAESTRO';
        } else if ($("#cardMir").is(':checked')) {
            return 'MIR';
        } else if ($("#psCard").is(':checked')) {
            return 'VISA';
        } else if ($("#psWM").is(':checked')) {
            return 'WEBMONEY';
        } else if ($("#psYandex").is(':checked')) {
            return 'YANDEXDENGI';
        } else if ($("#psQiwi").is(':checked')) {
            return 'QIWI';
        }
    }

    $('.ps').each(function () {

        var self = $(this);

        function handler() {
            var amount = getFloat($('#amountControl').val().replace(',', '.'));

            var ps = paymentSystem();
            var ia = ps + '_IN_AMOUNT';
            var t = tax[ps];
            var a = trunc(amount.toFixed(2) * 100);
            var c;
            if (ownCard != undefined && ownCard.is(':checked')) {
                c = 0.0;
            } else {
                if (tax[ia] === true) {
                    var a2 = a / (1 - t / 100.0);
                    c = trunc(a2 - a);
                    if (c != 0 && c < FIXED_TAX) {
                        c = FIXED_TAX;
                    }
                } else {
                    c = trunc(a * (t / 100.0));
                    if (c != 0 && c < FIXED_TAX) {
                        c = FIXED_TAX;
                    }
                }
            }
            if (isNaN(c)) {
                $('#totalCommission').text('--');
                $('#totalAmount').text('--');
                $('#fee_kop').val('--');
            } else {
                $('#totalCommission').text((c / 100.0).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1 '));
                $('#totalAmount').text(((a + c) / 100.0).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1 '));
                $('#fee_kop').val(c);
            }
            $('#amount_kop').val(a);
        }

        self.on('input', function () {
            handler();
        }).trigger('input');

        self.click(function () {
            handler();
        });

        if (self.is(':checked')) {
            handler();
        }
    });

    /*
     * Обработчик заполнения поля Description
     */
    $('input.desc-render').each(function () {

        function descHandler() {
            var accountNumberControlEl = $('#accNumber');
            var contractControlEl = $('#contract');
            var totalAmountEl = $('#totalAmount');
            var commEl = $('#totalCommission');
            var amountAddition = '';
            if ((totalAmountEl === undefined || totalAmountEl.length == 0) && (commEl === undefined || commEl.length == 0)) {
                amountAddition = " в сумме " + $('#amountControl').val() + cur;
            } else {
                amountAddition = " в сумме " + totalAmountEl.text() + cur + " с комиссией " + commEl.text() + cur;
            }
            if (accountNumberControlEl != undefined && accountNumberControlEl.val() != undefined && accountNumberControlEl.val() != '') {
                $('#description').val('Перевод денежных средств на счет N' + accountNumberControlEl.val() + '. Получатель ' + $('#fio').val() + amountAddition);
            } else if (contractControlEl != undefined && contractControlEl.val() != undefined && contractControlEl.val() != '') {
                $('#description').val('Перевод денежных средств по договору N' + contractControlEl.val() + '. Получатель ' + $('#fio').val() + amountAddition);
            }
        }

        var self = $(this);

        self.on('input', function () {
            descHandler();
        }).trigger('input');

        self.click(function () {
            descHandler();
        });

        if (self.is(':checked')) {
            descHandler();
        }
    });

    /* Для прослушивания полей, от которых зависит размер комиссии - навешиваем на них особый слушатель*/
    $('.tax-dependency').on('input', function () {
        taxHandler();
    }).trigger('input');
});
