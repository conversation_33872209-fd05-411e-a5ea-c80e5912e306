# How to run Postgres & RabbitMQ for local development

In case if you don't want to install `postgres` and `rabbitmq` on your local machine.


## Prerequisites

- [Install the `docker`](https://docs.docker.com/get-docker/) on your local machine.
- Download and copy database dump into `[project-root]/docker/docker-entrypoint-initdb.d`

## Run servers

    cd [project-root]/docker
    docker-compose up -d
    # wait services to start
    docker-compose logs -f      # Ctrl+C to exit


If the dump file is not binary but plain SQL dump, then the database will execute the SQL.

### Import binary dump

    cd [project-root]/docker
    docker-compose exec postgres bash # should login to postgres shell
    pg_restore -U postgres -d oskelly /docker-entrypoint-initdb.d/[oskelly_dump_file_name.dump]
    

## Stop services

    cd [project-root]/docker
    docker-compose down

## Stop services and delete volumes (for a re-import)

    docker-compose down -v  # remove data volumes

## IDE Support
Both IntelliJ IDE & VS Code have plugins that allow quick a stop/start from IDE and viewing logs.

