version: '3.1'

services:

  postgres:
    image: postgres
    container_name: oskelly-dev-pg
    restart: unless-stopped
    environment:
      - POSTGRES_USER=root
      - POSTGRES_PASSWORD=password
    volumes:
      - ./postgres-db-init:/docker-entrypoint-initdb.d/
      - ./postgres:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: oskelly-dev-rbmq
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: oskelly
      RABBITMQ_DEFAULT_PASS: qwerty
    ports:
      - "5672:5672"
      - "15672:15672"

  mongo:
    image: mongo
    container_name: oskelly-dev-mongo
    restart: always
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=oskelly
      - MONGO_INITDB_ROOT_PASSWORD=qwerty
    volumes:
      - ./resources/mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro


  zookeeper:
    image: confluentinc/cp-zookeeper:7.6.0
    container_name: zookeeper
    ports:
      - 2181:2181
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      KAFKA_OPTS:
        -Djava.security.auth.login.config=/etc/kafka/secrets/sasl/zookeeper_jaas.conf
        -Dzookeeper.authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
        -Dzookeeper.requireClientAuthScheme=sasl
    volumes:
      - ./kafka:/etc/kafka/secrets/sasl

  zookeeper-add-oskelly2kafka-users:
    image: confluentinc/cp-kafka:7.6.0
    container_name: "zookeeper-add-oskelly2kafka-users"
    depends_on:
      - zookeeper
    command: "bash -c 'echo Waiting for Zookeeper to be ready... && \
      cub zk-ready zookeeper:2181 120 && \
      kafka-configs --zookeeper zookeeper:2181 --alter --add-config 'SCRAM-SHA-512=[iterations=4096,password=password]' --entity-type users --entity-name admin && \
      kafka-configs --zookeeper zookeeper:2181 --alter --add-config 'SCRAM-SHA-512=[iterations=4096,password=oskelly123]' --entity-type users --entity-name oskelly '"
    environment:
      KAFKA_BROKER_ID: ignored
      KAFKA_ZOOKEEPER_CONNECT: ignored
      KAFKA_OPTS: -Djava.security.auth.login.config=/etc/kafka/secrets/sasl/kafka_jaas.conf
    volumes:
      - ./kafka:/etc/kafka/secrets/sasl

  kafka-1:
    image: confluentinc/cp-kafka:7.6.0
    ports:
      - 9092:9092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://kafka-1:19092,LISTENER_DOCKER_EXTERNAL://${DOCKER_HOST_IP:-127.0.0.1}:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,LISTENER_DOCKER_EXTERNAL:SASL_PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
      KAFKA_SASL_ENABLED_MECHANISMS: SCRAM-SHA-512
      KAFKA_LOG4J_ROOT_LOGLEVEL: ERROR
      KAFKA_CONFLUENT_SUPPORT_METRICS_ENABLE: "false"
      KAFKA_ALLOW_EVERYONE_IF_NO_ACL_FOUND: "true"
      KAFKA_AUTHORIZER_CLASS_NAME: kafka.security.authorizer.AclAuthorizer
      KAFKA_SUPER_USERS: User:admin
      KAFKA_OPTS: -Djava.security.auth.login.config=/etc/kafka/secrets/kafka_jaas.conf
      KAFKA_CONFLUENT_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    depends_on:
      - zookeeper
      - zookeeper-add-oskelly2kafka-users
    volumes:
      - ./resources/docker:/etc/kafka/secrets

#  kafka-2:
#    image: confluentinc/cp-kafka:7.6.0
#    ports:
#      - 9093:9093
#    environment:
#      KAFKA_BROKER_ID: 2
#      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
#      KAFKA_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://kafka-2:19093,LISTENER_DOCKER_EXTERNAL://${DOCKER_HOST_IP:-127.0.0.1}:9093
#      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,LISTENER_DOCKER_EXTERNAL:SASL_PLAINTEXT
#      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
#      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
#      KAFKA_SASL_ENABLED_MECHANISMS: SCRAM-SHA-512
#      KAFKA_LOG4J_ROOT_LOGLEVEL: ERROR
#      KAFKA_CONFLUENT_SUPPORT_METRICS_ENABLE: "false"
#      KAFKA_ALLOW_EVERYONE_IF_NO_ACL_FOUND: "true"
#      KAFKA_AUTHORIZER_CLASS_NAME: kafka.security.authorizer.AclAuthorizer
#      KAFKA_SUPER_USERS: User:admin
#      KAFKA_OPTS: -Djava.security.auth.login.config=/etc/kafka/secrets/kafka_jaas.conf
#      KAFKA_CONFLUENT_TOPIC_REPLICATION_FACTOR: 1
#      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
#    depends_on:
#      - zookeeper
#      - zookeeper-add-oskelly2kafka-users
#    volumes:
#      - ./resources/docker:/etc/kafka/secrets
#
#  kafka-3:
#    image: confluentinc/cp-kafka:7.6.0
#    ports:
#      - 9094:9094
#    environment:
#      KAFKA_BROKER_ID: 3
#      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
#      KAFKA_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://kafka-3:19094,LISTENER_DOCKER_EXTERNAL://${DOCKER_HOST_IP:-127.0.0.1}:9094
#      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,LISTENER_DOCKER_EXTERNAL:SASL_PLAINTEXT
#      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
#      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
#      KAFKA_SASL_ENABLED_MECHANISMS: SCRAM-SHA-512
#      KAFKA_LOG4J_ROOT_LOGLEVEL: ERROR
#      KAFKA_CONFLUENT_SUPPORT_METRICS_ENABLE: "false"
#      KAFKA_ALLOW_EVERYONE_IF_NO_ACL_FOUND: "true"
#      KAFKA_AUTHORIZER_CLASS_NAME: kafka.security.authorizer.AclAuthorizer
#      KAFKA_SUPER_USERS: User:admin
#      KAFKA_OPTS: -Djava.security.auth.login.config=/etc/kafka/secrets/kafka_jaas.conf
#      KAFKA_CONFLUENT_TOPIC_REPLICATION_FACTOR: 1
#      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
#    depends_on:
#      - zookeeper
#      - zookeeper-add-oskelly2kafka-users
#    volumes:
#      - ./resources/docker:/etc/kafka/secrets