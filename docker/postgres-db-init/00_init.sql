CREATE DATABASE oskelly;

CREATE USER oskelly WITH ENCRYPTED PASSWORD 'qwerty';
CREATE USER directory_moderator WITH ENCRYPTED PASSWORD 'qwerty';
CREATE USER pgosremote WITH ENCRYPTED PASSWORD 'qwerty';
CREATE USER oskslvusr WITH ENCRYPTED PASSWORD 'qwerty';
CREATE USER elasticusr WITH ENCRYPTED PASSWORD 'qwerty';


GRANT ALL ON DATABASE oskelly TO directory_moderator;
GRANT ALL ON DATABASE oskelly TO oskelly;
GRANT ALL ON DATABASE oskelly TO pgosremote;
GRANT ALL ON DATABASE oskelly TO oskslvusr;
GRANT ALL ON DATABASE oskelly TO elasticusr;

-- payments db
CREATE DATABASE oskelly_payments;

CREATE USER oskelly_payments WITH ENCRYPTED PASSWORD 'qwerty';
GRANT ALL ON DATABASE oskelly_payments TO oskelly_payments;
