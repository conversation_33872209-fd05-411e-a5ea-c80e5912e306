{"openapi": "3.0.3", "info": {"title": "Oskelly Main Service", "version": "2.2.1"}, "servers": [{"url": "http://0.0.0.0:8080", "description": "Inferred Url"}], "tags": [{"name": "admin-panel-orders-controller-api-v-2", "description": "Admin Panel Orders Controller Api V 2"}], "paths": {"/api/v2/admin/orders": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Получить список заказов", "operationId": "getOrdersUsingGET", "parameters": [{"name": "groupState", "in": "query", "description": "Выбор категории заказ. Категория заказов 'ВСЕ' установлена по умолчанию", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ALL", "COMPLETED", "CREATED", "EXPERTISE", "FROM_SELLER", "PAYMENT", "PAYMENT_TO_SELLER", "REFUND", "RETURN", "TO_BUYER", "UNCOMPLETED", "UNDEFINED"]}}, {"name": "statuses", "in": "query", "description": "Выбор подкатегории заказа. Подкатегория заказов 'ВСЕ' установлена по умолчанию", "required": false, "style": "form", "explode": true, "schema": {"type": "string", "enum": ["BOUTIQUE_ORDER_IN_BOUTIQUE", "BOUTIQUE_ORDER_ONLINE_CONFIRM", "BOUTIQUE_ORDER_ONLINE_PICKUP", "BOUTIQUE_ORDER_ON_EXPERTISE", "BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE", "BOUTIQUE_ORDER_ON_WAY_TO_OFFICE", "BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE", "BUYER_IN_MOSCOW", "CHOOSING_DELIVERY_METHOD_O2B", "CONCIERGE_ITEMS_WAITING_CONFIRMATION", "EXPECTING_CONFIRM_AGENT_REPORT", "EXPECTING_COURIER_TO_BUYER", "EXPECTING_COURIER_TO_SELLER", "EXPERTISE_COMPLETED", "EXPERTISE_START", "FROM_SELLER_TO_OFFICE", "HAS_CONCIERGE_ITEMS", "HAS_DISPUTE", "HOLD_COMPLETE_REJECTED", "LOGIST_ON_WAY_TO_BUYER", "LOGIST_ON_WAY_TO_SELLER", "ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS", "ORDER_COMPLETED", "ORDER_COMPLETED_RETURN", "ORDER_CONFIRMED", "ORDER_CONFIRMING", "ORDER_DELIVERED", "ORDER_IN_BOUTIQUE", "ORDER_REFUND", "ORDER_SOLD_IN_BOUTIQUE", "OURSELVES_DELIVERY_TO_BUYER", "OURSELVES_FROM_OFFICE_TO_BUYER", "OURSELVES_FROM_SELLER_TO_OFFICE", "OURSELVES_PICKING_UP_FROM_SELLER", "RETURN_COMPLETED", "RETURN_CREATED", "RETURN_EXPERTISE", "RETURN_ON_WAY_TO_OFFICE", "SELLER_IN_MOSCOW", "UNCOMPLETED", "UNDEFINED", "WAIT_PAYMENT_MONEY_TO_SELLER"]}}, {"name": "sellerType", "in": "query", "description": "Тип продавца", "required": false, "style": "form", "schema": {"type": "string", "enum": ["ALL", "BOUTIQUE", "PRIVATE_SELLER"]}}, {"name": "from", "in": "query", "description": "Дата начала поиска", "required": false, "style": "form", "schema": {"type": "string", "format": "date"}}, {"name": "to", "in": "query", "description": "Дата окончание поиска", "required": false, "style": "form", "schema": {"type": "string", "format": "date"}}, {"name": "orderIds", "in": "query", "description": "Список ID заказов для поиска", "required": false, "style": "form", "explode": true, "schema": {"type": "string"}}, {"name": "sort", "in": "query", "description": "sort", "required": false, "style": "form", "schema": {"type": "string", "enum": ["HOLD_TIME", "HOLD_TIME_DESC", "UPDATE_TIME", "UPDATE_TIME_DESC"]}}, {"name": "page", "in": "query", "description": "page", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfPageOfAdminPanelOrder"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/act-of-acceptance/pdf/{orderPositionIds}": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getActOfAcceptancePdf", "operationId": "getActOfAcceptancePdfUsingGET", "parameters": [{"name": "orderPositionIds", "in": "path", "description": "orderPositionIds", "required": true, "style": "simple", "schema": {"minimum": 1, "exclusiveMinimum": false, "type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/act-of-return/pdf/{orderPositionIds}": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getActOfReturnPdf", "operationId": "getActOfReturnPdfUsingGET", "parameters": [{"name": "orderPositionIds", "in": "path", "description": "orderPositionIds", "required": true, "style": "simple", "schema": {"minimum": 1, "exclusiveMinimum": false, "type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/ids": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get order IDs", "operationId": "getOrdersIdUsingGET", "parameters": [{"name": "orderSource", "in": "query", "description": "Order source (Boutique or Online)", "required": false, "style": "form", "schema": {"type": "string", "enum": ["BOUTIQUE", "ONLINE"]}}, {"name": "orderStates", "in": "query", "description": "List of order states to include", "required": false, "style": "form", "explode": true, "schema": {"type": "string", "enum": ["CANCELED", "COMPLETED", "CREATED", "DELETED", "HOLD", "HOLD_COMPLETED", "HOLD_COMPLETE_REJECTED", "HOLD_ERROR", "HOLD_PROCESSING", "MONEY_PAYMENT_ERROR", "MONEY_PAYMENT_NOT_ENOUGH", "MONEY_PAYMENT_TECHNICAL_ERROR", "MONEY_PAYMENT_WAIT", "MONEY_TRANSFERRED", "REFUND", "RETURN", "SELLER_PAID"]}}, {"name": "searchByScanCode", "in": "query", "description": "Code data (barcode / qr code) to find orders list", "required": false, "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOflong"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/logistic/act/pdf": {"post": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get PDF report for delivery company act for a list of orders", "operationId": "getLogisticCompanyActWithBodyUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/logistic/act/pdf/{orderIds}": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get PDF report for delivery company act for a list of orders", "operationId": "getLogisticCompanyActUsingGET", "parameters": [{"name": "orderIds", "in": "path", "description": "orderIds", "required": true, "style": "simple", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/logistic/delivery-company-buyers": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get list of available logistic companies, which can delivery orders between OSKELLY and BUYER", "operationId": "getLogisticBuyersCompanyListUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOfstring"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/logistic/delivery-company-sellers": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get list of available logistic companies, which can delivery orders between SELLER and OSKELLY", "operationId": "getLogisticSellersCompanyListUsingGET", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOfstring"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/orderpositions/{orderPositionId}/certif/pdf": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getCertifPDF", "operationId": "getCertifPDFUsingGET", "parameters": [{"name": "orderPositionId", "in": "path", "description": "orderPositionId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/orderpositions/{orderPositionId}/non-auth-certif/pdf": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getNonAuthCertifPDF", "operationId": "getNonAuthCertifPDFUsingGET", "parameters": [{"name": "orderPositionId", "in": "path", "description": "orderPositionId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/orderpositions/{orderPositionId}/decline": {"patch": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "declineOrderPosition", "operationId": "declineOrderPosition", "parameters": [{"name": "orderPositionId", "in": "path", "description": "order position id", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "saleRejectionReasonTypeId", "in": "query", "description": "sale rejection  reason type id", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "salePlatform", "in": "query", "description": "The platform the seller sold the product", "required": false, "schema": {"type": "string"}}, {"name": "newPrice", "in": "query", "description": "newPrice", "required": false, "schema": {"type": "number", "format": "double"}}, {"name": "comment", "in": "query", "description": "comment", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/orderpositions/{orderPositionId}/confirm": {"patch": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "confirmOrderPosition", "operationId": "confirmOrderPosition", "parameters": [{"name": "orderPositionId", "in": "path", "description": "orderPositionId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/waybills/{waybillId}/tkStickers/pdf": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getStickerPackPDF", "operationId": "getStickerPackPDFUsingGET", "parameters": [{"name": "waybillId", "in": "path", "description": "waybillId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "nCopies", "in": "query", "description": "nCopies", "required": false, "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "reRequest", "in": "query", "description": "reRequest", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/waybills/{waybillId}/tkWaybill/pdf": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getWaybillPDF", "operationId": "getWaybillPDFUsingGET", "parameters": [{"name": "waybillId", "in": "path", "description": "waybillId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "reRequest", "in": "query", "description": "reRequest", "required": false, "style": "form", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get order details", "operationId": "getOrderInfoUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfAdminPanelOrderDetails"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/bankoperations": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get bank operations for the order", "operationId": "getBankOperationsUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOfBankOperationDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/docs": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get order document links", "operationId": "getOrderDocumentLinksUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOfDocumentLinkDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/documents": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get order documents", "operationId": "getOrderDocumentsUsingGET", "parameters": [{"name": "orderId", "in": "query", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOfDocumentsDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/history": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get order history", "operationId": "getOrderHistoryUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfListOfAdminPanelOrderHistoryDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/logistic/details": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get logistic details for an order", "operationId": "getLogisticDetailsUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfLogisticDetailsDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/logistic/tracking": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "Get logistic tracking details for an order", "operationId": "getLogisticTrackingUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseOfLogisticDetailsDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/oskWaybillFromSeller/pdf": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getOskWaybillFromSellerPDF", "operationId": "getOskWaybillFromSellerPDFUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/admin/orders/{orderId}/oskWaybillToBuyer/pdf": {"get": {"tags": ["admin-panel-orders-controller-api-v-2"], "summary": "getOskWaybillToBuyerPDF", "operationId": "getOskWaybillToBuyerPDFUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}}, "components": {"schemas": {"AdminPanelOrder": {"title": "AdminPanelOrder", "required": ["amount", "amountWithoutDelivery", "amountWithoutPromoAndWithoutDelivery", "buyerInfo", "companyProfit", "id", "imageUrl", "lastUpdateDateTime", "promoCode", "sellerInfo", "sellerProfit", "status", "typeSeller"], "type": "object", "properties": {"amount": {"type": "number", "description": "Сумма дохода", "format": "double"}, "amountWithoutDelivery": {"type": "number", "description": "Сумма без учета доставки", "format": "double"}, "amountWithoutPromoAndWithoutDelivery": {"type": "number", "description": "Сумма без учета промокода и доставки", "format": "double"}, "buyerInfo": {"description": "Информация о покупателе", "$ref": "#/components/schemas/AdminPanelOrderUser"}, "companyProfit": {"type": "number", "description": "Доход компании", "format": "double"}, "holdPeriod": {"type": "integer", "description": "Количество дней после оплаты", "format": "int64"}, "id": {"type": "integer", "description": "ID Заказа", "format": "int64"}, "imageUrl": {"type": "string", "description": "Ссылка на изображение товара"}, "lastUpdateDateTime": {"type": "string", "description": "Дата обновления", "format": "date-time"}, "promoCode": {"type": "string", "description": "Промокод"}, "promoPercent": {"type": "number", "description": "Процент скидки по промокод", "format": "double"}, "promoValue": {"type": "number", "description": "Скидка по промокоду", "format": "double"}, "sellerInfo": {"description": "Информация о продавце", "$ref": "#/components/schemas/AdminPanelOrderUser"}, "sellerProfit": {"type": "number", "description": "Доход продавца", "format": "double"}, "status": {"type": "string", "description": "Статус заказа", "enum": ["BOUTIQUE_ORDER_IN_BOUTIQUE", "BOUTIQUE_ORDER_ONLINE_CONFIRM", "BOUTIQUE_ORDER_ONLINE_PICKUP", "BOUTIQUE_ORDER_ON_EXPERTISE", "BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE", "BOUTIQUE_ORDER_ON_WAY_TO_OFFICE", "BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE", "BUYER_IN_MOSCOW", "CHOOSING_DELIVERY_METHOD_O2B", "CONCIERGE_ITEMS_WAITING_CONFIRMATION", "EXPECTING_CONFIRM_AGENT_REPORT", "EXPECTING_COURIER_TO_BUYER", "EXPECTING_COURIER_TO_SELLER", "EXPERTISE_COMPLETED", "EXPERTISE_START", "FROM_SELLER_TO_OFFICE", "HAS_CONCIERGE_ITEMS", "HAS_DISPUTE", "HOLD_COMPLETE_REJECTED", "LOGIST_ON_WAY_TO_BUYER", "LOGIST_ON_WAY_TO_SELLER", "ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS", "ORDER_COMPLETED", "ORDER_COMPLETED_RETURN", "ORDER_CONFIRMED", "ORDER_CONFIRMING", "ORDER_DELIVERED", "ORDER_IN_BOUTIQUE", "ORDER_REFUND", "ORDER_SOLD_IN_BOUTIQUE", "OURSELVES_DELIVERY_TO_BUYER", "OURSELVES_FROM_OFFICE_TO_BUYER", "OURSELVES_FROM_SELLER_TO_OFFICE", "OURSELVES_PICKING_UP_FROM_SELLER", "RETURN_COMPLETED", "RETURN_CREATED", "RETURN_EXPERTISE", "RETURN_ON_WAY_TO_OFFICE", "SELLER_IN_MOSCOW", "UNCOMPLETED", "UNDEFINED", "WAIT_PAYMENT_MONEY_TO_SELLER"]}, "typeSeller": {"type": "string", "description": "Тип продавца", "enum": ["ALL", "BOUTIQUE", "PRIVATE_SELLER"]}}, "description": "Информация о заказе для панели администратора"}, "AdminPanelOrderDetails": {"title": "AdminPanelOrderDetails", "type": "object", "properties": {"currencyCode": {"type": "string"}, "orderInfo": {"$ref": "#/components/schemas/AdminPanelOrderInfo"}, "products": {"$ref": "#/components/schemas/AdminPanelProductListDTO"}, "userInfoDelivery": {"$ref": "#/components/schemas/AdminPanelUserInfoAndDeliveryDTO"}}, "description": "Order details for admin panel"}, "AdminPanelOrderDetailsUserAddressDTO": {"title": "AdminPanelOrderDetailsUserAddressDTO", "type": "object", "properties": {"addressLine": {"type": "string"}, "city": {"type": "string"}, "zipCode": {"type": "string"}}, "description": "User address details"}, "AdminPanelOrderDetailsUserDTO": {"title": "AdminPanelOrderDetailsUserDTO", "required": ["address", "email", "fullName", "nick<PERSON><PERSON>", "phone", "userId"], "type": "object", "properties": {"address": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$ref": "#/components/schemas/AdminPanelOrderDetailsUserAddressDTO"}, "comment": {"type": "string", "description": "Комментарий"}, "companyName": {"type": "string", "description": "Наименование компании"}, "counterpartyId": {"type": "integer", "description": "ID реквизитов", "format": "int64"}, "email": {"type": "string", "description": "Email"}, "fullName": {"type": "string", "description": "ФИО"}, "nickName": {"type": "string", "description": "Псевдоним"}, "phone": {"type": "string", "description": "Телефон"}, "type": {"type": "string", "enum": ["PRO", "SIMPLE"]}, "userId": {"type": "integer", "description": "ID", "format": "int64"}}, "description": "Details about user"}, "AdminPanelOrderHistoryDTO": {"title": "AdminPanelOrderHistoryDTO", "type": "object", "properties": {"dateTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["BOUTIQUE_ORDER_IN_BOUTIQUE", "BOUTIQUE_ORDER_ONLINE_CONFIRM", "BOUTIQUE_ORDER_ONLINE_PICKUP", "BOUTIQUE_ORDER_ON_EXPERTISE", "BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE", "BOUTIQUE_ORDER_ON_WAY_TO_OFFICE", "BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE", "BUYER_IN_MOSCOW", "CHOOSING_DELIVERY_METHOD_O2B", "CONCIERGE_ITEMS_WAITING_CONFIRMATION", "EXPECTING_CONFIRM_AGENT_REPORT", "EXPECTING_COURIER_TO_BUYER", "EXPECTING_COURIER_TO_SELLER", "EXPERTISE_COMPLETED", "EXPERTISE_START", "FROM_SELLER_TO_OFFICE", "HAS_CONCIERGE_ITEMS", "HAS_DISPUTE", "HOLD_COMPLETE_REJECTED", "LOGIST_ON_WAY_TO_BUYER", "LOGIST_ON_WAY_TO_SELLER", "ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS", "ORDER_COMPLETED", "ORDER_COMPLETED_RETURN", "ORDER_CONFIRMED", "ORDER_CONFIRMING", "ORDER_DELIVERED", "ORDER_IN_BOUTIQUE", "ORDER_REFUND", "ORDER_SOLD_IN_BOUTIQUE", "OURSELVES_DELIVERY_TO_BUYER", "OURSELVES_FROM_OFFICE_TO_BUYER", "OURSELVES_FROM_SELLER_TO_OFFICE", "OURSELVES_PICKING_UP_FROM_SELLER", "RETURN_COMPLETED", "RETURN_CREATED", "RETURN_EXPERTISE", "RETURN_ON_WAY_TO_OFFICE", "SELLER_IN_MOSCOW", "UNCOMPLETED", "UNDEFINED", "WAIT_PAYMENT_MONEY_TO_SELLER"]}}, "description": "История заказа"}, "AdminPanelOrderInfo": {"title": "AdminPanelOrderInfo", "required": ["companyProfit", "companyProfitWithoutPromoCode", "companyProfitWithoutPromoCodeInPercent", "orderAmount", "orderAmountWithPromoCodeWithoutDelivery", "orderCreatedDateTime", "orderDeliveryCost", "orderId", "orderItemsCount", "orderSource", "orderUpdatedDateTime", "productsCount", "sellerProfit", "totalOrderAmount"], "type": "object", "properties": {"companyProfit": {"type": "number", "description": "Company profit", "format": "double"}, "companyProfitWithoutPromoCode": {"type": "number", "description": "Company profit without promo-code", "format": "double"}, "companyProfitWithoutPromoCodeInPercent": {"type": "number", "description": "Company profit without promo-code in percent", "format": "double"}, "orderAmount": {"type": "number", "description": "Order amount", "format": "double"}, "orderAmountWithPromoCodeWithoutDelivery": {"type": "number", "description": "Order amount with promo-code and without delivery", "format": "double"}, "orderCreatedDateTime": {"type": "string", "description": "Date and time when order has been created", "format": "date-time"}, "orderDeliveryCost": {"type": "number", "description": "Delivery cost in order", "format": "double"}, "orderId": {"type": "integer", "description": "Order ID", "format": "int64"}, "parentOrderId": {"type": "integer", "description": "Parent order ID", "format": "int64"}, "orderItemsCount": {"type": "integer", "description": "Count of products in order", "format": "int64"}, "orderSource": {"type": "string", "description": "Order source (Boutique or Online)", "enum": ["BOUTIQUE", "ONLINE"]}, "orderUpdatedDateTime": {"type": "string", "description": "Date and time when order has been updated", "format": "date-time"}, "productsCount": {"type": "integer", "description": "Count of products in order", "format": "int32"}, "sellerProfit": {"type": "number", "description": "Seller profit", "format": "double"}, "totalOrderAmount": {"type": "number", "description": "Total/Sum", "format": "double"}}, "description": "Информация о заказе"}, "AdminPanelOrderUser": {"title": "AdminPanelOrderUser", "required": ["fullName", "mailAddr", "nick<PERSON><PERSON>"], "type": "object", "properties": {"fullName": {"type": "string", "description": "ФИО"}, "mailAddr": {"type": "string", "description": "Адрес электроной почты"}, "nickName": {"type": "string", "description": "Псевдоним"}}, "description": "Информация о пользователе в список заказов (покупатель / продавец)"}, "AdminPanelOrderWaybillDTO": {"title": "AdminPanelOrderWaybillDTO", "type": "object", "properties": {"cost": {"type": "number", "format": "double"}, "dateTime": {"type": "string", "format": "date-time"}, "deliveryCompanyName": {"type": "string"}, "status": {"type": "string", "enum": ["DELIVERED_FROM_BUYER_TO_OFFICE", "DELIVERED_FROM_SELLER_TO_OFFICE", "DELIVERED_TO_BUYER", "DELIVERED_TO_SELLER", "DELIVERY_IN_MOSCOW", "DELIVERY_TODAY_TO_BUYER", "FROM_BUYER_TO_OFFICE", "FROM_OFFICE_TO_BUYER", "FROM_OFFICE_TO_SELLER", "FROM_SELLER_TO_OFFICE", "JUST_CREATED", "JUST_CREATED_TO_BUYER", "OURSELVES_DELIVERY_TODAY_TO_BUYER", "OURSELVES_DELIVERY_TO_BUYER", "OURSELVES_FROM_BUYER_TO_OFFICE", "OURSELVES_FROM_OFFICE_TO_BUYER", "OURSELVES_FROM_OFFICE_TO_SELLER", "OURSELVES_FROM_SELLER_TO_OFFICE", "OURSELVES_PICKING_UP_FROM_BUYER", "OURSELVES_PICKING_UP_FROM_OFFICE", "OURSELVES_PICKING_UP_FROM_SELLER", "PICKING_UP_FROM_BUYER", "PICKING_UP_FROM_OFFICE", "PICKING_UP_FROM_SELLER", "PICKUP_DECLINED", "PICKUP_IN_MOSCOW"]}, "waybillId": {"type": "integer", "format": "int64"}}, "description": "Waybill for order"}, "AdminPanelProductDTO": {"title": "AdminPanelProductDTO", "type": "object", "properties": {"amountAfterPromoCode": {"type": "number", "format": "double"}, "brand": {"type": "string"}, "categoryList": {"type": "array", "items": {"type": "string"}}, "companyProfit": {"type": "number", "format": "double"}, "companyProfitPercent": {"type": "number", "format": "double"}, "imageUrl": {"type": "string"}, "orderPositionId": {"type": "integer", "format": "int64"}, "productId": {"type": "integer", "format": "int64"}, "productItemId": {"type": "integer", "format": "int64"}, "promoCode": {"type": "string"}, "promoCodePercent": {"type": "number", "format": "double"}, "sellerProfit": {"type": "number", "format": "double"}, "status": {"type": "string", "enum": ["CREATE_WAYBILL_TO_BUYER", "HQ_WAREHOUSE", "INITIAL", "ON_VERIFICATION", "PICKUP_DECLINED", "PURCHASE_REQUEST", "READY_TO_SHIP", "REJECTED_AFTER_VERIFICATION", "REQUESTED_TO_RETURN", "RETURN_ACCEPTED", "RETURN_DECLINED", "RETURN_VERIFICATION_OK", "RETURN_VERIFICATION_REJECTED", "SALE_CONFIRMED", "SALE_REJECTED", "SHIPPED_TO_CLIENT", "VERIFICATION_BAD_STATE", "VERIFICATION_NEED_CLEANING", "VERIFICATION_OK"]}, "totalAmount": {"type": "number", "format": "double"}, "bonuses": {"type": "number", "format": "double"}, "moneyBonuses": {"type": "number", "format": "double"}}, "description": "Product in order"}, "AdminPanelProductListDTO": {"title": "AdminPanelProductListDTO", "type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/AdminPanelProductDTO"}}, "totalAmount": {"type": "number", "format": "double"}, "totalCompanyProfit": {"type": "number", "format": "double"}, "totalSellerProfit": {"type": "number", "format": "double"}}, "description": "Products in order and totals for amount and profits"}, "AdminPanelUserInfoAndDeliveryDTO": {"title": "AdminPanelUserInfoAndDeliveryDTO", "type": "object", "properties": {"buyer": {"$ref": "#/components/schemas/AdminPanelOrderDetailsUserDTO"}, "buyerWaybill": {"$ref": "#/components/schemas/AdminPanelOrderWaybillDTO"}, "seller": {"$ref": "#/components/schemas/AdminPanelOrderDetailsUserDTO"}, "sellerWaybill": {"$ref": "#/components/schemas/AdminPanelOrderWaybillDTO"}}, "description": "Details about user and delivery"}, "Api2ResponseOfAdminPanelOrderDetails": {"title": "Api2ResponseOfAdminPanelOrderDetails", "type": "object", "properties": {"data": {"$ref": "#/components/schemas/AdminPanelOrderDetails"}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfListOfAdminPanelOrderHistoryDTO": {"title": "Api2ResponseOfListOfAdminPanelOrderHistoryDTO", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/AdminPanelOrderHistoryDTO"}}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfListOfBankOperationDTO": {"title": "Api2ResponseOfListOfBankOperationDTO", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/BankOperationDTO"}}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfListOfDocumentLinkDTO": {"title": "Api2ResponseOfListOfDocumentLinkDTO", "type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentLinkDTO"}}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfListOfDocumentsDTO": {"title": "Api2ResponseOfListOfDocumentsDTO", "type": "object", "properties": {"data": {"$ref": "#/components/schemas/AdminDocumentsDTO"}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfListOflong": {"title": "Api2ResponseOfListOflong", "type": "object", "properties": {"data": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfListOfstring": {"title": "Api2ResponseOfListOfstring", "type": "object", "properties": {"data": {"type": "array", "items": {"type": "string"}}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfLogisticDetailsDTO": {"title": "Api2ResponseOfLogisticDetailsDTO", "type": "object", "properties": {"data": {"$ref": "#/components/schemas/LogisticDetailsDTO"}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfOrderTrackingDTO": {"title": "Api2ResponseOfOrderTrackingDTO", "type": "object", "properties": {"data": {"$ref": "#/components/schemas/OrderTrackingDTO"}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOfPageOfAdminPanelOrder": {"title": "Api2ResponseOfPageOfAdminPanelOrder", "type": "object", "properties": {"data": {"$ref": "#/components/schemas/PageOfAdminPanelOrder"}, "errorData": {"type": "object"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "humanMessage": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "integer", "format": "int64"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}}}, "BankOperationDTO": {"title": "BankOperationDTO", "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "bankOperationId": {"type": "string"}, "cardBindTime": {"type": "string", "format": "date-time"}, "cardBrand": {"type": "string"}, "cardHolder": {"type": "string"}, "cardNumber": {"type": "string"}, "cardRefId": {"type": "string"}, "counterpartyId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "fee": {"type": "integer", "format": "int64"}, "id": {"type": "integer", "format": "int64"}, "objectId": {"type": "integer", "format": "int64"}, "operationType": {"type": "string", "enum": ["CARD_BIND", "CARD_UNBIND", "HOLD", "HOLD_COMPLETE", "HOLD_REVERSE", "OSKELLY_PAYOUT", "PARTIAL_REFUND", "REFUND", "SELLER_PAYOUT"]}, "orderId": {"type": "integer", "format": "int64"}, "paymentAccount": {"type": "string"}, "paymentSystem": {"type": "string"}, "paymentSystemCode": {"type": "string"}, "paymentSystemTime": {"type": "string", "format": "date-time"}, "rawResponse": {"type": "string"}, "rrn": {"type": "string"}, "state": {"type": "string", "enum": ["CANCELED", "DONE", "ERROR", "INPROGRESS", "PREPARED"]}, "stateUserText": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "uuid": {"type": "string"}}}, "DeliveryHintDTO": {"title": "DeliveryHintDTO", "type": "object", "properties": {"deliveryHints": {"type": "string"}, "preferCompany": {"type": "string"}}}, "DeliveryStatusDTO": {"title": "DeliveryStatusDTO", "type": "object", "properties": {"deliveryCompany": {"type": "string"}, "statusTime": {"type": "string", "format": "date-time"}, "trackingComment": {"type": "string"}, "trackingState": {"type": "string"}, "trackingTime": {"type": "string", "format": "date-time"}, "waybillExternalId": {"type": "string"}, "waybillOrderExternalId": {"type": "string"}}}, "DocumentLinkDTO": {"title": "DocumentLinkDTO", "type": "object", "properties": {"description": {"type": "string"}, "sequenceNumber": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["AGENT_REPORT", "CERTIFICATE", "OSKELLY_WAYBILL_FROM_SELLER", "OSKELLY_WAYBILL_TO_BUYER", "TK_STICKERS_TO_BUYER", "TK_WAYBILL_FROM_SELLER", "TK_WAYBILL_TO_BUYER", "DOC_ORDER_LABELS"]}, "url": {"type": "string"}}}, "AdminDocumentsDTO": {"title": "AdminDocumentsDTO", "type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentGroupDTO"}}}}, "DocumentGroupDTO": {"title": "DocumentGroupDTO", "type": "object", "properties": {"name": {"type": "string"}, "typeId": {"type": "integer", "format": "int64"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/DocumentDTO"}}, "alerts": {"type": "array", "items": {"type": "string"}}}}, "DocumentDTO": {"title": "DocumentDTO", "type": "object", "properties": {"filename": {"type": "string"}, "fileUrl": {"type": "string"}, "fileId": {"type": "integer", "format": "int64"}, "read": {"type": "boolean"}, "print": {"type": "boolean"}, "remove": {"type": "boolean"}}}, "LogisticDetailsDTO": {"title": "LogisticDetailsDTO", "type": "object", "properties": {"sellerSummary": {"$ref": "#/components/schemas/LogisticSummaryDTO"}, "buyerSummary": {"$ref": "#/components/schemas/LogisticSummaryDTO"}, "deliveryO2BHints": {"$ref": "#/components/schemas/DeliveryHintDTO"}, "deliveryS2OHints": {"$ref": "#/components/schemas/DeliveryHintDTO"}, "fromSellerDeliveryChanges": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryStatusDTO"}}, "toBuyerDeliveryChanges": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryStatusDTO"}}}}, "LogisticSummaryDTO": {"title": "LogisticSummaryDTO", "type": "object", "properties": {"deliveryCompany": {"type": "string"}, "waybillExternalId": {"type": "string"}, "waybillOrderExternalId": {"type": "string"}, "plannedDeliveryTime": {"type": "string", "format": "date-time"}}}, "OrderTrackingDTO": {"title": "OrderTrackingDTO", "type": "object", "properties": {"o2bTrackingUrl": {"type": "string"}, "s2oTrackingUrl": {"type": "string"}}}, "PageOfAdminPanelOrder": {"title": "PageOfAdminPanelOrder", "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/AdminPanelOrder"}}, "itemsCount": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}}}, "SaleRejectionReasonParamsDto": {"title": "SaleRejectionReasonParamsDto", "type": "object", "required": ["saleRejectionReasonTypeId"], "properties": {"saleRejectionReasonTypeId": {"description": "sale rejection  reason type id", "type": "integer", "format": "int64"}, "salePlatform": {"description": "The platform the seller sold the product", "type": "string"}, "newPrice": {"description": "newPrice", "type": "number", "format": "double"}, "comment": {"description": "comment", "type": "string"}}}}}}