openapi: 3.0.3
info:
  title: Oskelly Admin Panel API
  description: Oskelly Admin Panel API
  version: "1.0"
paths:
  /adminpanel/orders/{orderId}/users/{userId}/addresses:
    get:
      tags:
        - UserAddress
      summary: Get user delivery addresses
      operationId: getUserAddressList
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminAddressView'
  /adminpanel/orders/{orderId}/users/{userId}/deleted-addresses:
    get:
      tags:
        - UserAddress
      summary: Get deleted user delivery addresses
      operationId: getUserDeletedAddressList
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminAddressView'

  /adminpanel/orders/{orderId}/delivery/seller/office/pickup-info-set-by-seller:
    get:
      tags:
        - PickupInfo
      summary: Get pickup info set by seller
      operationId: getPickupInfoSetBySeller
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DateWithIntervalDTO'

  /adminpanel/orders/get-user-address/{orderId}/{addressId}:
    get:
      tags:
        - UserAddress
      summary: Get user address details
      operationId: getUserAddress
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/AddressIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressData'

  /adminpanel/orders/add-seller-address/{orderId}:
    post:
      tags:
        - UserAddress
      summary: Save seller address
      operationId: saveSellerAddress
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AddressData'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/add-buyer-address/{orderId}:
    post:
      tags:
        - UserAddress
      summary: Save buyer address
      operationId: saveBuyerAddress
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AddressData'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/change-address-endpoint/{orderId}:
    put:
      tags:
        - UserAddress
      summary: Change user delivery address
      operationId: changeUserDeliveryAddress
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/PickupAddressEndpointIdParam'
        - $ref: '#/components/parameters/DeliveryAddressEndpointIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/addresses:
    post:
      tags:
        - UserAddress
      summary: Get street hint
      operationId: getAddressStreetHint
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DaDataAddressRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetStreetHintResponseDTO'

  /adminpanel/orders/validate-addresses:
    post:
      tags:
        - UserAddress
      summary: Validate address
      operationId: validateAddress
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DaDataAddressRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateAddressResponseDTO'

  /adminpanel/orders/{orderId}/delivery/seller/office/pickup-info:
    post:
      tags:
        - PickupInfo
      summary: Set pick-up info for delivery from seller to office
      operationId: setPickupInfoFromSeller
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PickupInfoDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/refund-amount/{orderId}:
    post:
      tags:
        - Order
      summary: Execute manual refund with arbitrary amount (if possible / supported)
      operationId: ordersRefundAmount
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundAmountReqDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/refund/{orderId}:
    put:
      tags:
        - Order
      summary: Refund by admin activity
      operationId: orderRefund
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/refund-try-again-on-fail/{orderId}:
    put:
      tags:
        - Order
      summary: try refund again after fail
      operationId: refundTryAgainOnFail
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/bans/all:
    post:
      tags:
        - BanService
      summary: get all bans by filter
      operationId: getAllBans
      parameters:
        - $ref: '#/components/parameters/PageNumberParam'
        - $ref: '#/components/parameters/PageSizeParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BanSearchFilerDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminPanelUserBanDTO'

  /adminpanel/users/{userId}/payment-details:
    get:
      tags:
        - User
      summary: Get user payment details
      operationId: getUserPaymentDetails
      parameters:
        - $ref: '#/components/parameters/OrderIdOptionalParam'
        - $ref: '#/components/parameters/UserIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
    post:
      tags:
        - User
      summary: Save user payment details
      operationId: saveUserPaymentDetails
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/CounterpartyDTO'

  /adminpanel/users/{userId}/payment-details/{counterpartyId}:
    delete:
      tags:
        - User
      summary: Delete user payment details
      operationId: deleteUserPaymentDetails
      parameters:
        - $ref: '#/components/parameters/UserIdParam'
        - $ref: '#/components/parameters/CounterpartyIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/CounterpartyDTO'

  /adminpanel/orders/add-counterparty/{orderId}:
    post:
      tags:
        - User
      summary: Save seller payment details
      operationId: addCounterparty
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CounterpartyData'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/change-seller-counterparty/{orderId}:
    put:
      tags:
        - User
      summary: Save seller payment details
      operationId: changeSellerCounterparty
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/SellerCounterpartyIdOptionalParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/convert-hold-to-prepayment/{orderId}:
    put:
      tags:
        - Order
      summary: charge money by user request
      operationId: convertHoldToPrepayment
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/get-user-counterparty/{counterpartyId}:
    get:
      tags:
        - User
      summary: Get payment details by id
      operationId: getPaymentDetailsById
      parameters:
        - $ref: '#/components/parameters/CounterpartyIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounterpartyData'

  /adminpanel/orders/set-concierge-additional-info/{orderId}:
    patch:
      tags:
        - Concierge
      summary: add concierge extra data
      operationId: setConciergeExtraData
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConciergeAdditionalInfoDTO'
      responses:
        '200':
          description: Successful operation

  /adminpanel/orders/position/datamatrix:
    put:
      tags:
        - OrderPosition
      summary: Set datamatrix in order position
      operationId: setOrderPositionDatamatrix
      parameters:
        - $ref: '#/components/parameters/PositionIdParam'
        - $ref: '#/components/parameters/DatamatrixParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/position/approve-marking-code:
    put:
      tags:
        - OrderPosition
      summary: Approve marking code in order position
      operationId: approveOrderPositionMarkingCode
      parameters:
        - $ref: '#/components/parameters/PositionIdParam'
        - $ref: '#/components/parameters/SetValueBooleanParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/orderpositions/{orderPositionId}/set-country-of-origin:
    put:
      tags:
        - OrderPosition
      summary: set country of origin
      operationId: setCountryOfOrigin
      parameters:
        - $ref: '#/components/parameters/OrderPositionIdParam'
        - $ref: '#/components/parameters/CountryIdOptionalParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/shipment2boutique/{orderId}:
    post:
      tags:
        - Boutique
      summary: quick shipment to boutique
      operationId: shipment2boutique
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /adminpanel/orders/set-order-source-infos:
    patch:
      tags:
        - Boutique
      summary: set order source info
      operationId: setOrderSourceInfo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderSetOrderSourceInfoRequest'
      responses:
        '200':
          description: Successful operation

  /adminpanel/orders/set-order-legal-entity:
    post:
      tags:
        - Boutique
      summary: set legal entity
      operationId: setOrderLegalEntity
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderSetOrderLegalEntityRequest'
      responses:
        '200':
          description: Successful operation

  /adminpanel/orders/set-order-legal-entity-concierge:
    post:
      tags:
        - Boutique
      summary: set concierge legal entity
      operationId: setOrderConciergeLegalEntity
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderSetOrderLegalEntityConciergeRequest'
      responses:
        '200':
          description: Successful operation

  /api/v2/dictionary/concierge/client/channels:
    get:
      tags:
        - Concierge
      summary: Get concierge client channel list
      operationId: getConciergeClientChannelList
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ConciergeClientChannelResponseDTO'

  /api/v2/dictionary/sales/rejection/reasons:
    get:
      tags:
        - Dictionary
      summary: Get all rejection reasons
      operationId: getAllRejectionReasons
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2SaleRejectionReasonResponseDTO'

  /adminpanel/orders/switch-debt-on-payout/{orderId}:
    put:
      tags:
        - Payment
      summary: Switch debt on payout
      operationId: switchDebtOnPayout
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/SwitchDebtOnPayoutParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: integer
                format: int64

  /api/v2/address/countries-int:
    get:
      tags:
        - UserAddress
      summary: Get countries
      operationId: getInternationalCountryList
      parameters:
        - $ref: '#/components/parameters/Str'
        - $ref: '#/components/parameters/AbleToPickup'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressCountyListResponseDTO'

  /api/v2/admin/orders/{orderId}/add-seller-address-endpoint:
    post:
      tags:
        - UserAddress
      summary: Set seller address
      operationId: setSellerAddress
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressEndpointDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressEndpointResponseDTO'

  /api/v2/admin/orders/{orderId}/add-buyer-address-endpoint:
    post:
      tags:
        - UserAddress
      summary: Set buyer address
      operationId: setBuyerAddress
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressEndpointDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressEndpointResponseDTO'

  /api/v2/admin/orders/{orderId}/payments:
    get:
      tags:
        - Payment
      summary: Get order payments
      operationId: getAdminOrderPayments
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2OrderPaymentListResponseDTO'

  /api/v2/admin/orders/{orderId}/pickup-options:
    get:
      tags:
        - Delivery
      summary: Get order pickup options
      operationId: getPickupOptions
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2PickupOptionListResponseDTO'

  /api/v2/admin/orders/{orderId}/confirm-delivery-to-buyer:
    post:
      tags:
        - Delivery
      summary: Confirm delivery to buyer
      operationId: confirmDeliveryToBuyer
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: string

  /api/v2/admin/orders/{orderId}/dispute-history:
    get:
      tags:
        - Dispute
      summary: Get order dispute history
      operationId: getDisputeHistory
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2DisputeHistoryResponseDTO'

  /api/v2/admin/orders/clean-cart:
    delete:
      tags:
        - Order
      summary: Admin clean cart
      operationId: cleanCartAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CleanCartRequestDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AdminCleanCartResponseDTO'

  /api/v2/admin/orders/files:
    post:
      tags:
        - Order
      operationId: uploadOrderFiles
      parameters:
        - $ref: '#/components/parameters/OrderIdQueryParam'
        - $ref: '#/components/parameters/OrderFileTypeIdQueryParam'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
              required:
                - files
      responses:
        '200':
          description: OK
    get:
      tags:
        - Order
      operationId: getOrderFile
      parameters:
        - $ref: '#/components/parameters/OrderIdQueryParam'
        - $ref: '#/components/parameters/OrderFileIdQueryParam'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: 'filename'
              schema:
                type: string
    delete:
      tags:
        - Order
      operationId: removeOrderFile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderFileRemoveRequestDTO'
      responses:
        '200':
          description: OK

  /api/v2/admin/orders/files/list:
    get:
      tags:
        - Order
      operationId: getOrderFileList
      parameters:
        - $ref: '#/components/parameters/OrderIdQueryParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AdminOrderFileListResponseDTO'

  /api/v2/admin/estimation/orders/{orderId}:
    get:
      tags:
        - EventDateEstimation
      summary: Get event date estimations
      operationId: getEventDateEstimations
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2EventDateEstimationListResponseDTO'

  /api/v2/admin/estimation/orders/{orderId}/history:
    get:
      tags:
        - EventDateEstimation
      summary: Get event date estimation history
      operationId: getEventDateEstimationHistory
      parameters:
        - $ref: '#/components/parameters/OrderIdParam'
        - $ref: '#/components/parameters/EstimatedEventTypeParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2EventDateEstimationHistoryListResponseDTO'

  /api/v2/address/cities-int-admin:
    get:
      tags:
        - UserAddress
      summary: Get cities
      operationId: getInternationalCityListAdmin
      parameters:
        - $ref: '#/components/parameters/CountryIdParam'
        - $ref: '#/components/parameters/Str'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressCityListResponseDTO'

  /api/v2/address/cities-cis:
    get:
      tags:
        - UserAddress
      summary: Get cities in CIS
      operationId: getCitiesCis
      parameters:
        - $ref: '#/components/parameters/QueryParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressListResponseDTO'

  /api/v2/address/cities-unrestricted:
    get:
      tags:
        - UserAddress
      summary: Get cities with no settlement type restrictions
      operationId: getCitiesUnrestricted
      parameters:
        - $ref: '#/components/parameters/QueryParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressListResponseDTO'

  /api/v2/address/cities-int:
    get:
      tags:
        - UserAddress
      summary: Get cities
      operationId: getCitiesInternationalisation
      parameters:
        - $ref: '#/components/parameters/QueryParam'
        - $ref: '#/components/parameters/CountryIdParam'
        - $ref: '#/components/parameters/SupportedOnlyParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2InternationalAddressCityListResponseDTO'

  /api/v2/address/suggestions:
    get:
      tags:
        - UserAddress
      summary: Get address suggestions
      operationId: getAddressSuggestions
      parameters:
        - $ref: '#/components/parameters/QueryParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2AddressListResponseDTO'

  /api/v2/frontend/admin/config/base:
    get:
      tags:
        - FrontendAdminConfig
      summary: Get base configs
      operationId: getBaseFrontendAdminConfigs
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FrontendAdminConfig'

  /api/v3/admin/user/counterparty/getForUser/{userId}:
    get:
      tags:
        - CounterpartyAPI
      summary: Получить реквизиты пользователя
      operationId: getCounterpartiesForUserUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfCounterpartyDTO'

  /api/v3/admin/user/counterparty/edit:
    post:
      tags:
        - CounterpartyAPI
      summary: Редактировать реквизиты
      operationId: editCounterpartyUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EditCounterpartyRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCounterpartyDTO'

  /api/v3/admin/user/counterparty/changeVisibility:
    post:
      tags:
        - CounterpartyAPI
      summary: Скрыть/показать реквизиты
      operationId: changeCounterpartyVisibilityUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeCounterpartyVisibilityRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCounterpartyDTO'

  /api/v3/admin/user/counterparty/markAsDeleted:
    post:
      tags:
        - CounterpartyAPI
      summary: Удалить реквизиты
      operationId: markCounterpartyAsDeletedUsingPOST
      parameters:
        - name: counterpartyId
          in: query
          description: Counterparty ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCounterpartyDTO'

  /api/v3/admin/user/address:
    post:
      tags:
        - Users API
      summary: Редактировать адрес
      operationId: editAddressUsingPATCH
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressEndpointDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAddressEndpointDTO'
    delete:
      tags:
        - Users API
      summary: Удалить адрес
      operationId: deleteAddressUsingDELETE
      parameters:
        - name: addressEndpointId
          in: query
          description: Address endpoint ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'

  /api/v3/admin/user/address/{userId}:
    get:
      tags:
        - Users API
      summary: Список адресов
      operationId: getAddressListUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfAddressEndpointDTO'

  /api/v3/admin/user/address/markAsNonActual:
    post:
      tags:
        - Users API
      summary: Пометить адрес, как неактуальный
      operationId: markAsNonActualPOST
      parameters:
        - name: addressEndpointId
          in: query
          description: Address endpoint ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAddressEndpointDTO'
components:
  parameters:
    OrderIdParam:
      name: orderId
      in: path
      description: order ID
      required: true
      schema:
        type: integer
        format: int64
    OrderIdQueryParam:
      name: orderId
      in: query
      required: true
      schema:
        type: integer
        format: int64
    OrderFileIdQueryParam:
      name: fileId
      in: query
      required: true
      schema:
        type: integer
        format: int64
    OrderFileTypeIdQueryParam:
      name: orderFileTypeId
      in: query
      required: true
      schema:
        type: integer
        format: int64
    OrderIdOptionalParam:
      name: orderId
      in: query
      description: optional order ID
      required: false
      schema:
        type: integer
        format: int64
    OrderPositionIdParam:
      name: orderPositionId
      in: path
      description: order position ID
      required: true
      schema:
        type: integer
        format: int64
    PositionIdParam:
      name: positionId
      in: query
      description: order position ID
      required: true
      schema:
        type: integer
        format: int64
    DatamatrixParam:
      name: datamatrix
      in: query
      description: datamatrix value
      required: true
      schema:
        type: string
    UserIdParam:
      name: userId
      in: path
      description: user ID
      required: true
      schema:
        type: integer
        format: int64
    CounterpartyIdParam:
      name: counterpartyId
      in: path
      description: counterparty ID
      required: true
      schema:
        type: integer
        format: int64
    CountryIdParam:
      name: countryId
      in: query
      description: country ID
      required: true
      schema:
        type: integer
        format: int64
    CountryIdOptionalParam:
      name: countryId
      in: query
      description: country ID
      required: false
      schema:
        type: integer
        format: int64
    AddressIdParam:
      name: addressId
      in: path
      description: address ID
      required: true
      schema:
        type: integer
        format: int64
    Str:
      name: str
      in: query
      description: search string
      required: false
      schema:
        type: string
    AbleToPickup:
      name: ableToPickup
      in: query
      required: false
      schema:
        type: boolean
    QueryParam:
      name: query
      in: query
      description: search string
      required: false
      schema:
        type: string
    PickupAddressEndpointIdParam:
      name: pickupAddressEndpointId
      in: query
      description: pick up address endpoint ID
      required: false
      schema:
        type: integer
        format: int64
    DeliveryAddressEndpointIdParam:
      name: deliveryAddressEndpointId
      in: query
      description: delivery address endpoint ID
      required: false
      schema:
        type: integer
        format: int64
    DateParam:
      name: date
      in: query
      description: localdate param
      required: false
      schema:
        type: string
        format: date
    PageNumberParam:
      name: pageNumber
      in: query
      description: page number on request
      required: true
      schema:
        type: integer
    PageSizeParam:
      name: pageSize
      in: query
      description: total element in response list
      required: true
      schema:
        type: integer
    SellerCounterpartyIdOptionalParam:
      name: sellerCounterpartyId
      in: query
      description: seller counterparty id
      required: false
      schema:
        type: integer
        format: int64
    SwitchDebtOnPayoutParam:
      name: switchTo
      in: query
      description: Switch debt on payout value
      required: true
      schema:
        type: boolean
    DaDataEnabledParam:
      name: isDaDataEnabled
      in: query
      description: data suggestions
      required: false
      schema:
        type: boolean
    SetValueBooleanParam:
      name: setValue
      in: query
      description: boolean value
      required: false
      schema:
        type: boolean
    EstimatedEventTypeParam:
      name: eventType
      in: query
      description: estimated event type
      required: true
      schema:
        $ref: '#/components/schemas/EstimatedEventTypeDTO'
    SupportedOnlyParam:
      name: supportedOnly
      in: query
      description: Include only supported cities
      required: false
      schema:
        type: boolean

  schemas:
    Api2ResponseOfListOfAddressEndpointDTO:
      type: "object"
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/AddressEndpointDTO'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfListOfAddressEndpointDTO"
    Api2ResponseOfAddressEndpointDTO:
      title: Api2ResponseOfAddressEndpointDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AddressEndpointDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfString:
      type: "object"
      properties:
        data:
          type: "string"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfString"
    ChangeCounterpartyVisibilityRequest:
      type: object
      properties:
        counterpartyId:
          type: integer
          format: int64
        visible:
          type: boolean
    Api2ResponseOfListOfCounterpartyDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    EditCounterpartyRequest:
      type: object
      properties:
        counterparty:
          $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
    AdminAddressView:
      properties:
        id:
          type: integer
          format: int64
        fullname:
          type: string
        phone:
          type: string
        addressInfo:
          type: string
        isFias:
          type: boolean
    AddressData:
      properties:
        endpointId:
          type: integer
          format: int64
        addressId:
          type: integer
          format: int64
        active:
          type: boolean
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
        phone:
          type: string
        region:
          type: string
        country:
          type: string
        countryDataId:
          type: integer
          format: int64
        city:
          type: string
        cityDataId:
          type: integer
          format: int64
        regionCity:
          type: string
        addressFull:
          type: string
        street:
          type: string
        house:
          type: string
        flat:
          type: string
        isValidated:
          type: boolean
          default: false
        isCityValidated:
          type: boolean
          default: false
        lastCityValidationTime:
          type: string
        isAddressValidated:
          type: boolean
        lastAddressValidationTime:
          type: string
        fiasId:
          type: string
        cityFiasId:
          type: string
        settlementFiasId:
          type: string
        dataFullAddress:
          type: string
        isCis:
          type: boolean
        displayString:
          type: string
        cityDisplayString:
          type: string
    GetCityHintResponseDTO:
      required: [ data ]
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CityHintDTO'
    CityHintDTO:
      properties:
        region:
          type: string
        city:
          type: string
    GetStreetHintResponseDTO:
      required: [ data ]
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/StreetHintDTO'
    StreetHintDTO:
      properties:
        street:
          type: string
    ValidateAddressResponseDTO:
      properties:
        isValidated:
          type: boolean
          default: false
        isCityValidated:
          type: boolean
          default: false
        lastCityValidationTime:
          type: string
          format: date-time
        isAddressValidated:
          type: boolean
        lastAddressValidationTime:
          type: string
          format: date-time
        fiasId:
          type: string
        cityFiasId:
          type: string
        settlementFiasId:
          type: string
        dataFullAddress:
          type: string
    DaDataAddressRequest:
      properties:
        query:
          type: string
        count:
          type: integer
        from_bound:
          $ref: '#/components/schemas/DaDataBoundDTO'
        to_bound:
          $ref: '#/components/schemas/DaDataBoundDTO'
    DaDataBoundDTO:
      properties:
        value:
          type: string
    BanType:
      type: string
      enum:
        - USER_BAN
        - COMMENT_BAN
        - PUBLISH_BAN
        - STORIES_BAN
        - BARGAIN_BAN
        - STREAM_BAN
        - WARNING
        - OSOCIAL_POST_BAN
        - OSOCIAL_COMMENT_BAN
        - COMMENT_SHADOW_BAN
    BanSearchFilerDTO:
      properties:
        banIds:
          type: array
          items:
            type: integer
            format: int64
        userIds:
          type: array
          items:
            type: integer
            format: int64
        banTypes:
          type: array
          items:
            $ref: '#/components/schemas/BanType'
        isBaned:
          type: boolean
        isDeleted:
          type: boolean
    AdminPanelUserBanDTO:
      properties:
        id:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        banType:
          $ref: '#/components/schemas/BanType'
        description:
          type: string
        createDate:
          type: integer
          format: int64
        startDate:
          type: integer
          format: int64
        endDate:
          type: integer
          format: int64
        isBaned:
          type: boolean
        isDeleted:
          type: boolean
        statusChangedUserId:
          type: integer
          format: int64
        adminComment:
          type: string
    CounterpartyType:
      type: string
      enum:
        - PHYS
        - IP
        - JUR
        - CARD
        - BONUS_12_STOREEZ
        - INTERNATIONAL
        - INTERNATIONAL_LEGAL_ENTITY
    AddressDTO:
      properties:
        id:
          type: integer
          format: int64
        zipCode:
          type: string
        country:
          type: string
        countryData:
          $ref: '#/components/schemas/CountryDTO'
        cityData:
          $ref: '#/components/schemas/CityDTO'
        region:
          type: string
        city:
          type: string
        address:
          type: string
        addressBreakdown:
          $ref: '#/components/schemas/AddressBreakdownDTO'
        address2:
          type: string
        address3:
          type: string
        fiasId:
          type: string
        regionFiasId:
          type: string
        cityFiasId:
          type: string
        settlementFiasId:
          type: string
        dadataFullAddress:
          type: string
        fullCityName:
          type: string
        fullAddress:
          type: string
        checked:
          type: boolean
        createTime:
          type: integer
          format: int64
        changeTime:
          type: integer
          format: int64
        cis:
          type: boolean
    AddressBreakdownDTO:
      properties:
        street:
          type: string
        house:
          type: string
        flat:
          type: string
    AddressEndpointDTO:
      title: AddressEndpointDTO
      type: object
      properties:
        address:
          $ref: '#/components/schemas/AddressDTO'
        firstName:
          type: string
        id:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        lastName:
          type: string
        patronymicName:
          type: string
        phone:
          type: string
        deleteTime:
          type: integer
          format: int64
        changeTime:
          type: integer
          format: int64
    CountryDTO:
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        uiCurrencyCode:
          type: string
        environment:
          type: string
          enum:
            - RU
            - INT
        isoCodeAlpha2:
          type: string
        imageUrl:
          type: string
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        requireZipcode:
          type: boolean
    CurrencyDTO:
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        sign:
          type: string
        isoCode:
          type: string
        isoNumber:
          type: integer
        base:
          type: boolean
        active:
          type: boolean
        selectedByDefault:
          type: boolean
    CityDTO:
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        region:
          type: string
    CounterpartyData:
      properties:
        id:
          type: integer
          format: int64
        active:
          type: boolean
        counterpartyType:
          $ref: '#/components/schemas/CounterpartyType'
        organisationForm:
          type: string
        companyName:
          type: string
        ipName:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
        inn:
          type: string
        bik:
          type: string
        kpp:
          type: string
        paymentAccount:
          type: string
        vatRateIndex:
          type: integer
    ConciergeAdditionalInfoDTO:
      properties:
        adminComment:
          type: string
        deliveryToBuyerDateHint:
          type: string
          format: date-time
        deliveryToBuyerDateHintInSeconds:
          type: integer
          format: int64
        clientChannelId:
          type: integer
          format: int64
        deliveryToBuyerDate:
          type: string
          format: date
    ConciergeClientChannelDTO:
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
      required: [ id, name ]
    OrderSourceInfoDTO:
      properties:
        id:
          type: integer
          format: int64
        displayName:
          type: string
      required: [ id, name ]
    ApiV2ConciergeClientChannelResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ConciergeClientChannelDTO'
    ApiV2DictionaryOrderSourceInfoResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrderSourceInfoDTO'
    ApiV2AddressCountyListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CountryDTO'
    ApiV2AddressCityListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AddressDTO'
    ApiV2OrderPaymentListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AdminOrderPaymentDTO'
    ApiV2PickupOptionListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PickupOptionDTO'
    ApiV2AdminCleanCartResponseDTO:
      properties:
        data:
          type: integer
          format: int64
    ApiV2AdminOrderFileListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrderFileDTO'
    ApiV2DisputeHistoryResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/DisputeHistoryItemDTO'
    ApiV2InternationalAddressCityListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CityDTO'
    ApiV2AddressListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AddressDTO'
    ApiV2AddressEndpointResponseDTO:
      properties:
        data:
          $ref: '#/components/schemas/AddressEndpointDTO'
    ApiV2EventDateEstimationListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/EventDateEstimationDTO'
    ApiV2EventDateEstimationHistoryListResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/EventDateEstimationHistoryDTO'
    FrontendAdminConfig:
      properties:
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        isInternational:
          type: boolean
      required: [ isInternational, currency ]
    CleanCartRequestDTO:
      properties:
        orderId:
          type: integer
          format: int64
        productIds:
          type: array
          items:
            type: integer
            format: int64
        orderPositionIds:
          type: array
          items:
            type: integer
            format: int64
    SaleRejectionReasonDTO:
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        isForAdmin:
          type: boolean
        adminDisplayName:
          type: string
      required: [ id, name, isForAdmin, adminDisplayName ]
    ApiV2SaleRejectionReasonResponseDTO:
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SaleRejectionReasonDTO'

    AdminOrderPaymentDTO:
      properties:
        paymentVersion:
          type: string
        paymentMethods:
          type: string
        paymentType:
          type: string
        amountInBase:
          type: number
          format: int64
        authorizeTime:
          type: string
          format: date-time
        authorizeExpireTime:
          type: string
          format: date-time
        captureAmountInBase:
          type: number
          format: int64
        captureTime:
          type: string
          format: date-time
        refundAmountInBase:
          type: number
          format: int64
        refundsTime:
          type: string
          format: date-time
        currencyAmount:
          type: number
          format: int64
        currencyRate:
          type: number
          format: int64

    RefundAmountReqDTO:
      properties:
        refundAmount:
          type: number
          format: int64
        comment:
          type: string

    OrderSetOrderSourceInfoRequest:
      properties:
        orderIds:
          type: array
          items:
            type: integer
            format: int64
        orderSourceInfoId:
          type: integer
          format: int64
      required: [ orderIds, orderSourceInfoId ]

    OrderSetOrderLegalEntityRequest:
      properties:
        orderIds:
          type: array
          items:
            type: integer
            format: int64
        legalEntityId:
          type: integer
          format: int64
      required: [ orderIds, legalEntityId ]

    OrderSetOrderLegalEntityConciergeRequest:
      properties:
        orderIds:
          type: array
          items:
            type: integer
            format: int64
      required: [ orderIds ]

    DateWithIntervalDTO:
      properties:
        date:
          type: string
          format: date-time
        timeInterval:
          $ref: '#/components/schemas/TimeIntervalDTO'

    PickupOptionDTO:
      properties:
        deliveryCompany:
          $ref: '#/components/schemas/DeliveryCompanyDTO'
        dateList:
          type: array
          items:
            $ref: '#/components/schemas/DateWithIntervalsDTO'

    DeliveryCompanyDTO:
      properties:
        id:
          type: number
          format: int64
        name:
          type: string

    DateWithIntervalsDTO:
      properties:
        date:
          type: string
          format: date-time
        timeIntervals:
          type: array
          items:
            $ref: '#/components/schemas/TimeIntervalDTO'

    TimeIntervalDTO:
      properties:
        id:
          type: number
          format: int64
        fromHour:
          type: number
          format: int32
        toHour:
          type: number
          format: int32
        displayName:
          type: string

    PickupInfoDTO:
      properties:
        deliveryCompanyId:
          type: number
          format: int64
        pickupDate:
          type: string
          format: date-time
        timeIntervalId:
          type: number
          format: int64

    DisputeHistoryItemDTO:
      properties:
        startedBy:
          $ref: '#/components/schemas/UserSimpleDTO'
        startedAt:
          type: string
          format: date-time
        closedBy:
          $ref: '#/components/schemas/UserSimpleDTO'
        closedAt:
          type: string
          format: date-time
        parentOrderId:
          type: number
          format: int64
      required: [ startedAt ]

    OrderFileDTO:
      properties:
        id:
          type: integer
          format: int64
        filename:
          type: string
        fileUrl:
          type: string
        fileOrder:
          type: integer
        type:
          $ref: '#/components/schemas/OrderFileTypeDTO'
      required: [id, filename, fileUrl, fileOrder, type]
    OrderFileTypeDTO:
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
      required: [id, name]
    OrderFileRemoveRequestDTO:
      properties:
        orderId:
          type: integer
          format: int64
        fileIdList:
          type: array
          items:
            type: integer
            format: int64
      required: [ orderId, fileIdList ]

    UserSimpleDTO:
      properties:
        id:
          type: integer
          format: int64
        nickname:
          type: string
      required: [ id, nickname ]

    EventDateEstimationDTO:
      properties:
        eventType:
          $ref: '#/components/schemas/EstimatedEventTypeDTO'
        estimatedDateFrom:
          type: string
          format: date-time
        estimatedDateTo:
          type: string
          format: date-time

    EventDateEstimationHistoryDTO:
      properties:
        calculationTime:
          type: string
          format: date-time
        estimatedDateFrom:
          type: string
          format: date-time
        estimatedDateTo:
          type: string
          format: date-time
        calculationContext:
          type: string

    EstimatedEventTypeDTO:
      type: string
      enum:
        - DELIVERY_FROM_SELLER_COMPLETION
        - EXPERTISE_COMPLETION
        - DELIVERY_TO_BUYER_COMPLETION
