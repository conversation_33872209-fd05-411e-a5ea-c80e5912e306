openapi: 3.0.3

info:
  title: Oskelly external integration API v1, gazprom
  description: Oskelly external integration API v1, gazprom
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

paths:
  /api/v1/integration/gazprom/users/{id}:
    get:
      tags:
        - gazprom
      summary: Получить информацию о пользователе по идентификатору
      operationId: getUserById
      parameters:
        - name: id
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GazpromUserDTO'
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера
    put:
      tags:
        - gazprom
      summary: Обновление информации о пользователе
      operationId: updateUser
      requestBody:
        description: Запрос на обновление пользователя
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GazpromUserUpdateRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GazpromUserDTO'
        '400':
          description: Некорректный код статуса
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера
  /api/v1/integration/gazprom/users:
    get:
      tags:
        - gazprom
      summary: Поиск пользователя по телефону
      operationId: getUserByPhone
      parameters:
        - name: phone
          in: query
          description: Телефон для поиска
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GazpromUserDTO'
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера
    post:
      tags:
        - gazprom
      summary: Создание пользователя
      operationId: createUser
      requestBody:
        description: Запрос на создание пользователя
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GazpromUserCreateRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GazpromUserDTO'
        '400':
          description: Некорректный запрос
        '500':
          description: Внутренняя ошибка сервера
components:
  schemas:
    GazpromUserDTO:
      required:
        - id
        - status
        - gazpromBonus
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Идентификатор пользователя
        status:
          type: string
          description: Код статуса пользователя O!Community
        gazpromBonus:
          type: boolean
          description: Признак вхождения в программу Газпром Бонус
    GazpromUserCreateRequest:
      required:
        - phone
      type: object
      properties:
        phone:
          type: string
          description: Номер телефона по которому нужно создать пользователя
    GazpromUserUpdateRequest:
      required:
        - status
      type: object
      properties:
        status:
          type: string
          description: Код статуса пользователя O!Community. Допустимые значения - BEGINNER, BUDDY, BESTIE, EXPERT_FRIEND или VIF
          enum:
            - BEGINNER
            - BUDDY
            - BESTIE
            - EXPERT_FRIEND
            - VIF
        statusDate:
          type: integer
          format: int64
          description: Дата-время окончания действия статуса, в unix time в utc таймзоне
        gazpromBonus:
          type: boolean
          description: Признак вхождения в программу Газпром Бонус