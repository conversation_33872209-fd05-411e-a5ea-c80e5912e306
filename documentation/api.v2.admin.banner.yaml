openapi: 3.0.3
info:
  title: Oskelly Admin API v2, banner
  description: Oskelly Admin API v2, banner
  version: "1.0"
paths:
  /api/v2/admin/banner:
    post:
      tags:
        - bannerSetting
      summary: Create empty banner setting
      operationId: createEmptyBannerSetting
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBannerSetting'
  /api/v2/admin/banner/{bannerId}:
    get:
      tags:
        - bannerSetting
      summary: get banner setting
      operationId: getBannerSetting
      parameters:
        - in: path
          name: bannerId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBannerSetting'
        '404':
          description: Not Found
  /api/v2/admin/banner/delete/{bannerId}:
    post:
      tags:
        - bannerSetting
      summary: delete banner setting
      operationId: deleteBannerSetting
      parameters:
        - in: path
          name: bannerId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
        '404':
          description: Not Found
  /api/v2/admin/banner/update:
    post:
      tags:
        - bannerSetting
      summary: Update existing banner setting
      operationId: updateBannerSetting
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BannerSettingDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBannerSetting'
        '404':
          description: Not Found
        '400':
          description: Invalid request body
  /api/v2/admin/banner/list:
    get:
      tags:
        - bannerSetting
      summary: get banner setting list
      operationId: getBannerSettingList
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBannerSettingDTO'
  /api/v2/admin/banner/listAll:
    get:
      tags:
        - bannerSetting
      summary: get all banner setting list
      operationId: getAllBannerSettingList
      parameters:
        - in: query
          name: pageNumber
          schema:
            type: integer
            default: 0
          required: false
        - in: query
          name: pageSize
          schema:
            type: integer
            default: 50
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBannerSettingDTO'
  /api/v2/admin/banner/conciergeList:
    get:
      tags:
        - bannerSetting
      summary: get concierge banner list
      operationId: getConciergeBannerList
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfConciergeBannerSettingContainerDtoList'

  /api/v2/admin/banner/productRequestList:
    get:
      tags:
        - banner-setting-controller-api-v-2
      summary: getProductRequestBannerList
      operationId: getProductRequestBannerListUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductRequestBannerSettingContainerDtoList'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false

components:
  schemas:
    Api2ResponseOfProductTotalCount:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          type: integer
          format: int64
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfBannerSetting:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/BannerSettingDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfBannerSettingDTO:
      title: Api2ResponseOfBannerSettingDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BannerSettingDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfPageOfBannerSettingDTO:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfBannerSettingDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfConciergeBannerSettingContainerDtoList:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ConciergeBannerSettingContainerDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfProductRequestBannerSettingContainerDtoList:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductRequestBannerSettingContainerDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PageOfBannerSettingDto:
      title: PageOfBannerSettingDto
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BannerSettingDto'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    BannerSettingDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        primaryPageId:
          type: string
        propertiesBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/BannerPropertiesBlockDto'
        generalInfoBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/GeneralInfoBlock'
        status:
          type: string
          enum:
            - DRAFT
            - PUBLISH
        bannerDeeplink:
          type: string
          description: deeplink for redirect to banner page
        publicationTime:
          type: string
          format: date-time
          example: 2022-05-30T15:26:14.883
        filter:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'
        productCount:
          type: integer
          format: int64
        productCollectionBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        bestSellersBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        selectedProductsBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        blogBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        storiesBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        promoBannerId:
          type: integer

    ConciergeBannerSettingContainerDto:
      type: object
      properties:
        bannerSettingId:
          type: string
        sex:
          type: string
          enum:
            - MALE
            - FEMALE
        createTime:
          type: string
          format: date-time
        updateTime:
          type: string
          format: date-time

    ProductRequestBannerSettingContainerDto:
      type: object
      properties:
        bannerSettingId:
          type: string
        sex:
          type: string
          enum:
            - MALE
            - FEMALE
        createTime:
          type: string
          format: date-time
        updateTime:
          type: string
          format: date-time
