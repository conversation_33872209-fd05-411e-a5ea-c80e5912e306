openapi: 3.0.3
info:
  title: Oskelly Admin API v2, button control
  description: Oskelly Admin API v2, button control
  version: "1.0"
paths:
  /api/v2/admin/buttoncontrol/update:
    post:
      tags:
        - adminPanel
        - buttonControl
        - update
      summary: Update button control
      operationId: updateButtonControl
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ButtonControlDto'
      responses:
        '200':
          description: updated button control
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfButtonControl'

  /api/v2/admin/buttoncontrol:
    get:
      tags:
        - adminPanel
        - buttonControl
        - byIds
      summary: Get button control
      operationId: getButtonControlList
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
      responses:
        '200':
          description: List of ButtonControlDto
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfButtonControlList'


components:
  schemas:
    Api2ResponseOfButtonControl:
      title: Api2ResponseOfButtonControl
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ButtonControlDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfButtonControlList:
      title: Api2ResponseOfButtonControlList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ButtonControlDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    ButtonControlDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        url:
          type: string
        imageUrl:
          type: string
          description: path to image
        imageBase64:
          type: string
          description: image in Base64 format