openapi: 3.0.3

info:
  title: Oskelly Admin API v2, currency_rates
  description: Oskelly Admin API v2, currency_rates
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

paths:
  /api/v2/admin/currency/rate:
    get:
      tags:
        - CurrencyRate
      summary: Get adminpanel currency rates list
      operationId: getCurrencyRates
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCurrencyRatesDTO'
  /api/v2/admin/currency/rate/update:
    post:
      tags:
        - CurrencyRate
      summary: Update currency rate
      operationId: updateCurrencyRate
      parameters:
        - $ref: '#/components/parameters/RateIdParam'
      requestBody:
        description: Currency Rate Update Request, contains state which the appropriate rate to be set with
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCurrencyRateRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCurrencyRateDTO'
  /api/v2/admin/currency/base:
    get:
      tags:
        - CurrencyBase
      summary: Get adminpanel base currency
      operationId: getBaseCurrency
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCurrencyDTO'

components:
  parameters:
    RateIdParam:
      name: rateId
      in: query
      description: Promocode ID
      required: true
      schema:
        type: integer
        format: int64

  schemas:
    Api2ResponseCurrencyRatesDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CurrencyRatesDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    Api2ResponseCurrencyRateDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CurrencyRateDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    Api2ResponseCurrencyDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CurrencyDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    UpdateCurrencyRateRequest:
      required:
        - rateValue
        - commission
      type: object
      properties:
        rateValue:
          type: number
          format: bigdecimal
        commission:
          type: number
          format: bigdecimal
    CurrencyRatesDTO:
      required:
        - rates
      type: object
      properties:
        rates:
          type: array
          items:
            $ref: '#/components/schemas/CurrencyRateDTO'
    CurrencyRateDTO:
      required:
        - id
        - currency
      type: object
      properties:
        id:
          type: integer
          format: int64
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        currencyTo:
          $ref: '#/components/schemas/CurrencyDTO'
        rateValue:
          type: number
          format: bigdecimal
        commission:
          type: number
          format: bigdecimal
    CurrencyDTO:
      required:
        - id
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
          description: Currency name
        sign:
          type: string
          description: Currency sign
        isoCode:
          type: string
          description: Currency isoCode
        isoNumber:
          type: integer
          description: Currency isoNumber
