openapi: 3.0.3
info:
  title: Oskelly Admin API v2, Customizable banner
  description: Oskelly Admin API v2, Customizable banner
  version: "1.0"
paths:
  /api/v2/admin/banner/customizablepage:
    post:
      tags:
        - bannerSetting
        - customizable
      summary: Create empty customizable banner setting
      operationId: createEmptyCustomizableBannerSetting
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCustomizableBannerSetting'

  /api/v2/admin/banner/customizablepage/{bannerId}:
    get:
      tags:
        - bannerSetting
        - customizable
      summary: get customizable banner setting
      operationId: getCustomizableBannerSetting
      parameters:
        - in: path
          name: bannerId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCustomizableBannerSetting'
        '404':
          description: Not Found

  /api/v2/admin/banner/customizablepage/update:
    post:
      tags:
        - bannerSetting
        - customizable
      summary: Update existing  customizablebanner setting
      operationId: updateCustomizableBannerSetting
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomizableBannerSettingDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCustomizableBannerSetting'
        '404':
          description: Not Found
        '400':
          description: Invalid request body

components:
  schemas:
    Api2ResponseOfCustomizableBannerSetting:
      title: Api2ResponseOfCustomizableBannerSetting
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CustomizableBannerSettingDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    CustomizableBannerSettingDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        primaryPageId:
          type: string
        propertiesBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/BannerPropertiesBlockDto'
        generalInfoBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/GeneralInfoBlock'
        status:
          type: string
          enum:
            - DRAFT
            - PUBLISH
        bannerDeeplink:
          type: string
          description: deeplink for redirect to banner page
        publicationTime:
          type: string
          format: date-time
          example: 2022-05-30T15:26:14.883
        filter:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'
        productCount:
          type: integer
          format: int64
        contentBlocks:
          type: array
          items:
            $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        fixedContentBlocks:
          type: array
          items:
            $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        promoBannerId:
          type: string
