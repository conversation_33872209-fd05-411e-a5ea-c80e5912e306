openapi: 3.0.3

info:
  title: Oskelly Admin API v2, deeplink
  description: Oskelly Admin API v2, deeplink
  version: "1.0"

paths:
  /api/v2/admin/deeplink/cart/add:
    get:
      tags:
        - deeplink
      summary: Get add to cart deeplink;
      operationId: getAddToCartDeeplink
      parameters:
        - name: productId
          in: query
          required: true
          description: ID of the product
          schema:
            type: integer
            format: int64
        - name: sizeId
          in: query
          required: true
          description: ID of the selected size
          schema:
            type: integer
            format: int64
        - name: count
          in: query
          required: true
          description: Number of items to add to cart
          schema:
            type: integer
            format: int32
            minimum: 1
        - name: bitrixDealId
          in: query
          required: false
          description: ID of the Bitrix deal (optional)
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAddToCartDeeplinkResultDTO'

components:
  schemas:
    Api2ResponseOfAddToCartDeeplinkResultDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AddToCartDeeplinkResultDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string

    AddToCartDeeplinkResultDTO:
      type: object
      properties:
        addToCartDeeplink:
          type: string