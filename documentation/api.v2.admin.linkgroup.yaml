openapi: 3.0.3
info:
  title: Oskelly Admin API v2, Link Group
  description: Oskelly Admin API v2, Link Group
  version: "1.0"
paths:
  /api/v2/admin/linkGroup:
    post:
      tags:
        - link_group
        - update
      summary: Update Link Group
      operationId: updateLinkGroup
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.admin.partial.yaml#/components/schemas/LinkGroupDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfLinkGroupDto'
    get:
      tags:
        - link_group
        - getByIds
      summary: Get By ids
      operationId: getLinkGroupsByIds
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfLinkGroupDtoList'


components:
  schemas:
    Api2ResponseOfLinkGroupDto:
      title: Api2ResponseOfLinkGroupDto
      type: object
      properties:
        data:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/LinkGroupDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfLinkGroupDtoList:
      title: Api2ResponseOfLinkGroupDto
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './api.v2.admin.partial.yaml#/components/schemas/LinkGroupDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string