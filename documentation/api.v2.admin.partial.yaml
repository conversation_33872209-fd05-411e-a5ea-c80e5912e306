openapi: 3.0.3
info:
  title: Oskelly Admin API v2, partial
  description: Oskelly Admin API v2, partial
  version: "1.0"
paths:
components:
  parameters:
    IdList:
      name: ids
      in: query
      description: List of ids
      required: false
      schema:
        type: array
        items:
          type: string
  schemas:
    FilterContentDto:
      type: object
      properties:
        categoryAndUserFilter:
          $ref: '#/components/schemas/CategoryAndUserFilterDto'
        productListFilter:
          $ref: '#/components/schemas/ProductListFilterDto'
        userProfileFilter:
          $ref: '#/components/schemas/UserProfileFilterDto'
        productRequestCategoryAndUserFilter:
          $ref: '#/components/schemas/ProductRequestCategoryAndUserFilterDto'
        productRequestListFilter:
          $ref: '#/components/schemas/ProductRequestListFilterDto'
        emptyFilter:
          $ref: '#/components/schemas/EmptyFilterDto'
        enableFiltration:
          type: boolean
        title:
          type: string
    Api2ResponseOfIdentDto:
      title: Api2ResponseOfIdentDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/IdentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
        additionalProperties:
          type: string
    IdentDTO:
      title: IdentDTO
      type: object
      properties:
        alignment:
          $ref: '#/components/schemas/AlignmentType'
        indentTop:
          type: number
        indentBottom:
          type: number
        indentLeft:
          type: number
        indentRight:
          type: number
    CategoryAndUserFilterDto:
      type: object
      properties:
        categoryIds:
          type: array
          items:
            type: integer
            format: int64
        brandIds:
          type: array
          items:
            type: integer
            format: int64
        productModelIds:
          type: array
          items:
            type: integer
            format: int64
        colorIds:
          type: array
          items:
            type: integer
            format: int64
        materialIds:
          type: array
          items:
            type: integer
            format: int64
        conditionIds:
          type: array
          items:
            type: integer
            format: int64
        price:
          type: object
          properties:
            min:
              type: number
            max:
              type: number
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserDto'
        sellerTypes:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/SellerType'
        isOskellyChoice:
          type: boolean
        isBeegz:
          type: boolean
        isInBoutique:
          type: boolean
        isInOskellyStock:
          type: boolean
        isExclusiveSelection:
          type: boolean
        isNewCollection:
          type: boolean
        isVintage:
          type: boolean
        boutiqueLocationTagIds:
          type: array
          items:
            type: integer
            format: int64
        newResaleSelection:
          $ref: '#/components/schemas/NewResaleSelectionDTO'

    NewResaleSelectionDTO:
      type: string
      enum:
        - ALL
        - NEW
        - RESALE

    AlignmentType:
      type: string
      enum:
        - CENTER
        - DOWN

    ProductListFilterDto:
      type: object
      properties:
        productIds:
          type: array
          items:
            type: integer
            format: int64

    UserProfileFilterDto:
      type: object
      properties:
        userId:
          type: integer
          format: int64

    UserDto:
      type: object
      required:
        - id
      properties:
        id:
          type: integer
          format: int64
        nickname:
          type: string
        isPro:
          type: boolean

    GeneralInfoBlock:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        additionalDescription:
          type: string
        imageUrl:
          type: string
          description: path to image
        imageBase64:
          type: string
          description: image in Base64 format
        buttonTitle:
          type: string
        buttonUrl:
          type: string
        descriptionButtonText:
          type: string
        descriptionButtonUrl:
          type: string
        shareButtonText:
          type: string

    ContentBlock:
      type: object
      properties:
        blockId:
          type: string
        title:
          type: string
        isEnable:
          type: boolean
        createTime:
          type: string
          format: date-time
        updateTime:
          type: string
          format: date-time
        contentIds:
          type: array
          items:
            type: string
        contentList:
          type: array
          items:
            $ref: '#/components/schemas/SelectedProductDto'
        segments:
          type: array
          items:
            $ref: '#/components/schemas/ContentBlockSegmentDto'
        type:
          $ref: '#/components/schemas/ContentBlockType'

    SelectedProductDto:
      type: object
      properties:
        productId:
          type: string
        description:
          type: string

    TextSlideDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        iconPath:
          type: string
        iconBase64:
          type: string
          description: icon in Base64 format
        deepLinkUrl:
          type: string
        description:
          type: string

    ContentBlockType:
      title: ContentBlockType
      type: string
      enum:
        - BANNER
        - COLLECTION
        - ADDITIONAL_COLLECTION_1
        - ADDITIONAL_COLLECTION_2
        - BLOG
        - TEXT_SLIDES
        - BEST_SELLERS
        - SELECTED_PRODUCT
        - STORIES
        - STOREEZ
        - INSTAGRAM_FEED
        - SHELF
        - FILTERABLE_SHELF
        - VERTICAL_COLLECTION
        - HORIZONTAL_COLLECTION
        - BUTTON_CONTROL
        - TEXT_CONTROL
        - LINK_GROUPS_BLOCK
        - OSOCIAL_POSTS_COLLECTION
        - SPACER
        - ORDERS
        - PROMO_AUTO_COLLECTION

    ContentBlockSegmentDto:
      type: object
      properties:
        id:
          type: string
        segmentId:
          type: integer
          format: int64
        title:
          type: string
        name:
          type: string
        description:
          type: string
        promoCode:
          type: string
        properties:
            allOf:
              - $ref: '#/components/schemas/BaseSegmentPropertiesDto'
              - $ref: '#/components/schemas/FilterableShelfSegmentPropertiesDto'
              - $ref: '#/components/schemas/ShelfSegmentPropertiesDto'
              - $ref: '#/components/schemas/StoreezSegmentPropertiesDto'
              - $ref: '#/components/schemas/InstagramFeedSegmentPropertiesDto'
              - $ref: '#/components/schemas/TextSlidesSegmentPropertiesDto'
              - $ref: '#/components/schemas/OSocialPostsCollectionSegmentPropertiesDto'
        contentIds:
          type: array
          items:
            type: string
        contentList:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/SelectedProductDto'
              - $ref: '#/components/schemas/TextSlideDto'

    BaseSegmentPropertiesDto:
      type: object

    FilterableShelfSegmentPropertiesDto:
      type: object
      properties:
        withFilterOptions:
          type: boolean

    ShelfSegmentPropertiesDto:
      type: object
      properties:
        withBannerInformation:
          type: boolean
        isSellerHidden:
          type: boolean

    StoreezSegmentPropertiesDto:
      type: object
      properties:
        storeezId:
          type: string

    TextSlidesSegmentPropertiesDto:
      type: object
      properties:
        slideInterval:
          description: Интервал перелистывания в мс
          type: integer
          format: int64

    BannerPropertiesBlockDto:
      type: object
      required:
        - isCatalogBanner
        - isMaleConciergeBanner
        - isFemaleConciergeBanner
      properties:
        isCatalogBanner:
          type: boolean
        isMaleConciergeBanner:
          type: boolean
        isFemaleConciergeBanner:
          type: boolean
        isMaleProductRequestBanner:
          type: boolean
        isFemaleProductRequestBanner:
          type: boolean
        isFlexiblyButton:
          type: boolean
        isEndButton:
          type: boolean
        isJournalMode:
          type: boolean

    InstagramFeedSegmentPropertiesDto:
      type: object
      properties:
        maxItemsCount:
          type: integer
        minLikesCount:
          type: integer
        minCommentsCount:
          type: integer
        toInstagramPageButtonTitle:
          type: string
        indicatorsEnabled:
          type: boolean
          default: false

    OSocialPostsCollectionSegmentPropertiesDto:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int64
        authorIds:
          type: array
          items:
            type: integer
            format: int64
        tagIds:
          type: array
          items:
            type: integer
            format: int64
        publishedAtFrom:
          type: string
          format: date
        publishedAtTo:
          type: string
          format: date

    ProductRequestCategoryAndUserFilterDto:
      type: object
      properties:
        brandIds:
          type: array
          items:
            type: integer
            format: int64
        categoryIds:
          type: array
          items:
            type: integer
            format: int64
        colorIds:
          type: array
          items:
            type: integer
            format: int64
        conditionIds:
          type: array
          items:
            type: integer
            format: int64
        materialIds:
          type: array
          items:
            type: integer
            format: int64
        price:
          $ref: '#/components/schemas/PriceDto'
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserDto'
      title: ProductRequestCategoryAndUserFilterDto

    ProductRequestListFilterDto:
      type: object
      properties:
        productRequestIds:
          type: array
          items:
            type: integer
            format: int64
      title: ProductRequestListFilterDto

    EmptyFilterDto:
      type: object
      title: EmptyFilterDto

    PriceDto:
      type: object
      properties:
        max:
          type: number
          format: double
        min:
          type: number
          format: double
      title: PriceDto

    LinkGroupDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        links:
          type: array
          items:
            $ref: '#/components/schemas/LinkDto'

    LinkDto:
      type: object
      properties:
        text:
          type: string
        url:
          type: string