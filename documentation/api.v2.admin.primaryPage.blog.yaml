openapi: 3.0.3
info:
  title: Oskelly Admin API v2, product blog
  description: Oskelly Admin API v2, product blog
  version: "1.0"
paths:
  /api/v2/admin/blog:
    post:
      tags:
        - adminPanel
        - productBlog
      summary: Update product blog
      operationId: updateProductBlog
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductBlogDtoForUpdate'
      responses:
        '200':
          description: updated product Blog
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductBlog'
    get:
      tags:
        - adminPanel
        - productBlog
      summary: Get product blog
      operationId: getProductBlogList
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
      responses:
        '200':
          description: List of ProductBlog
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductBlogList'

components:
  parameters:
  schemas:
    Api2ResponseOfProductBlog:
      title: Api2ResponseOfProductBlog
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductBlogDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfProductBlogList:
      title: Api2ResponseOfProductBlogList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductBlogDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    ProductBlogDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        title:
          type: string
        imageUrl:
          type: string
          description: path to image
        imageBase64:
          type: string
          description: image in Base64 format
        blogUrl:
          type: string

    ProductBlogDtoForUpdate:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        imageUrl:
          type: string
          description: path to image
        imageBase64:
          type: string
          description: image in Base64 format
        blogUrl:
          type: string