openapi: 3.0.3
info:
  title: Oskelly Admin API v2, banner
  description: Oskelly Admin API v2, banner
  version: "1.0"
paths:
  /api/v2/admin/primary/page:
    get:
      tags:
        - primaryPage
      summary: Get all content for show primary page setting
      operationId: getPrimaryPage
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPrimaryPageSettingContainerDto'

  /api/v2/admin/primary/page/setting/{id}:
    get:
      tags:
        - primaryPage
      summary: Get primary setting content
      operationId: getPrimaryPageSetting
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPrimaryPageSettingDto'
        '404':
          description: Primary page not found or id is null

  /api/v2/admin/primary/customizablepage/setting/{id}:
    get:
      tags:
        - primaryPage
        - customizable
      summary: Get primary setting content
      operationId: getСustomizablePrimaryPageSetting
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPrimaryCustomizablePageSettingDto'
        '404':
          description: Primary page not found or id is null

  /api/v2/admin/primary/page/setting/update:
    post:
      tags:
        - primaryPage
        - setting
      summary: Update only not null fields
      operationId: updatePrimaryPageSetting
      requestBody:
        description: Path to update
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrimaryPageSettingDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPrimaryPageSettingDto'
        '404':
          description: Primary page not found or id is null

  /api/v2/admin/primary/customizablepage/setting/update:
    post:
      tags:
        - primaryPage
        - setting
        - customizable
      summary: Update all fields
      operationId: updateСustomizablePrimaryPageSetting
      requestBody:
        description: Path to update
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrimaryCustomizablePageSettingDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPrimaryCustomizablePageSettingDto'
        '404':
          description: Primary page not found or id is null

  /api/v2/admin/primary/promobanner/{id}:
    get:
      tags:
        - prrimaryPage
        - promo
      summary: get promo banner content
      operationId: getPromoBannerById
      parameters:
        - in: path
          name: id
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPromoBannerDto'
        '404':
          description: Promo banner not found
  /api/v2/admin/primary/promobanner/update:
    post:
      tags:
        - prrimaryPage
        - promo
      summary: update promo banner
      operationId: updatePromoBanner
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PromoBannerDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPromoBannerDto'
        '404':
          description: Promo banner not found

  /api/v2/admin/primary/promobanner/create/bannersetting/{bannerSettingId}:
    post:
      tags:
        - prrimaryPage
        - promo
        - banner setting
      summary: create promo banner by banner setting id
      operationId: createPromoBannerByBannerSettingId
      parameters:
        - in: path
          name: bannerSettingId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPromoBannerDto'
        '404':
          description: BannerSetting not found

  /api/v2/admin/segment/allSegments:
    get:
      tags:
        - primaryPage
        - userSegment
      summary: Get all user segments
      operationId: getAllSegments
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSegmentDtoList'

  /api/v2/admin/segment/{segmentId}:
    delete:
      tags:
        - primaryPage
        - userSegment
        - deletet
      operationId: deltetUserSegmentById
      parameters:
        - in: path
          name: segmentId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSegmentDto'

  /api/v2/admin/segment/update:
    post:
      tags:
        - primaryPage
        - userSegment
        - update
      summary: Update user segments
      operationId: updateSegment
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SegmentDTO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSegmentDto'

  /api/v2/admin/segment/upload/csv/{segmentId}:
    post:
      tags:
        - primaryPage
        - userSegment
        - csv
      summary: Upload userIds from csv file for segment
      operationId: uploadUserIdsForSegmentFromCsvFile
      parameters:
        - in: path
          name: segmentId
          schema:
            type: string
          required: true
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              type: object
              properties:
                file:
                  type: string
                  format: binary
            encoding:
              data:
                contentType: text/plain
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfUploadUserIdsForSegmentDTO'

  /api/v2/admin/primary/customizablepage/contentblock/availabletypes:
    get:
      tags:
        - contentBlockType
        - customizable
      summary: Get available content block's types
      operationId: getAvailableContentBlockTypes
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfContentBlockTypeDtoList'

components:
  schemas:
    Api2ResponseOfPrimaryPageSettingContainerDto:
      title: Api2ResponseOfPrimaryPageSettingContainerDto
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PrimaryPageSettingContainerDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfContentBlockTypeDtoList:
      title: Api2ResponseOfContentBlockTypeDtoList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ContentBlockTypeDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PrimaryPageSettingContainerDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        promoBannerId:
          type: string
        categorySettings:
          type: array
          items:
            $ref: '#/components/schemas/PrimaryPageCategoryDto'
        sexToSettingIdMap:
          type: object
          properties:
            FEMALE:
              type: string
            MALE:
              type: string
            CHILD:
              type: string
            LIFESTYLE:
              type: string

    PrimaryPageCategoryDto:
      type: object
      required:
        - primaryPageCategory
      properties:
        primaryPageCategory:
          $ref: '#/components/schemas/PrimaryPageCategory'
        conditionSettings:
          type: array
          items:
            $ref: '#/components/schemas/PrimaryPageConditionDto'

    PrimaryPageConditionDto:
      type: object
      required:
        - condition
        - primaryPageSettingId
      properties:
        condition:
          $ref: '#/components/schemas/PrimaryPageCondition'
        primaryPageSettingId:
          type: string

    PrimaryPageCategory:
      title: PrimaryPageCategory
      type: string
      enum:
        - MALE
        - FEMALE
        - CHILD
        - LIFESTYLE

    PrimaryPageCondition:
      title: PrimaryPageCondition
      type: string
      enum:
        - ALL
        - NEW
        - RESALE

    ContentBlockTypeDto:
      type: object
      required:
        - type
        - title
      properties:
        title:
          type: string
        type:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlockType'

    Api2ResponseOfPrimaryPageSettingDto:
      title: Api2ResponseOfPrimaryPageSettingDto
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PrimaryPageSettingDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PrimaryPageSettingDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        type:
          $ref: './partial.yaml#/components/schemas/PrimaryPageType'
        bannerBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        bannerCollectionBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        additionalCollectionBlock1:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        additionalCollectionBlock2:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        blogBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        storiesBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'

    Api2ResponseOfPrimaryCustomizablePageSettingDto:
      title: Api2ResponseOfPrimaryPageSettingDto
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PrimaryCustomizablePageSettingDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PrimaryCustomizablePageSettingDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        type:
          $ref: './partial.yaml#/components/schemas/PrimaryPageType'
        contentBlocks:
          type: array
          items:
            $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'
        fixedContentBlocks:
          type: array
          items:
            oneOf:
              - $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlock'

    Api2ResponseOfPromoBannerDto:
      title: Api2ResponseOfPromoBannerDto
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PromoBannerDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfSegmentDtoList:
      title: Api2ResponseOfSegmentDtoList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SegmentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfSegmentDto:
      title: Api2ResponseOfSegmentDto
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SegmentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfUploadUserIdsForSegmentDTO:
      title: Api2ResponseOfUploadUserIdsForSegmentDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/UploadUserIdsForSegmentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PromoBannerDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        isEnable:
          type: boolean
        description:
          type: string
        segments:
          type: array
          items:
            $ref: './api.v2.admin.partial.yaml#/components/schemas/ContentBlockSegmentDto'

    SegmentDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        userCount:
          type: integer
          format: int64
        isEditable:
          type: boolean
        deleteTime:
          type: string
          format: date-time

    UploadUserIdsForSegmentDTO:
      type: object
      properties:
        userIdsCountInFile:
          type: integer
        actualCountInDBAfterUploading:
          type: integer