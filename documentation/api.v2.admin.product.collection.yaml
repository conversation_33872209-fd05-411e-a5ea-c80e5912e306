openapi: 3.0.3
info:
  title: Oskelly Admin API v2, product collection
  description: Oskelly Admin API v2, product collection
  version: "1.0"
paths:
  /api/v2/admin/productCollection:
    post:
      tags:
        - adminPanel
        - productCollection
      summary: Update product collection
      operationId: updateProductCollection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductCollectionDtoForUpdate'
      responses:
        '200':
          description: updated product collection
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductCollection'
    get:
      tags:
        - adminPanel
        - productCollection
      summary: List product collections
      operationId: getProductCollectionList
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
        - $ref: '#/components/parameters/ProjectionName'
      responses:
        '200':
          description: List of ProductCollection
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductCollectionDTO'

  /api/v2/admin/productCollection/listAll:
    get:
      tags:
        - adminPanel
        - productCollection
      summary: List all product collections
      operationId: getAllProductCollectionList
      parameters:
        - $ref: '#/components/parameters/ProjectionName'
        - in: query
          name: pageNumber
          schema:
            type: integer
            default: 0
          required: false
        - in: query
          name: pageSize
          schema:
            type: integer
            default: 50
          required: false
      responses:
        '200':
          description: List of ProductCollection
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfProductCollectionDTO'

  /api/v2/admin/productCollection/delete/{collectionId}:
    post:
      tags:
        - adminPanel
        - productCollection
      summary: Delete product collection
      operationId: deleteProductCollection
      parameters:
        - in: path
          name: collectionId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
        '404':
          description: Not Found

components:
  parameters:
    ProjectionName:
      name: projection
      in: query
      description: The name of prjection of data
      required: false
      schema:
        type: string
        default: FULL
        enum:
          - FULL
          - SMALL_CARD
  schemas:
    Api2ResponseOfProductCollection:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductCollectionDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfProductCollectionDTO:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductCollectionDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfPageOfProductCollectionDTO:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfProductCollectionDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PageOfProductCollectionDto:
      title: PageOfProductCollectionDto
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductCollectionDto'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    ProductCollectionDto:
      type: object
      required:
        - id
      properties:
        id:
          type: string
        status:
          type: string
        generalInfoBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/GeneralInfoBlock'
        productCount:
          type: integer
          format: int64
        publicationTime:
          type: string
          format: date-time
          example: 2022-05-30T15:26:14.883
        collectionUrl:
          type: string
        filter:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'
        primaryPageId:
          type: string
        bannerId:
          type: string

    ProductCollectionDtoForUpdate:
      type: object
      properties:
        id:
          type: string
        generalInfoBlock:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/GeneralInfoBlock'
        productCount:
          type: integer
          format: int64
        collectionUrl:
          type: string
        filter:
          $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'