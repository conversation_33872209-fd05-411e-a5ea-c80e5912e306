openapi: 3.0.3
info:
  title: Oskelly Admin API v2, product
  description: Oskelly Admin API v2, product
  version: "1.0"
paths:
  /api/v2/admin/product/indent:
    get:
      tags:
        - admin-panel-product-controller
      summary: getAlignmentAndIndent
      operationId: getAlignmentAndIndentUsingGET
      parameters:
        - name: categoryId
          in: query
          description: Идентификатор категории
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './api.v2.admin.partial.yaml#/components/schemas/Api2ResponseOfIdentDto'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/admin/product/find:
    get:
      operationId: findProducts
      parameters:
        - in: query
          name: query
          required: false
          schema:
            type: string
        - in: query
          name: id
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: currencyCode
          required: false
          schema:
            type: string
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfProductShortDTO'
          description: OK
      tags:
        - admin-panel-product-controller
  /api/v2/admin/product/find/page:
    get:
      operationId: findProductsPage
      parameters:
        - in: query
          name: query
          required: false
          schema:
            type: string
        - in: query
          name: id
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: currencyCode
          required: false
          schema:
            type: string
        - in: query
          name: page
          required: false
          schema:
            type: integer
        - in: query
          name: pageLength
          required: false
          schema:
            type: integer
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfProductShortDTO'
          description: OK
      tags:
        - admin-panel-product-controller
  /api/v2/admin/product/total:
    post:
      tags:
        - bannerSetting
      summary: Get product total for given filter
      operationId: getProductTotal
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'
      responses:
        '200':
          description: Total count of products
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductTotalCount'
components:
  schemas:
    Api2Response:
      type: object
      properties:
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfProductTotalCount:
      title: Api2ResponseOfCategoryTree
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: integer
              format: int64

    Api2ResponseListOfProductShortDTO:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ProductShortDTO'

    Api2ResponsePageOfProductShortDTO:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/PageOfProductShortDTO'

    PageOfProductShortDTO:
      title: PageOfProductShortDTO
      type: object
      properties:
        totalPages:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int32
        itemsCount:
          type: integer
          format: int32
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductShortDTO'

    ProductShortDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        image:
          $ref: '#/components/schemas/ProductImageDTO'
        displayName:
          type: string
        stateDisplayName:
          type: string
        currentPrice:
          type: number
        currencyCode:
          type: string
        brand:
          $ref: './partial.yaml#/components/schemas/BrandShortDTO'
        category:
          $ref: './partial.yaml#/components/schemas/CategoryDisplayNameDTO'

      required:
        - id
        - displayName
        - stateDisplayName
        - currentPrice
        - currencyCode
    ProductImageDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        order:
          type: integer
          format: int32
        path:
          type: string
