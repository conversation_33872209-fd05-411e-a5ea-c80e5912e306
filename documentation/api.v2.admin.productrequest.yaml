openapi: 3.0.3
info:
  title: Oskelly Admin API v2, product request
  description: Oskelly Admin API v2, product request
  version: "1.0"
paths:
  /api/v2/admin/productRequest/list:
    post:
      tags:
        - admin-panel-product-request-controller-api-v-2
      summary: getProductRequestTotal
      operationId: getProductRequestListUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfProductRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/productRequest/total:
    post:
      tags:
        - admin-panel-product-request-controller-api-v-2
      summary: getProductRequestTotal
      operationId: getProductRequestTotalUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.admin.partial.yaml#/components/schemas/FilterContentDto'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductRequestTotalCount'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
components:
  schemas:
    Api2ResponseOfPageOfProductRequestDTO:
      type: object
      properties:
        data:
          $ref: './partial.yaml#/components/schemas/PageOfProductRequestDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfPageOfProductRequestDTO
    Api2ResponseOfProductRequestTotalCount:
      title: Api2ResponseOfProductRequestTotalCount
      type: object
      properties:
        data:
          type: integer
          format: int64
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string