openapi: 3.0.3
info:
  title: Oskelly Admin API v2, Salesman Management
  description: Oskelly Admin API v2, Salesman assignment and management
  version: "2.0"

paths:
  /api/v2/admin/salesman/{salesmanId}/clients:
    get:
      tags:
        - salesman
      summary: Get clients assigned to a salesman
      operationId: searchClients
      security:
        - BearerAuth: []
      parameters:
        - name: salesmanId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of assigned clients
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserDTO'

  /api/v2/admin/salesman/unassigned:
    get:
      tags:
        - salesman
      summary: Get unassigned clients
      operationId: searchUnassignedClients
      security:
        - BearerAuth: []
      responses:
        '200':
          description: List of unassigned clients
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserDTO'

  /api/v2/admin/salesman:
    get:
      tags:
        - salesman
      summary: Get list of salesmen by store name
      operationId: getListOfSalesByShop
      security:
        - BearerAuth: []
      responses:
        '200':
          description: List of salesmen
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserDTO'

  /api/v2/admin/salesman/assign:
    post:
      tags:
        - salesman
      summary: Assign purchasers to a salesman
      operationId: assignPurchaser
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignPurchaserRequest'
      responses:
        '200':
          description: Purchasers successfully assigned

  /api/v2/admin/salesman/redistribute:
    post:
      tags:
        - salesman
      summary: Redistribute purchasers among salesmen
      operationId: redistributePurchasers
      security:
        - BearerAuth: []
      parameters:
        - name: saleId
          in: query
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Purchasers successfully redistributed

  /api/v2/admin/salesman/upload:
    post:
      tags:
        - salesman
      summary: Upload purchasers from CSV file
      operationId: uploadPurchasersFromCSV
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: File successfully uploaded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadResponse'
                
  /api/v2/admin/salesman/unique-roles:
    get:
      tags:
        - salesman
      summary: get unique unassigned roles for sales app
      operationId: getUnassignedUniqueAuthorities
      security:
        - BearerAuth: [ ]
      responses:
        "200":
          description: List of unassigned roles
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AuthorityDTO"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AdminPanelUserDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        info:
          $ref: '#/components/schemas/AdminPanelUserInfoDTO'
        extendedInfo:
          $ref: '#/components/schemas/AdminPanelUserExtendedInfoDTO'
        metaInfo:
          $ref: '#/components/schemas/MetaInfo'

    AdminPanelUserInfoDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        avatarPath:
          type: string
        nickname:
          type: string
        email:
          type: string
          format: email
        sellerType:
          type: string
        registrationDate:
          type: string
          format: date-time
        hasActiveBans:
          type: boolean
        tradeStat:
          type: object
        badges:
          type: array
          items:
            type: object
        userRole:
          type: string
        authorities:
          type: array
          items:
            type: object
        authorityType:
          type: string
        deleteTime:
          type: string
          format: date-time

    AdminPanelUserExtendedInfoDTO:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        name:
          type: string
        phone:
          type: string
        isPhoneConfirmed:
          type: boolean
        sex:
          type: string
        isLoyaltyProgramAccepted:
          type: boolean
        dateOfBirth:
          type: string
          format: date
        likedBrands:
          type: array
          items:
            type: object
        canCreateStream:
          type: boolean
        canPublishMultiSizes:
          type: boolean
        canPublishToDisabledCategories:
          type: boolean
        canAcceptsReturns:
          type: boolean
        canAcceptsBargains:
          type: boolean
        fashionBuyerManager:
          type: boolean
        userType:
          type: string
        socialAccounts:
          type: object
          additionalProperties:
            type: object
        tagGroups:
          type: object
          additionalProperties:
            type: object
        conciergeBuyer:
          type: boolean
        segments:
          type: array
          items:
            type: object
        userCurrentOCommunityStatusExtended:
          type: object
        crossBorder:
          type: boolean
        commissionGrid:
          type: object
        autoPickupDisabled:
          type: boolean
        deviceCountry:
          type: object
        pickupCountry:
          type: object
        syncAgree:
          type: boolean
        syncSuccess:
          type: boolean
        personalManagers:
          type: array
          items:
            type: object
        saleShipmentRoute:
          type: object

    MetaInfo:
      type: object
      properties:
        clientsCount:
          type: integer
        vipCount:
          type: integer
        isVIP:
          type: boolean

    AssignPurchaserRequest:
      type: object
      properties:
        purchaserIds:
          type: array
          items:
            type: integer
            format: int64
        salesmanId:
          type: integer
          format: int64

    UploadResponse:
      type: object
      properties:
        successCount:
          type: integer
        errorIdsCount:
          type: array
          items:
            type: integer
            format: int64
            
    AuthorityDTO:
      type: object
      properties:
        authorityName:
          type: string
          enum:
            - ADMIN
            - PRODUCT_MODERATION
            - USER_MODERATION
            - USERFILE_MODERATION
            - AUTHORITY_MODERATION
            - CONTENT_CREATE
            - CONTENT_DELETE
            - ORDER_MODERATION
            - CAN_VIEW_ALL_PRODUCTS
            - OFFER_MODERATION
            - RETOUCHING_MODERATION
            - COMMENT_MODERATION
            - PROMOCODES_MODERATION
            - PROMOCODES_ADMINISTRATION
            - CURRENCY_RATE_MODERATION
            - CONTENT_MODERATION
            - MASTER_USER
            - STORY_MODERATION
            - ORDER_RETURNS
            - ORDER_PAYOUTS
            - ORDER_CONCIERGE_PAYOUTS
            - STREAM_MODERATION
            - EXPERTISE
            - ORDER_PREPAYMENTS
            - ORDER_RETURN_COMPLETED
            - ORDER_RESOLVE_DISPUTE
            - ORDER_EXPERTISE_FINISH_ANY_DEFECTS_NEGOTIATION
            - BOUTIQUE_SALES
            - ORDER_MARKING_CODES
            - USER_BALANCE_CHANGE
            - PAYOUT_BY_CASH
            - CUSTOM_COMMISSION
            - LOGISTIC
            - SALE_REQUEST_MODERATION
            - PRODUCTS_BULK_EDIT
            - LOGISTIC_SEND_DELIVERY_COMPANY_MAIL_REQUEST
            - ORDER_REFUND_AMOUNT
            - ORDER_MANUAL_TRANSFER
            - SPLIT_ORDER
            - MARK_ORDER_AS_PREPARATION_FOR_PUBLICATION_REQUIRED
            - ORDER_VIEW_ALL_ORDER_SOURCES
            - ORDER_BOUTIQUE_0ST_ACTION
            - ORDER_BOUTIQUE_1ST_ACTION
            - ORDER_BOUTIQUE_2ND_ACTION
            - ORDER_VIEW_LIST_BY_LOCATION
            - USER_DELETE
            - ORDER_MANUAL_CHANGE_DELIVERY_STATE
            - ORDER_MANUAL_CANCEL
            - FAST_AUTO_SHIPMENT_TO_BOUTIQUE
            - ORDER_BOUTIQUE_PACKING_STEP
            - TEST_AUTHORITY_00
            - ORDER_SELLER_CONCIERGE_MOVE_TO_BOUTIQUE
            - USER_CHANGE_MAIL
            - COMMISSION_MODERATION
            - GAZPROM_BONUS
            - OSOCIAL_ADMIN
            - OSOCIAL_MODERATOR
            - BONUS_MODERATION
            - BONUS_INTEGRATION
            - EXPORT2BITRIX_CROSS_BORDER
            - ADMIN_SHOW_PERSONAL_DATA
            - ADMIN_EDIT_PERSONAL_DATA
            - CONCIERGE_SALES_ADMIN
            - SALES
            - CONCIERGE_SOURCERS_ADMIN
            - SOURCER
            - MERCAUX_ADMIN
            - STOLESHNIKOV_ADMIN
            - STOLESHNIKOV_BOUTIQUE_SALESMAN
            - KUZNETSKY_BRIDGE_ADMIN
            - KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN
            - ACCESS_TO_MERKO
            - SHOW_EMAIL
            - CART_CLEAN_ADMIN
        displayName:
          type: string
        turnOn:
          type: boolean
