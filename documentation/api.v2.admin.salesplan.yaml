openapi: 3.0.3
info:
  title: Oskelly Admin API v2, Sales Plan
  description: Oskelly Admin API v2, Sales Plan management
  version: "1.0"
paths:
  /api/v2/admin/sales-plan/{storeName}:
    post:
      tags:
        - salesPlan
      summary: Create a sales plan for a store
      operationId: createSalesPlanForStore
      parameters:
        - in: path
          name: storeName
          schema:
            type: string
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SalesPlanCreateDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesPlanDto'

  /api/v2/admin/sales-plan/salesmen:
    get:
      tags:
        - salesPlan
      summary: Get all salesmen by store name
      operationId: getAllSalesmenByStoreCode
      parameters:
        - in: query
          name: storeName
          schema:
            type: string
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SalesmanInfoDto'

  /api/v2/admin/sales-plan/all:
    get:
      tags:
        - salesPlan
      summary: Get all sales plans
      operationId: getAllPlans
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StoreSalesPlanDto'

  /api/v2/admin/sales-plan/{id}:
    patch:
      tags:
        - salesPlan
      summary: Update a sales plan
      operationId: updateSalesPlan
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SalesPlanCreateDto'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesPlanDto'
        '404':
          description: Not Found
        '400':
          description: Invalid request body

    delete:
      tags:
        - salesPlan
      summary: Delete a sales plan
      operationId: deleteSalesPlan
      parameters:
        - in: path
          name: id
          schema:
            type: integer
          required: true
      responses:
        '204':
          description: No Content
        '404':
          description: Not Found


components:
  schemas:
    SalesPlanDto:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        amount:
          type: integer
        percentageCompleted:
          type: string
        actualSales:
          type: string
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        store:
          $ref: '#/components/schemas/StoreInfoDto'
        salesmanPlans:
          type: array
          items:
            $ref: '#/components/schemas/SalesmanInfoDto'

    StoreSalesPlanDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        salesPlans:
          type: array
          items:
            $ref: '#/components/schemas/SalesmanInfoDto'

    SalesPlanByStoreDto:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        amount:
          type: integer
        percentageCompleted:
          type: string
        actualSales:
          type: string
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        salesmanPlans:
          type: array
          items:
            $ref: '#/components/schemas/SalesmanInfoDto'

    SalesmanInfoDto:
      type: object
      properties:
        id:
          type: integer
        userPhoto:
          type: string
        username:
          type: string
        role:
          type: string
        userSalesPlan:
          type: number
        userActualSales:
          type: number
        userPerformancePercentage:
          type: number

    SalesPlanCreateDto:
      type: object
      properties:
        title:
          type: string
        startDate:
          type: string
          format: date-time
        endDate:
          type: string
          format: date-time
        amount:
          type: integer
        salesmenPlans:
          type: array
          items:
            $ref: '#/components/schemas/SalesmanPlanCreateDto'

    SalesmanPlanCreateDto:
      type: object
      properties:
        id:
          type: integer
        amount:
          type: number

    StoreInfoDto:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
