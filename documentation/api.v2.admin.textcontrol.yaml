openapi: 3.0.3
info:
  title: Oskelly Admin API v2, text control
  description: Oskelly Admin API v2, text control
  version: "1.0"
paths:
  /api/v2/admin/textcontrol/update:
    post:
      tags:
        - adminPanel
        - textControl
        - update
      summary: Update text control
      operationId: updateTextControl
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TextControlDto'
      responses:
        '200':
          description: updated Text control
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfTextControl'

  /api/v2/admin/textcontrol:
    get:
      tags:
        - adminPanel
        - productText
        - byIds
      summary: Get text control list
      operationId: getTextControlList
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
      responses:
        '200':
          description: List of TextControl
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfTextControlList'


components:
  schemas:
    Api2ResponseOfTextControl:
      title: Api2ResponseOfTextControl
      type: object
      properties:
        data:
          $ref: '#/components/schemas/TextControlDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfTextControlList:
      title: Api2ResponseOfTextControlList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/TextControlDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    TextControlDto:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        text:
          type: string
        imageUrl:
          type: string
          description: path to image
        imageBase64:
          type: string
          description: image in Base64 format
        imageWidth:
          type: integer
        imageHeight:
          type: integer
        userId:
          type: integer
          format: int64
        userSubtitle:
          type: string
          description: user subtitle