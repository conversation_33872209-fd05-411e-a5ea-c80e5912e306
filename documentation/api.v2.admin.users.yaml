openapi: 3.0.3

info:
  title: Oskelly Admin API v2, promocodes
  description: Oskelly Admin API v2, promocodes
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

paths:
  /api/v2/admin/users:
    get:
      tags:
        - User
      summary: Get list of users
      operationId: getUsers
      parameters:
        - $ref: '#/components/parameters/UsersSearchQueryParam'
        - $ref: '#/components/parameters/UsersPageParam'
        - $ref: '#/components/parameters/UsersPageSizeParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersApi2ResponseOfPageOfUserDTO'

  /api/v2/admin/users/list:
    get:
      tags:
        - User
        - list
      summary: Get userr rlist by ids
      operationId: getUserListByIds
      parameters:
        - $ref: './api.v2.admin.partial.yaml#/components/parameters/IdList'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfUserList'
components:
  parameters:
    UsersPageParam:
      name: page
      in: query
      description: Page number
      required: false
      schema:
        type: integer
        format: int32
        default: 1
    UsersPageSizeParam:
      name: rowsPerPage
      in: query
      description: Page size
      required: false
      schema:
        type: integer
        format: int32
        default: 15
    UsersSearchQueryParam:
      name: searchQuery
      in: query
      description: search query
      required: true
      schema:
        type: string
  schemas:
    UsersApi2ResponseOfPageOfUserDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/UsersPageOfUserDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    UsersPageOfUserDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/UserDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    Api2ResponseOfUserList:
      title: Api2ResponseOfUserList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/UserDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string