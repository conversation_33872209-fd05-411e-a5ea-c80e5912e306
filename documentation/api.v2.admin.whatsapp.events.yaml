openapi: 3.0.1
info:
  title: WhatsApp WebSocket API
  description: API for handling WebSocket connections and events
  version: 1.0.0
paths:
  /ws:
    get:
      summary: WebSocket connection endpoint
      tags:
        - WebSocket
      description: Establish a WebSocket connection to receive real-time updates
      responses:
        "101":
          description: Switching protocols to WebSocket
components:
  schemas:
    EventDto:
      type: object
      properties:
        eventType:
          type: string
    MessageAcknowledgeEventDto:
      allOf:
        - $ref: '#/components/schemas/EventDto'
        - type: object
          properties:
            messageId:
              type: string
    MessageEventDto:
      allOf:
        - $ref: '#/components/schemas/EventDto'
        - type: object
          properties:
            from:
              type: string
            to:
              type: string
            body:
              type: string
            chatWith:
              type: string
            timestamp:
              type: integer
            hasQuotedMsg:
              type: boolean
            isForwarded:
              type: boolean
            messageId:
              type: string
            viewed:
              type: boolean
            hasMedia:
              type: boolean
            media:
              $ref: '#/components/schemas/MediaDto'
            quotedMessage:
              $ref: '#/components/schemas/QuotedMessageDto'
    MessageReactionEventDto:
      allOf:
        - $ref: '#/components/schemas/EventDto'
        - type: object
          properties:
            senderId:
              type: string
            reaction:
              type: string
            read:
              type: boolean
            timestamp:
              type: integer
            msgId:
              type: string
            remote:
              type: string
    MediaDto:
      type: object
      properties:
        mediaKey:
          type: string
        mimetype:
          type: string
        caption:
          type: string
        mms3Url:
          type: string
        mediaKeyTimestamp:
          type: integer
        waveform:
          type: array
          items:
            type: integer
        duration:
          type: string
        size:
          type: integer
    QuotedMessageDto:
      type: object
      properties:
        body:
          type: string
        quotedStanzaID:
          type: string
        media:
          $ref: '#/components/schemas/MediaDto'
