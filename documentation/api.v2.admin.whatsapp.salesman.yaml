openapi: 3.0.1
info:
  title: WhatsApp API
  description: API for working with WhatsApp chats
  version: 1.0.0
paths:
  /api/whatsapp/salesmen:
    get:
      summary: Get a list of available salesmen
      tags:
        - Salesmen
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SalesmanChatsDto'
  /api/whatsapp/salesmen/{userId}/chats/{chatId}:
    get:
      summary: Get chat messages
      tags:
        - Chats
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
        - name: chatId
          in: path
          required: true
          schema:
            type: string
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 15
      responses:
        "200":
          description: List of messages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MessageDto'
  /api/whatsapp/salesmen/{userId}/chats/{chatId}/{messageId}:
    get:
      summary: Get messages before a specified message
      tags:
        - Chats
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
        - name: chatId
          in: path
          required: true
          schema:
            type: string
        - name: messageId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: List of messages
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MessageDto'
components:
  schemas:
    SalesmanChatsDto:
      type: object
      properties:
        chats:
          type: array
          items:
            $ref: '#/components/schemas/ChatDto'
        unreadCount:
          type: integer
        userId:
          type: integer
        chatAmount:
          type: integer
        instanceId:
          type: integer
        userPhoto:
          type: string
        username:
          type: string
        roleName:
          type: string
        roleDescription:
          type: string
        phone:
          type: string
    ChatDto:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        chatId:
          type: string
        unreadCount:
          type: integer
        lastMessage:
          $ref: '#/components/schemas/LastMessageDto'
        userPhoto:
          type: string
        username:
          type: string
    LastMessageDto:
      type: object
      properties:
        body:
          type: string
        timestamp:
          type: integer
        hasMedia:
          type: boolean
        hasReaction:
          type: boolean
        hasQuotedMsg:
          type: boolean
        media:
          $ref: '#/components/schemas/MediaDto'
        link:
          type: object
    MessageDto:
      type: object
      properties:
        from:
          type: string
        to:
          type: string
        author:
          type: string
        body:
          type: string
        messageId:
          type: string
        timestamp:
          type: integer
        hasMedia:
          type: boolean
        viewed:
          type: boolean
        isNewMsg:
          type: boolean
        hasReaction:
          type: boolean
        isVideoCall:
          type: boolean
        hasQuotedMsg:
          type: boolean
        mimetype:
          type: string
        stickerSentTs:
          type: integer
        media:
          $ref: '#/components/schemas/MediaDto'
        quotedMessage:
          $ref: '#/components/schemas/QuotedMessageDto'
        reactions:
          type: array
    MediaDto:
      type: object
      properties:
        mediaKey:
          type: string
        mimetype:
          type: string
        caption:
          type: string
        mms3Url:
          type: string
        mediaKeyTimestamp:
          type: integer
        waveform:
          type: array
          items:
            type: integer
        duration:
          type: string
        size:
          type: integer
        body:
          type: string
    QuotedMessageDto:
      type: object
      properties:
        body:
          type: string
        quotedStanzaID:
          type: string
        media:
          $ref: '#/components/schemas/MediaDto'
