openapi: 3.0.3
info:
  title: Oskelly Admin API v2, Work Schedule
  description: Oskelly Admin API v2, Work Schedule management
  version: "1.0"
paths:
  /api/v2/admin/work-schedule/settings/salesman/{salesmanId}/update:
    put:
      tags:
        - workScheduleSettings
      summary: Update work schedule settings
      operationId: updateSettings
      parameters:
        - in: path
          name: salesmanId
          schema:
            type: integer
            example: 3433
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkScheduleSettingRequestDto'
      responses:
        '200':
          description: Successful operation

  /api/v2/admin/work-schedule/settings/salesman/{salesmanId}:
    get:
      tags:
        - workScheduleSettings
      summary: Get work schedule settings by salesman
      operationId: getSettings
      parameters:
        - in: path
          name: salesmanId
          schema:
            type: integer
            example: 3433
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkScheduleSettingResponseDto'

  /api/v2/admin/work-schedule/search:
    get:
      tags:
        - workSchedule
      summary: Find work schedules for filters
      operationId: searchWorkSchedules
      parameters:
        - in: query
          name: salesmanName
          schema:
            type: string
            example: Иванов
        - in: query
          name: storeIds
          schema:
            type: array
            items:
              type: integer
            example: [1, 5, 6]
        - in: query
          name: fromDate
          required: true
          schema:
            type: string
            format: date
            example: 2025-04-01
        - in: query
          name: toDate
          required: true
          schema:
            type: string
            format: date
            example: 2025-05-01
        - in: query
          name: page
          required: true
          schema:
            type: integer
            example: 0
        - in: query
          name: limit
          required: true
          schema:
            type: integer
            example: 8
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkScheduleResponseDto'

  /api/v2/admin/work-schedule/day/{workScheduleDayId}/update:
    put:
      tags:
        - workSchedule
      summary: Update work schedule day
      operationId: updateWorkScheduleDay
      parameters:
        - in: path
          name: workScheduleDayId
          schema:
            type: string
            format: uuid
            example: "550e8400-e29b-41d4-a716-************"
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkScheduleDayUpdateDto'
      responses:
        '200':
          description: Successful operation

  /api/v2/admin/work-schedule/store/list/filter:
    get:
      tags:
        - workSchedule
      summary: Get stores list for filter
      operationId: getStoreSalesmanCountList
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/WorkScheduleStoreSalesmanCountDto'

  /api/v2/admin/work-schedule/store/list:
    get:
      tags:
        - workSchedule
      summary: Get stores list for settings
      operationId: getAllowedStoreList
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StoreInfoDto'

components:
  schemas:
    WorkScheduleSettingRequestDto:
      type: object
      properties:
        storeId:
          type: integer
          example: 5
        workingDays:
          type: array
          items:
            type: string
            enum:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
          example: [ "TUESDAY", "MONDAY", "FRIDAY", "SATURDAY" ]
        startWorkingDayTime:
          type: string
          format: time
          example: "09:00"
        endWorkingDayTime:
          type: string
          format: time
          example: "18:00"
        stateHolidays:
          type: boolean
          example: false
        extraDays:
          type: array
          items:
            $ref: "#/components/schemas/ExtraDayDto"
        breaks:
          type: array
          items:
            $ref: "#/components/schemas/BreakDto"

    WorkScheduleSettingResponseDto:
      type: object
      properties:
        storeId:
          type: integer
          example: 5
        workingDays:
          type: array
          items:
            type: string
            enum:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
          example: ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]
        nonWorkingDays:
          type: array
          items:
            type: string
            enum:
              - SATURDAY
              - SUNDAY
          example: ["SATURDAY", "SUNDAY"]
        startWorkingDayTime:
          type: string
          format: time
          example: "09:00"
        endWorkingDayTime:
          type: string
          format: time
          example: "18:00"
        stateHolidays:
          type: boolean
          example: false
        extraWorkingDays:
          type: array
          items:
            $ref: "#/components/schemas/ExtraDayDto"
        extraNonWorkingDays:
          type: array
          items:
            $ref: "#/components/schemas/ExtraDayDto"
        extraWorkingDaysHistory:
          type: array
          items:
            $ref: "#/components/schemas/ExtraDayHistoryDto"
        extraNonWorkingDaysHistory:
          type: array
          items:
            $ref: "#/components/schemas/ExtraDayHistoryDto"
        breaks:
          type: array
          items:
            $ref: "#/components/schemas/BreakDto"
        workingDaysCalendar:
          type: array
          items:
            type: string
            format: date
          description: List of working days for the past, current and future months
          example:
            - "2025-04-01"
            - "2025-04-02"
            - "2025-04-03"
            - "2025-04-04"

    ExtraDayDto:
      type: object
      properties:
        id:
          type: integer
          example: 1234
          nullable: true
        dayType:
          required: true
          type: string
          example: OVERTIME
          enum:
            - WORKING_DAY
            - OVERTIME
            - HOLIDAY
            - VACATION
        startDate:
          required: true
          type: string
          format: date
          example: 2025-04-01
        endDate:
          required: true
          type: string
          format: date
          example: 2025-04-01
        description:
          type: string
          example: Отработка выходного

    ExtraDayHistoryDto:
      type: object
      properties:
        id:
          type: integer
          example: 1234
        dayType:
          required: true
          type: string
          example: OVERTIME
          enum:
            - WORKING_DAY
            - OVERTIME
            - HOLIDAY
            - VACATION
        startDate:
          required: true
          type: string
          format: date
          example: 2025-04-01
        endDate:
          required: true
          type: string
          format: date
          example: 2025-04-01
        description:
          type: string
          example: Отработка выходного

    BreakDto:
      type: object
      properties:
        startBreakTime:
          required: true
          type: string
          format: time
          example: 12:00
        endBreakTime:
          required: true
          type: string
          format: time
          example: 13:00

    WorkScheduleDayDto:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        salesmanId:
          type: integer
          example: 12345
        storeId:
          type: integer
          example: 67890
        dayDate:
          type: string
          format: date
          example: "2025-04-01"
        dayType:
          type: string
          example: OVERTIME
          enum:
            - WORKING_DAY
            - OVERTIME
            - HOLIDAY
            - VACATION
        additionalInfo:
          type: string
          example: "Отработка выходного"
        workingHours:
          type: string
          example: "09:00-18:00"

    SalesmanWorkScheduleDto:
      type: object
      properties:
        salesman:
          $ref: "#/components/schemas/SalesmanShortInfoDto"
        workScheduleDays:
          type: array
          items:
            $ref: "#/components/schemas/WorkScheduleDayDto"

    SalesmanShortInfoDto:
      type: object
      properties:
        salesmanId:
          type: integer
          example: 12345
          description: Идентификатор сотрудника в сервисе админ-панели
        userId:
          type: integer
          example: 555022
          description: Идентификатор пользователя
        name:
          type: string
          example: Иван Иванов
        role:
          type: string
          example: Администратор Столешников
        avatarPath:
          type: string
          description: url аватара пользователя

    WorkScheduleResponseDto:
      type: object
      properties:
        workSchedules:
          type: array
          items:
            $ref: "#/components/schemas/SalesmanWorkScheduleDto"
        page:
          type: integer
          example: 0
        limit:
          type: integer
          example: 8
        total:
          type: integer
          example: 100

    WorkScheduleDayUpdateDto:
      type: object
      properties:
        dayType:
          required: true
          type: string
          example: WORKING_DAY
          enum:
            - WORKING_DAY
            - OVERTIME
            - HOLIDAY
            - VACATION

    WorkScheduleStoreSalesmanCountDto:
      type: object
      properties:
        storeId:
          type: integer
          example: 9
        name:
          type: string
          example: Кузнецкий Мост
        salesmanCount:
          type: integer
          example: 20

    StoreInfoDto:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Кузнецкий мост