openapi: 3.0.3

info:
  title: Oskelly API v2, authenticity
  description: Oskelly API v2, authenticity
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

tags:
  - name: authenticity-controller-api-v-2
    description: Authenticity Controller Api V 2

paths:
  /api/v2/authenticity:
    get:
      tags:
        - authenticity-controller-api-v-2
      summary: fetching DTO with authenticity opinion bu UUID
      operationId: getAuthenticityOpinion
      parameters:
        - in: query
          name: uuid
          description: a UUID to find matching authenticity opinion
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductAuthenticityOpinionDTO'
        '404':
          description: Opinion matching UUID is not found

components:
  schemas:
    Api2ResponseOfProductAuthenticityOpinionDTO:
      title: Api2ResponseOfProductAuthenticityOpinionDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductAuthenticityOpinionDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ProductAuthenticityOpinionDTO:
      title: ProductAuthenticityOpinionDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        uuid:
          type: string
          format: uuid
        product:
          $ref: './partial.yaml#/components/schemas/ProductDTO'
        customerFullName:
          type: string
        opinionText:
          type: string
        date:
          type: string
          format: date-time
