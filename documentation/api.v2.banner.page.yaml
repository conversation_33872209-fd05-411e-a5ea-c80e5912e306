openapi: 3.0.3
info:
  title: Oskelly API v2, banner page
  description: Oskelly API v2, banner page
  version: "1.0"
paths:
  /api/v2/banner/page:
    get:
      tags:
        - bannerPage
      summary: Get banner page content
      operationId: getBannerPageContent
      parameters:
        - $ref: '#/components/parameters/BannerId'
        - $ref: '#/components/parameters/PageNumber'
      responses:
        '200':
          description: banner content
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2Response'
components:
  parameters:
    BannerId:
      name: bannerId
      in: query
      description: banner id
      required: true
      schema:
        type: string
    PageNumber:
      name: pageNumber
      in: query
      description: page number
      required: false
      schema:
        type: integer
        default: 0
  schemas:
    Api2Response:
      title: Api2Response
      type: object
      properties:
        data:
          type: object
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string