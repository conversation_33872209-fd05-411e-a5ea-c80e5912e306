openapi: 3.0.0
info:
  title: Oskelly Bonuses API
  description: API для работы с Бонусной системой.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: localhost
tags:
  - name: bonuses-offline-controller-api
    description: Bonuses Controller API
paths:
  /api/v2/bonuses-offline/balance-brief:
    get:
      summary: Инфа о количестве бонусов пользователя
      operationId: getBalanceBrief
      parameters:
        - name: barcode
          in: query
          description: Баркод
          required: true
          schema:
            type: string
      tags:
        - bonuses-offline-controller-api
      responses:
        '200':
          description: Текущей баланс + инфа о сгорающих бонусах
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfBonusesBalanceDTO'

  /api/v2/bonuses-offline/bonuses-info:
    post:
      summary: Инфа о потенциально начисленных/списанных бонусах за товар
      operationId: getBonusesInfo
      tags:
        - bonuses-offline-controller-api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BonusesOfflineInfoRequest'
      responses:
        '200':
          description: Инфа о потенциально начисленных/списанных бонусах за товар
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfBonusesOfflineInfoItemResponse'

  /api/v2/bonuses-offline/send-verification-sms-code:
    post:
      summary: Отправка смс-кода для подтверждения списания бонусов
      operationId: sendSms
      tags:
        - bonuses-offline-controller-api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BonusesOfflineSendVerificationSmsRequest'
      responses:
        '200':
          description: Пустой ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'

  /api/v2/bonuses-offline/withdraw:
    post:
      summary: Списание бонусов
      operationId: withdraw
      tags:
        - bonuses-offline-controller-api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BonusesOfflineWithdrawRequest'
      responses:
        '200':
          description: Инфа о результате списания бонусов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBonusesOfflineWithdrawResponse'

  /api/v2/bonuses-offline/cancel:
    post:
      summary: Отмена списания бонусов
      operationId: cancel
      tags:
        - bonuses-offline-controller-api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BonusesOfflineCancelRequest'
      responses:
        '200':
          description: Отмена транзакции по списанию бонусов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'

components:
  schemas:
    BonusesOfflineInfoRequest:
      type: object
      properties:
        barcode:
          type: string
          nullable: false
          description: Баркод
        items:
          nullable: false
          type: array
          items:
            $ref: '#/components/schemas/BonusesOfflineProductItemRequest'

    Api2ResponseOfListOfBonusesOfflineInfoItemResponse:
      title: Api2ResponseOfBonusesBonusCardInfoDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BonusesOfflineInfoItemResponse'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    BonusesOfflineInfoItemResponse:
      description: Ответ по бонусам по одному товару
      type: object
      properties:
        productItemId:
          nullable: false
          type: integer
          format: int64
          description: Идентификатор позиции
          example: 111
        bonusesInfo:
          $ref: './partial.yaml#/components/schemas/BonusesInfoDTO'

    BonusesOfflineProductItemRequest:
      description: Данные для запроса инфы по одному товару
      type: object
      properties:
        productItemId:
          nullable: false
          type: integer
          format: int64
          description: Идентификатор товара
          example: 111
        price:
          nullable: false
          type: number
          format: bigdecimal
          description: Стоимость товара
          example: 222

    BonusesOfflineSendVerificationSmsRequest:
      type: object
      properties:
        barcode:
          type: string
          nullable: false
          description: Баркод

    BonusesOfflineWithdrawRequest:
      type: object
      properties:
        barcode:
          type: string
          nullable: false
          description: Баркод
        smsCode:
          type: string
          nullable: false
          description: Баркод
        items:
          type: array
          items:
            type: integer
            format: int64
        totalBonusesAmount:
          nullable: false
          type: number
          format: bigdecimal
          description: Общее количество сгораемых и НЕсгораемых бонусов для списания
          example: 222

    Api2ResponseOfBonusesOfflineWithdrawResponse:
      title: Api2ResponseOfBonusesBonusCardInfoDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/BonusesOfflineWithdrawResponse'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    BonusesOfflineWithdrawResponse:
      description: Ответ по списанию бонусов
      type: object
      properties:
        offlineBonusesTransactionId:
          nullable: false
          type: integer
          format: int64
          description: Идентификатор транзакции списания бонусов
          example: 111
        items:
          type: array
          items:
            $ref: '#/components/schemas/BonusesOfflineWithdrawProductItem'

    BonusesOfflineWithdrawProductItem:
      description: Ответ по одному товару по результатам списания бонусов
      type: object
      properties:
        productItemId:
          nullable: false
          type: integer
          format: int64
          description: Идентификатор товара
          example: 111
        bonuses:
          nullable: false
          type: number
          format: bigdecimal
          description: Количество списанных сгораемых бонусов
          example: 222
        money:
          nullable: false
          type: number
          format: bigdecimal
          description: Количество списанных НЕсгораемых бонусов
          example: 222

    BonusesOfflineCancelRequest:
      type: object
      properties:
        barcode:
          type: string
          nullable: false
          description: Баркод
        offlineBonusesTransactionId:
          nullable: false
          type: integer
          format: int64
          description: Идентификатор транзакции списания бонусов
          example: 111

    Api2ResponseOfString:
      type: object
      properties:
        data:
          type: string
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfString