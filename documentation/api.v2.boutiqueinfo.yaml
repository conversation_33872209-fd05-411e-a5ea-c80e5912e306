openapi: 3.0.0
info:
  title: Oskelly BoutiqueInfo API
  description: API для получения информации по бутикам.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: localhost
tags:
  - name: boutiqueinfo-controller-api
    description: BoutiqueInfo Controller API
paths:
  /api/v2/boutique/boutiques-info:
    get:
      summary: Получение информации об оффлайн-бутиках
      operationId: getBoutiques
      tags:
        - boutiqueinfo-controller-api
      responses:
        '200':
          description: Информации об оффлайн-бутиках
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfBoutiqueInfoDTO'

components:
  schemas:
    Api2ResponseOfListOfBoutiqueInfoDTO:
      title: Api2ResponseOfListOfBoutiqueInfoDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BoutiqueInfoDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    BoutiqueInfoDTO:
      description: Информация по одному оффлайн-бутику
      type: object
      properties:
        id:
          description: Идентификатор записи об оффлайн-бутике
          nullable: false
          type: integer
          format: int64
        title:
          nullable: false
          type: string
          description: Название бутика
        address:
          nullable: false
          type: string
          description: Адрес
        longitude:
          nullable: false
          type: string
          description: Долгота
        latitude:
          nullable: false
          type: string
          description: Широта
        description:
          nullable: false
          type: string
          description: Описание
        phone:
          nullable: false
          type: string
          description: Телефон
        whatsappLink:
          nullable: false
          type: string
          description: Ссылка на контакт WhatsApp
        productItemLocationId:
          nullable: false
          type: integer
          format: int64
          description: Идентификатор productItemLocationId
        tags:
          type: array
          items:
            type: string
            description: тэг
        images:
          type: array
          items:
            type: string
            description: ссылка на изображение
        workingHours:
          description: Рабочие часы
          type: array
          items:
            $ref: '#/components/schemas/BoutiqueInfoWorkingHoursDTO'

    BoutiqueInfoWorkingHoursDTO:
      description: Информация по элементу режима работы
      type: object
      properties:
        label:
          type: string
          description: Текст метки
        workingHours:
          type: string
          description: Часы работы