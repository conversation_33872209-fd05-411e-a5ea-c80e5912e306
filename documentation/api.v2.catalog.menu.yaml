openapi: 3.0.3
x-stoplight:
  id: onpgkawco9rng
info:
  title: catalog-menu
  version: '1.0'
servers:
  - url: 'http://localhost:3000'
paths:
  /api/v2/catalog/menuContent:
    get:
      summary: Get Catalog Menu Content
      tags: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfCatalogMenu'
              examples: {}
      operationId: get-api-v2-catalog-menuContent
      parameters:
        - schema:
            type: string
          in: query
          name: version
        - schema:
            type: boolean
          in: query
          name: isExperimentalVersion
        - schema:
            type: boolean
          in: query
          name: productRequestEnabled
        - schema:
            type: boolean
          in: query
          name: newResaleSplitEnabled
components:
  schemas:
    BaseBannerLink:
      title: BaseBannerLink
      x-stoplight:
        id: 0dba7ae4806cb1
      type: object
      properties:
        type:
          type: string
          enum:
            - brands
            - category
            - deeplink
            - new
            - choice
            - stock
            - pro
            - sale
      required:
        - type
    BaseCatalogElement:
      title: BaseCatalogElement
      x-stoplight:
        id: e3318f6b020d3
      type: object
      properties:
        type:
          type: string
          enum:
            - banner
            - banner_grid
            - banner_mini
            - list
            - stream
      required:
        - type
    BanneredLinkData:
      title: BanneredLinkData
      x-stoplight:
        id: yg2mxjalre7eb
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        image:
          type: string
        imageWebp:
          type: string
    BanneredLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            categoryId:
              type: integer
            bannerData:
              $ref: '#/components/schemas/BanneredLinkData'
    BrandsLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            categoryId:
              type: integer
    CategoryLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            categoryId:
              type: integer
            parentCategoryId:
              type: integer
    ChoiceLink:
      $ref: '#/components/schemas/BanneredLink'
    StockLink:
      $ref: '#/components/schemas/BanneredLink'
    DeepLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            deeplink:
              type: string
    NewLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            categoryId:
              type: integer
    ProLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            categoryId:
              type: integer
    SaleLink:
      allOf:
        - $ref: '#/components/schemas/BaseBannerLink'
        - type: object
          properties:
            categoryId:
              type: integer
    BannerData:
      title: BannerData
      x-stoplight:
        id: hgl8yuprdzcie
      type: object
      properties:
        title:
          type: string
        badge:
          type: string
        description:
          type: string
        image:
          type: string
        imageWebp:
          type: string
        link:
          $ref: '#/components/schemas/BannerLink'
    PlainTextData:
      title: PlainTextData
      x-stoplight:
        id: hgl8yuprdzdscie
      type: object
      properties:
        title:
          type: string
        link:
          $ref: '#/components/schemas/BannerLink'
    BlockElement:
      allOf:
        - $ref: '#/components/schemas/BaseCatalogElement'
        - type: object
          properties:
            blockId:
              type: string
    BannerElement:
      allOf:
        - $ref: '#/components/schemas/BlockElement'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/BannerData'
    PlainTextElement:
      allOf:
        - $ref: '#/components/schemas/BlockElement'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/PlainTextData'
    BannerMiniElement:
      $ref: '#/components/schemas/BannerElement'
    StreamElement:
      $ref: '#/components/schemas/BlockElement'
    BannerGridData:
      title: BannerGridData
      x-stoplight:
        id: vijlo0le93dw9
      type: object
      properties:
        children:
          type: array
          items:
            $ref: '#/components/schemas/CatalogElement'
    BannerGridElement:
      allOf:
        - $ref: '#/components/schemas/BaseCatalogElement'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/BannerGridData'
    ListData:
      title: ListData
      x-stoplight:
        id: 47ygq300jyxfr
      type: object
      properties:
        title:
          type: string
        children:
          type: array
          items:
            $ref: '#/components/schemas/CatalogElement'
    ListElement:
      allOf:
        - $ref: '#/components/schemas/BaseCatalogElement'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ListData'
    ItemData:
      title: ItemData
      x-stoplight:
        id: firhdmvr3xai5
      type: object
      properties:
        children:
          type: array
          items:
            $ref: '#/components/schemas/CatalogElement'
    MenuItem:
      title: MenuItem
      x-stoplight:
        id: h7j467xlqrudj
      type: object
      properties:
        name:
          type: string
        item_id:
          type: string
        data:
          $ref: '#/components/schemas/ItemData'
    CatalogMenu:
      title: CatalogMenu
      x-stoplight:
        id: gyb3j0n4j0j1l1
      type: object
      properties:
        version:
          type: string
        description:
          type: string
        catalog:
          type: array
          items:
            $ref: '#/components/schemas/MenuItem'
    ApiV2ResponseOfCatalogMenu:
      title: ApiV2ResponseOfCatalogMenu
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CatalogMenu'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      x-stoplight:
        id: 7c258e47dd68b1
    CatalogElement:
      oneOf:
        - $ref: '#/components/schemas/BannerElement'
        - $ref: '#/components/schemas/BannerGridElement'
        - $ref: '#/components/schemas/BannerMiniElement'
        - $ref: '#/components/schemas/ListElement'
        - $ref: '#/components/schemas/StreamElement'
        - $ref: '#/components/schemas/PlainTextElement'
      title: ''
    BannerLink:
      oneOf:
        - $ref: '#/components/schemas/BrandsLink'
        - $ref: '#/components/schemas/CategoryLink'
        - $ref: '#/components/schemas/DeepLink'
        - $ref: '#/components/schemas/NewLink'
        - $ref: '#/components/schemas/ChoiceLink'
        - $ref: '#/components/schemas/StockLink'
        - $ref: '#/components/schemas/ProLink'
        - $ref: '#/components/schemas/SaleLink'
