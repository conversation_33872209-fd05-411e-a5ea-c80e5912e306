# TODO REQUIRED ID
openapi: 3.0.3

info:
  title: Oskelly API v2, catalog
  description: Oskelly API v2, catalog
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

tags:
  - name: catalog-controller-api-v-2
    description: Catalog Controller Api V 2

paths:
  /api/v2/catalog/allBrands:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getBrands
      operationId: getBrandsUsingGET
      parameters:
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductsRequest'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/search:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getBrands
      operationId: getBrandsFilteredUsingGET
      parameters:
        - name: categoryId
          in: query
          required: true
          style: form
          schema:
            type: integer
            format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: search
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/attributeTree:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTree
      operationId: getAttributeTreeUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreePost
      operationId: getAttributeTreePostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/attributeTree/color:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreeColor
      operationId: getAttributeTreeColorUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreeColorPost
      operationId: getAttributeTreeColorPostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/attributeTree/color-post:
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreeColorPostV2
      operationId: getAttributeTreeColorPostV2UsingPOST
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/catalog/attributeTree/material:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreeMaterial
      operationId: getAttributeTreeMaterialUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: flatAttrValues
          in: query
          required: false
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreeMaterialPost
      operationId: getAttributeTreeMaterialPostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/attributeTree/material-post:
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getAttributeTreeMaterialPostV2
      operationId: getAttributeTreeMaterialPostV2UsingPOST
      parameters:
        - name: flatAttrValues
          in: query
          required: false
          schema:
            type: boolean
            default: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAttributeTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/catalog/availableFilters:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getAvailableFilters
      operationId: getAvailableFiltersUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingProductModels
          in: query
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAvailableFilters'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getAvailableFiltersPost
      operationId: getAvailableFiltersPostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAvailableFilters'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getBrands
      operationId: getBrandsUsingGET_1
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getBrandsPost
      operationId: getBrandsPostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBrandDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands-post:
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getBrandsPostV2
      operationId: getBrandsPostV2UsingPOST
      parameters:
        - schema:
            type: string
          in: query
          name: currencyCode
          required: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BrandsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBrandDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/catalog/brands/liked:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedBrands
      operationId: getLikedBrandsUsingGET_1
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/liked/{userId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedBrands
      operationId: getLikedBrandsUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/liked/page:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedBrandsBriefPageForCurrentUser
      operationId: getLikedBrandsBriefPageForCurrentUserUsingGET
      parameters:
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/liked/{userId}/page:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedBrandsBriefPageForUser
      operationId: getLikedBrandsBriefPageForUserUsingGET
      parameters:
        - name: userId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/name/{urlName}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getBrand
      operationId: getBrandUsingGET_1
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: urlName
          in: path
          description: urlName
          required: true
          style: simple
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/top/manual:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getTopBrandsManual
      operationId: getTopBrandsManualUsingGET
      parameters:
        - name: categoryId
          in: query
          description: categoryId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/top:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getTopBrands
      operationId: getTopBrandsUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: rootCategoryId
          in: query
          description: rootCategoryId
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: count
          in: query
          description: count
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getTopBrandsPost
      operationId: getTopBrandsPostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: rootCategoryId
          in: query
          description: rootCategoryId
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: count
          in: query
          description: count
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/ourChoiceCategories:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getOurChoiceAvailableCategories
      operationId: getOurChoiceAvailableCategoriesUsingGET
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOflong'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/{brandId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getBrand
      operationId: getBrandUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: brandId
          in: path
          description: brandId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withDescription
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withProducts
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/{brandId}/dislike:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: dislikeBrand
      operationId: dislikeBrandUsingPUT
      parameters:
        - name: brandId
          in: path
          description: brandId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/{brandId}/like:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: likeBrand
      operationId: likeBrandUsingPUT
      parameters:
        - name: brandId
          in: path
          description: brandId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/brands/{brandId}/toggle:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: toggleLikeBrand
      operationId: toggleLikeBrandUsingPUT
      parameters:
        - name: brandId
          in: path
          description: brandId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfToggleResult'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/categories/{categoryId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getDirectChildrenCategories
      operationId: getDirectChildrenCategoriesUsingGET
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfCategoryDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/fullCategoryTree:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getFullCategoryTree
      operationId: getFullCategoryTreeUsingGET
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCategoryTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/categoryTree:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getCategoryTree
      operationId: getCategoryTreeUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: exceptBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCategoryTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getCategoryTreePost
      operationId: getCategoryTreePostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCategoryTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/product-ids-page:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getProductIdsPage
      operationId: getProductIdsPageUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOflong'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getProductIdsPagePost
      operationId: getProductIdsPagePostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOflong'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getCatalog
      operationId: getCatalogUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: exceptBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingProductModels
          in: query
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            oneOf:
              - type: string
              - type: array
                items:
                  type: integer
                  format: int64

        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getCatalogPost
      operationId: getCatalogPostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products-post:
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getCatalogPostV2
      operationId: getCatalogPostV2UsingPOST
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/catalog/products/lastSeen:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLastSeenProducts
      operationId: getLastSeenProductsUsingGET
      parameters:
        - name: count
          in: query
          description: count
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/lastSeenPage:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLastSeenProductsPage
      operationId: getLastSeenProductsPageUsingGET
      parameters:
        - name: page
          in: query
          description: page
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/liked:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedProducts
      operationId: getLikedProductsUsingGET
      parameters:
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: showSoldWithSimilar
          in: query
          required: false
          style: form
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/liked-page:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedProductsPage
      operationId: getLikedProductsPageUsingGET
      parameters:
        - name: page
          in: query
          description: page
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/liked-page/{userId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedProductsPage
      operationId: getLikedProductsPageUsingGET_1
      parameters:
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: page
          in: query
          description: page
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: showSoldWithSimilar
          in: query
          required: false
          style: form
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/liked/{userId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getLikedProducts
      operationId: getLikedProductsUsingGET_1
      parameters:
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: showSoldWithSimilar
          in: query
          required: false
          style: form
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/pricefollowings:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getFollowingProducts
      operationId: getFollowingProductsUsingGET
      parameters:
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getProduct
      operationId: getProductUsingGET
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: withSizeChart
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: supportCommunity
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: saleInfoId
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withPostsExistsFlag
          in: query
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/saleinfo:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getProductBySaleInfoId
      operationId: getProductIdBySaleInfoIdUsingGET
      parameters:
        - name: saleInfoId
          in: query
          required: true
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfProductIdDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/lite:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getProductLite
      operationId: getProductLiteUsingGET
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfProductDTOLite'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/blog:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getProductForBlog
      operationId: getProductForBlogUsingGET
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/images:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getProductImages
      operationId: getProductImagesUsingGET
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductImageDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/dislike:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: dislikeProduct
      operationId: dislikeProductUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/followPrice:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: followPrice
      operationId: followPriceUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/like:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: likeProduct
      operationId: likeProductUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/toggle:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: toggleLikeProduct
      operationId: toggleLikeProductUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfToggleResult'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/toggle/like:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: toggleLikeToLikeProduct
      operationId: toggleLikeToLikeProductUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfToggleResult'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/toggle/dislike:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: toggleLikeToDislikeProduct
      operationId: toggleLikeToDislikeProductUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfToggleResult'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/products/{productId}/unfollowPrice:
    put:
      tags:
        - catalog-controller-api-v-2
      summary: unfollowPrice
      operationId: unfollowPriceUsingPUT
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/sizeTree:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getSizeTree
      operationId: getSizeTreeUsingGET
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSizeTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - catalog-controller-api-v-2
      summary: getSizeTreePost
      operationId: getSizeTreePostUsingPOST
      parameters:
        - name: attributeTreeKeyword
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: attributeTreeLevelsLimit
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: categoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: endPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: exceptCategoriesIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: hasOurChoice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: hasSlides
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: interestingAttributeValues
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingBrands
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingConditions
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: interestingSizeType
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizeTypes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
            enum:
              - AGE
              - AU
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - DE
              - EU
              - FR
              - HEIGHT
              - INCHES
              - INT
              - IT
              - JEANS
              - JPN
              - NO_SIZE
              - BUST
              - RING_EUROPEAN
              - RING_RUSSIAN
              - RU
              - UK
              - US
        - name: interestingSizes
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: isAtOffice
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isDescriptionExists
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isNewCollection
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isOnSale
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPro
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isPublishedAfterTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isPublishedBeforeTimestamp
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: isStartPriceHigher
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVintage
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: isVip
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: order
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: page
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageLength
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: productsIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: retoucherId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerEmailSubstring
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: sellerId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: sellerIds
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: integer
              format: int64
        - name: sex
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - ADULT
              - BOY
              - CHILD
              - FEMALE
              - GIRL
              - MALE
        - name: sortAttribute
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - CHANGE_TIME
              - CHANGE_TIME_DESC
              - DISCOUNT
              - DISCOUNT_DESC
              - ID
              - ID_DESC
              - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
              - PRICE
              - PRICE_DESC
              - PRODUCT_STATE_TIME
              - PRODUCT_STATE_TIME_DESC
              - PUBLISH_TIME
              - PUBLISH_TIME_DESC
        - name: startPrice
          in: query
          required: false
          style: form
          schema:
            type: number
            format: bigdecimal
        - name: state
          in: query
          required: false
          style: form
          schema:
            type: string
            enum:
              - BANED
              - DELETED
              - DRAFT
              - HIDDEN
              - NEED_MODERATION
              - NEED_RETOUCH
              - PUBLISHED
              - REJECTED
              - RETOUCH_DONE
              - SECOND_EDITION
              - SOLD
        - name: storeCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: useUserSex
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: userLikedBrands
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: vendorCodeContains
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: withAttributeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withCategoryTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withRrp
          in: query
          required: false
          style: form
          schema:
            type: boolean
        - name: withSizeTree
          in: query
          required: false
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSizeTree'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/catalog/sizes/{categoryId}:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getSizes
      operationId: getSizesUsingGET
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfSizeTypeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

components:
  schemas:
    Api2ResponseOfListOfSizeTypeDTO:
      title: Api2ResponseOfListOfSizeTypeDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SizeTypeDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfSizeTree:
      title: Api2ResponseOfSizeTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SizeTree'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfPageOflong:
      title: Api2ResponseOfPageOflong
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOflong'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfToggleResult:
      title: Api2ResponseOfToggleResult
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ToggleResult'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfBoolean:
      title: Api2ResponseOfBoolean
      type: object
      properties:
        data:
          type: boolean
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfPageOfBrandDTO:
      title: Api2ResponseOfPageOfBrandDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfBrandDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    PageOfBrandDTO:
      title: PageOfBrandDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/BrandDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    Api2ResponseOfBrandDTO:
      title: Api2ResponseOfBrandDTO
      type: object
      properties:
        data:
          $ref: './partial.yaml#/components/schemas/BrandDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    SizeTree:
      title: SizeTree
      type: object
      properties:
        rootCategory:
          $ref: './partial.yaml#/components/schemas/CategoryDTO'
    SizeTypeDTO:
      title: SizeTypeDTO
      type: object
      properties:
        isDefault:
          type: boolean
        sizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        sizeTypeAbbreviation:
          type: string
        sizeTypeDescription:
          type: string
        values:
          type: array
          items:
            $ref: '#/components/schemas/SizeDTO'
    PageOflong:
      title: PageOflong
      type: object
      properties:
        items:
          type: array
          items:
            type: integer
            format: int64
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    ToggleResult:
      title: ToggleResult
      type: object
      properties:
        actualLikesCount:
          type: integer
          format: int32
        canBeLiked:
          type: boolean
    SizeDTO:
      title: SizeDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        optionalValuesForAllSizeTypes:
          type: string
    Api2ResponseOfAvailableFilters:
      title: Api2ResponseOfAvailableFilters
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AvailableFilters'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfAttributeTree:
      title: Api2ResponseOfAttributeTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AttributeTree'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    AvailableFilters:
      title: AvailableFilters
      type: object
      properties:
        atOffice:
          type: boolean
        attributeTree:
          $ref: '#/components/schemas/AttributeTree'
        brand:
          type: array
          items:
            type: integer
            format: int64
        productModel:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/ProductModelDTORes'
        category:
          type: array
          items:
            type: integer
            format: int64
        categoryTree:
          $ref: './partial.yaml#/components/schemas/CategoryTree'
        endPrice:
          type: number
          format: bigdecimal
        filter:
          type: array
          items:
            type: integer
            format: int64
        hasOurChoice:
          type: boolean
        newCollection:
          type: boolean
        notPro:
          type: boolean
        onSale:
          type: boolean
        pro:
          type: boolean
        productCondition:
          type: array
          items:
            type: integer
            format: int64
        productCount:
          type: integer
          format: int64
        size:
          type: array
          items:
            type: integer
            format: int64
        sizeTree:
          $ref: '#/components/schemas/SizeTree'
        startPrice:
          type: number
          format: bigdecimal
        startPriceHigher:
          type: boolean
        vintage:
          type: boolean
    AttributeTree:
      title: AttributeTree
      type: object
      properties:
        rootCategory:
          $ref: './partial.yaml#/components/schemas/CategoryDTO'
    ProductsRequest:
      type: object
      properties:
        attributeTreeKeyword:
          type: string
        attributeTreeLevelsLimit:
          type: integer
          format: int32
        categoriesIds:
          type: array
          items:
            type: integer
            format: int64
        currencyCode:
          type: string
        endPrice:
          type: number
        exceptCategoriesIds:
          type: array
          items:
            type: integer
            format: int64
        hasOurChoice:
          type: boolean
        hasSlides:
          type: boolean
        interestingAttributeValues:
          type: array
          items:
            type: integer
            format: int64
        exceptBrands:
          type: array
          items:
            type: integer
            format: int64
        interestingBrands:
          type: array
          items:
            type: integer
            format: int64
        interestingConditions:
          type: array
          items:
            type: integer
            format: int64
        interestingProductModels:
          type: array
          items:
            type: integer
            format: int64
        interestingSizeType:
          type: string
          enum:
            - RU
            - EU
            - US
            - INT
            - UK
            - FR
            - IT
            - DE
            - AU
            - JPN
            - INCHES
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - RING_RUSSIAN
            - RING_EUROPEAN
            - JEANS
            - HEIGHT
            - AGE
            - NO_SIZE
            - BUST
        interestingSizeTypes:
          type: array
          items:
            type: string
            enum:
              - RU
              - EU
              - US
              - INT
              - UK
              - FR
              - IT
              - DE
              - AU
              - JPN
              - INCHES
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - RING_RUSSIAN
              - RING_EUROPEAN
              - JEANS
              - HEIGHT
              - AGE
              - NO_SIZE
              - BUST
        interestingSizes:
          type: array
          items:
            type: integer
            format: int64
        isAtOffice:
          type: boolean
        isDescriptionExists:
          type: boolean
        isInBoutique:
          type: boolean
        isNewCollection:
          type: boolean
        isOnSale:
          type: boolean
        isPro:
          type: boolean
        isPublishedAfterTimestamp:
          type: integer
          format: int64
        isPublishedBeforeTimestamp:
          type: integer
          format: int64
        isStartPriceHigher:
          type: boolean
        isStreetwear:
          type: boolean
        isVintage:
          type: boolean
        isVip:
          type: boolean
        order:
          type: string
        page:
          type: integer
          format: int32
        pageLength:
          type: integer
          format: int32
        productsIds:
          type: array
          items:
            type: integer
            format: int64
        retoucherId:
          type: integer
          format: int64
        sellerEmailSubstring:
          type: string
        sellerId:
          type: integer
          format: int64
        sellerIds:
          type: array
          items:
            type: integer
            format: int64
        sex:
          type: string
          enum:
            - MALE
            - FEMALE
            - BOY
            - GIRL
            - ADULT
            - CHILD
        sortAttribute:
          type: string
          enum:
            - ID
            - ID_DESC
            - PRICE
            - PRICE_DESC
            - PUBLISH_TIME_DESC
            - PROMOTION_TIME_DESC
            - PUBLISH_TIME
            - PRODUCT_STATE_TIME_DESC
            - PRODUCT_STATE_TIME
            - CHANGE_TIME_DESC
            - CHANGE_TIME
            - DISCOUNT_DESC
            - DISCOUNT
            - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
        startPrice:
          type: number
        state:
          type: string
          enum:
            - DRAFT
            - SECOND_EDITION
            - NEED_MODERATION
            - NEED_RETOUCH
            - RETOUCH_DONE
            - REJECTED
            - PUBLISHED
            - HIDDEN
            - SOLD
            - DELETED
            - BANED
        storeCodeContains:
          type: string
        useUserSex:
          type: boolean
        userLikedBrands:
          type: boolean
        vendorCodeContains:
          type: string
        viewQualification:
          $ref: '#/components/schemas/ViewQualification'
        withAttributeTree:
          type: boolean
        withCategoryTree:
          type: boolean
        withRrp:
          type: boolean
        withSizeTree:
          type: boolean
      title: ProductsRequest
    BrandsRequest:
      type: object
      properties:
        attributeTreeKeyword:
          type: string
        attributeTreeLevelsLimit:
          type: integer
          format: int32
        categoriesIds:
          type: array
          items:
            type: integer
            format: int64
        currencyCode:
          type: string
        endPrice:
          type: number
        exceptCategoriesIds:
          type: array
          items:
            type: integer
            format: int64
        hasOurChoice:
          type: boolean
        hasSlides:
          type: boolean
        interestingAttributeValues:
          type: array
          items:
            type: integer
            format: int64
        interestingBrands:
          type: array
          items:
            type: integer
            format: int64
        interestingConditions:
          type: array
          items:
            type: integer
            format: int64
        interestingProductModels:
          type: array
          items:
            type: integer
            format: int64
        interestingSizeType:
          type: string
          enum:
            - RU
            - EU
            - US
            - INT
            - UK
            - FR
            - IT
            - DE
            - AU
            - JPN
            - INCHES
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - RING_RUSSIAN
            - RING_EUROPEAN
            - JEANS
            - HEIGHT
            - AGE
            - NO_SIZE
            - BUST
        interestingSizeTypes:
          type: array
          items:
            type: string
            enum:
              - RU
              - EU
              - US
              - INT
              - UK
              - FR
              - IT
              - DE
              - AU
              - JPN
              - INCHES
              - CENTIMETERS
              - COLLAR_CENTIMETERS
              - COLLAR_INCHES
              - RING_RUSSIAN
              - RING_EUROPEAN
              - JEANS
              - HEIGHT
              - AGE
              - NO_SIZE
              - BUST
        interestingSizes:
          type: array
          items:
            type: integer
            format: int64
        isAtOffice:
          type: boolean
        isDescriptionExists:
          type: boolean
        isInBoutique:
          type: boolean
        isNewCollection:
          type: boolean
        isOnSale:
          type: boolean
        isPro:
          type: boolean
        isPublishedAfterTimestamp:
          type: integer
          format: int64
        isPublishedBeforeTimestamp:
          type: integer
          format: int64
        isStartPriceHigher:
          type: boolean
        isStreetwear:
          type: boolean
        isVintage:
          type: boolean
        isVip:
          type: boolean
        order:
          type: string
        page:
          type: integer
          format: int32
        pageLength:
          type: integer
          format: int32
        productsCount:
          type: integer
          format: int32
        productsIds:
          type: array
          items:
            type: integer
            format: int64
        retoucherId:
          type: integer
          format: int64
        sellerEmailSubstring:
          type: string
        sellerId:
          type: integer
          format: int64
        sellerIds:
          type: array
          items:
            type: integer
            format: int64
        sex:
          type: string
          enum:
            - MALE
            - FEMALE
            - BOY
            - GIRL
            - ADULT
            - CHILD
        sortAttribute:
          type: string
          enum:
            - ID
            - ID_DESC
            - PRICE
            - PRICE_DESC
            - PUBLISH_TIME_DESC
            - PROMOTION_TIME_DESC
            - PUBLISH_TIME
            - PRODUCT_STATE_TIME_DESC
            - PRODUCT_STATE_TIME
            - CHANGE_TIME_DESC
            - CHANGE_TIME
            - DISCOUNT_DESC
            - DISCOUNT
            - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
        startPrice:
          type: number
        state:
          type: string
          enum:
            - DRAFT
            - SECOND_EDITION
            - NEED_MODERATION
            - NEED_RETOUCH
            - RETOUCH_DONE
            - REJECTED
            - PUBLISHED
            - HIDDEN
            - SOLD
            - DELETED
            - BANED
        storeCodeContains:
          type: string
        useUserSex:
          type: boolean
        userLikedBrands:
          type: boolean
        vendorCodeContains:
          type: string
        viewQualification:
          $ref: '#/components/schemas/ViewQualification'
        withAttributeTree:
          type: boolean
        withCategoryTree:
          type: boolean
        withDescription:
          type: boolean
        withProducts:
          type: boolean
        withRrp:
          type: boolean
        withSizeTree:
          type: boolean
      title: BrandsRequest
    ViewQualification:
      type: object
      title: ViewQualification