# TODO REQUIRED ID
openapi: 3.0.3

info:
  title: Oskelly API v2, counterparties
  description: Oskelly API v2, counterparties
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

tags:
  - name: counterparty-controller-api-v-2
    description: Counterparty Controller Api V 2

paths:
  /api/v2/counterparty/availableTypes:
    get:
      tags:
        - counterparty-controller-api-v-2
      summary: getAvailableCounterpartyTypes
      operationId: getAvailableCounterpartyTypesUsingGET
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfCounterpartyType'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

components:
  schemas:
    Api2ResponseOfListOfCounterpartyType:
      title: Api2ResponseOfListOfCounterpartyType
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/CounterpartyType'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string