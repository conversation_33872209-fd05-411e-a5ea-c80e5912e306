openapi: 3.0.3

info:
  title: Oskelly API v2, dictionary
  description: Oskelly API v2, dictionary
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

tags:
  - name: dictionary-controller-api-v-2
    description: Dictionary Controller Api V 2

paths:
  /api/v2/dictionary/categoryTree:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getCategoryTree
      operationId: getCategoryTreeUsingGET_1
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: ./partial.yaml#/components/schemas/Api2ResponseOfCategoryTree
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/dictionary/brands:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getBrands
      operationId: getBrandsUsingGET_1
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: ./partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/dictionary/attributes:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getAttributes
      operationId: getAttributesUsingGET_1
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: ./partial.yaml#/components/schemas/Api2ResponseOfListOfAttributeDTO
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/dictionary/order/source/info:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getOrderSourceInfo
      operationId: getOrderSourceInfoUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfOrderSourceInfoDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/dictionary/legal-entities:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getLegalEntities
      operationId: getLegalEntitiesUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfLegalEntitiesDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/dictionary/product-tags:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getProductTags
      operationId: getProductTags
      parameters:
        - name: category
          in: query
          required: false
          description: Категория тега
          schema:
            $ref: './partial.yaml#/components/schemas/ProductTagCategory'
        - name: locationType
          in: query
          required: false
          description: Тип тега расположения, используется при указанной категории тега LOCATION
          schema:
            $ref: './partial.yaml#/components/schemas/ProductTagLocationType'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductTagDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/dictionary/promocode-types:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getPromocodeTypes
      operationId: getPromocodeTypes
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfPromocodeTypeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

  /api/v2/dictionary/expertise/reconciliation/negative-reasons:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: getExpertiseReconciliationNegativeReasons
      operationId: getExpertiseReconciliationNegativeReasons
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfDictionaryDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

  /api/v2/dictionary/countries:
    get:
      tags:
        - dictionary-controller-api-v-2
      summary: get countries
      operationId: getCountries
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfDictionaryDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
