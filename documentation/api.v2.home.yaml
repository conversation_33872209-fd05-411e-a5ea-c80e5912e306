openapi: 3.0.3
info:
  title: Oskelly Admin API v2, home
  description: Oskelly Admin API v2, home
  version: "1.0"
paths:
  /api/v2/home:
    get:
      tags:
        - home page
      summary: Get primary page data
      operationId: getPrimaryPageData
      parameters:
        - in: query
          name: category
          schema:
            $ref: './partial.yaml#/components/schemas/PrimaryPageType'
          required: true
        - in: query
          name: pageNumber
          schema:
            type: integer
          required: true
        - in: query
          name: supportBannerPage
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: countryId
          schema:
            type: integer
          required: false
        - in: query
          name: supportHorizontalSelections
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: newHomeDesignEnabled
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: recentlyViewedDisabled
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: withStories
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: withStoreez
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: withInstagramFeed
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: withSEO
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: blockListVersion
          schema:
            type: integer
            default: false
          required: false
        - in: query
          name: withOSocialPostsCollections
          schema:
            type: boolean
            default: false
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /api/v2/home/<USER>
    get:
      tags:
        - banner page
      summary: get banner page data
      operationId: getBannerContent
      parameters:
        - in: query
          name: id
          schema:
            type: string
          required: true
        - in: query
          name: pageNumber
          schema:
            type: integer
            default: 0
          required: false
        - in: query
          name: countryId
          schema:
            type: integer
          required: false
        - in: query
          name: productFiltrationEnabled
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: withStories
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: withSEO
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: blockListVersion
          schema:
            type: integer
            default: false
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/v2/home/<USER>/filterableItems:
    parameters: []
    post:
      summary: get banner filterable items
      operationId: post-api-v2-home-banner-filterableItems
      parameters:
        - schema:
            type: string
          in: query
          name: id
          required: true
        - schema:
            type: string
          in: query
          name: countryId
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.products.filter.yaml#/components/schemas/ProductFilterItemsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/v2/home/<USER>
    post:
      summary: get filterableContentItems
      operationId: post-api-v2-home-filterableContentItems
      parameters:
        - schema:
            type: string
          in: query
          name: countryId
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.products.filter.yaml#/components/schemas/ProductFilterItemsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseListContent'
  /api/v2/home/<USER>
    parameters: [ ]
    post:
      summary: get filterable items
      operationId: post-api-v2-home-filterableItems
      parameters:
        - schema:
            type: string
          in: query
          name: countryId
      requestBody:
        content:
          application/json:
            schema:
              $ref: './api.v2.products.filter.yaml#/components/schemas/ProductFilterItemsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  /api/v2/home/<USER>
    get:
      summary: Get primary banners list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseBannerDTOList'
      operationId: get-api-v2-home-primaryBanners
      parameters:
        - in: query
          name: category
          schema:
            $ref: './partial.yaml#/components/schemas/PrimaryPageType'
          required: true
      description: ''
  /api/v2/home/<USER>
    get:
      summary: Get promo banner
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponsePromoBannerContent'
      operationId: get-api-v2-home-promoBanners
      description: ''
  /api/v2/home/<USER>
    get:
      tags:
        - social page
      summary: get social page data
      operationId: get-api-v2-home-social
      parameters:
        - in: query
          name: anchor
          schema:
            type: string
          required: false
        - in: query
          name: count
          schema:
            type: integer
            default: 0
          required: false
        - in: query
          name: selectedIds
          schema:
            type: array
            items:
             type: string
          required: false
        - in: query
          name: disableFeed
          schema:
            type: boolean
            default: false
          required: false
        - in: query
          name: showStreams
          schema:
            type: boolean
            default: true
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseSocialContent'
  /api/v2/home/<USER>/promo-banner/{bannerSettingId}:
    get:
      summary: Get promo banner by banner setting id
      parameters:
        - in: path
          name: bannerSettingId
          schema:
            type: string
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponsePromoBannerContent'
      operationId: get-api-v2-home-promoBanner-by-banner-id
      description: ''

  /api/v2/home/<USER>
    get:
      summary: Get instagram feed
      parameters:
        - in: query
          name: maxItemsCount
          schema:
            type: integer
          required: true
        - in: query
          name: minLikesCount
          schema:
            type: integer
          required: false
        - in: query
          name: minCommentsCount
          schema:
            type: integer
          required: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseInstagramFeedItemListContent'
      operationId: getInstagramFeed

components:
  schemas:
    ApiResponse:
      title: ApiResponse
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PrimaryPageDTO'
    ApiResponseListContent:
      title: ApiResponseListContent
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PrimaryContentDTO'
    ApiResponseBannerDTOList:
      title: ApiResponseApiResponseBannerDTOList
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BannerDTO'
    ApiResponsePromoBannerContent:
      title: ApiResponsePromoBannerContent
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PromoBannerContent'

    ApiResponseSocialContent:
      title: ApiResponseSocialContent
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SocialContent'

    ApiResponseInstagramFeedItemListContent:
      title: ApiResponseInstagramFeedItemListContent
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/InstagramFeedItem'

    PrimaryPageDTO:
      title: PrimaryPageDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/PrimaryContentDTO'
        seoLinks:
          $ref: '#/components/schemas/PrimaryContentDTO'
        pageNumber:
          type: integer
        pageCount:
          type: integer
        itemsCount:
          type: integer
        concierge:
          type: boolean
        onboarding:
          type: string
          enum:
            - PRODUCT_REQUEST_ONBOARDING
            - CONCIERGE_BUYER_ONBOARDING
        itemType:
          type: string
          enum:
            - PRODUCT
            - PRODUCT_REQUEST
        pageTitle:
          type: string

    PrimaryContentDTO:
      title: PrimaryContentDTO
      type: object
      properties:
        title:
          type: string
        imagePath:
          type: string
        contentType:
          $ref: '#/components/schemas/ContentType'
        id:
          type: string
        contentList:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/PromoBannerDTO'
              - $ref: '#/components/schemas/BannerDTO'
              - $ref: '#/components/schemas/CollectionDTO'
              - $ref: '#/components/schemas/CelebrityDTO'
              - $ref: '#/components/schemas/PrimaryPageProductDTO'
              - $ref: '#/components/schemas/BlogDTO'
              - $ref: '#/components/schemas/TextSlideDTO'
              - $ref: '#/components/schemas/AdditionalCollectionDTO'
              - $ref: '#/components/schemas/PrimaryContentDTOProductItem'
              - $ref: '#/components/schemas/ButtonControlViewDTO'
              - $ref: '#/components/schemas/FooterButtonDTO'
              - $ref: '#/components/schemas/SeoLinkGroupDto'

        content:
          oneOf:
            - $ref: '#/components/schemas/PromoBannerDTO'
            - $ref: '#/components/schemas/BannerDTO'
            - $ref: '#/components/schemas/CollectionDTO'
            - $ref: '#/components/schemas/CelebrityDTO'
            - $ref: '#/components/schemas/PrimaryPageProductDTO'
            - $ref: '#/components/schemas/BlogDTO'
            - $ref: '#/components/schemas/AdditionalCollectionDTO'
            - $ref: '#/components/schemas/FilterDTO'
            - $ref: '#/components/schemas/StoriesDTO'
            - $ref: '#/components/schemas/StoreezContentDTO'
            - $ref: '#/components/schemas/InstagramFeedDTO'
            - $ref: '#/components/schemas/ShelfWithFiltersDTO'
            - $ref: '#/components/schemas/TextControlViewDTO'
            - $ref: '#/components/schemas/OSocialPostsCollectionDTO'

        nextAnchor:
          type: string
        additionalData:
          $ref: '#/components/schemas/PrimaryContentAdditionalData'
        chapterIndex:
          type: integer
          format: int64
        updateTime:
          type: string
          format: date-time
        segmentId:
          type: integer
          format: int64
      required:
        - id
    PrimaryContentDTOProductItem:
      title: PrimaryContentDTO
      type: object
      properties:
        contentType:
          $ref: '#/components/schemas/ContentType'
        content:
          $ref: '#/components/schemas/PrimaryPageProductDTO'
    PrimaryContentAdditionalData:
      title: PrimaryContentAdditionalData
      type: object
      properties:
        count:
          type: integer
          format: int64
        imgPathList:
          type: array
          items:
            type: string
        jumpLink:
          type: string
        description:
          type: string
        isSellerHidden:
          type: boolean
        isOnePageContent:
          type: boolean
        isJournalMode:
          type: boolean
        textSlideInterval:
          description: Интервал перелистывания слайдов в мс
          type: integer
          format: int64
    PromoBannerDTO:
      title: PromoBannerDTO
      type: object
      properties:
        title:
          type: string
    BannerDTO:
      title: BannerDTO
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        additionalDescription:
          type: string
        imgPath:
          type: string
        link:
          type: string
        catalogLink:
          type: string
        buttonTitle:
          type: string
        buttonLink:
          type: string
        badge:
          type: string
        descriptionButtonText:
          type: string
        descriptionButtonUrl:
          type: string
        isFlexiblyButton:
          type: boolean
        isEndButton:
          type: boolean
        isButtonInsideBanner:
          type: boolean
        isJournalMode:
          type: boolean
    CollectionDTO:
      title: CollectionDTO
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        imgPath:
          type: string
        link:
          type: string
    CelebrityDTO:
      title: CelebrityDTO
      type: object
      properties:
        id:
          type: string
        description:
          type: string
        link:
          type: string
        subscribeLink:
          type: string
        socialNetworkURI:
          type: string
        productCount:
          type: integer
        instagramFollowerCount:
          type: string
        userDTO:
          $ref: '#/components/schemas/PrimaryPageUserDTO'
    PrimaryPageUserDTO:
      title: PrimaryPageUserDTO
      type: object
      nullable: true
      properties:
        id:
          type: string
        name:
          type: string
        nickName:
          type: string
        avatarPath:
          type: string
        type:
          type: string
        isTrusted:
          type: boolean
        isSubscribed:
          type: boolean
        communityBadge:
          $ref: './partial.yaml#/components/schemas/CommunityBadge'
        sellerType:
          $ref: './partial.yaml#/components/schemas/SellerType'
    PrimaryPageProductDTO:
      title: PrimaryPageProductDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        imgPath:
          type: string
        brandName:
          type: string
        categoryName:
          type: string
        conditionId:
          type: integer
          format: int64
        conditionName:
          type: string
        price:
          type: integer
        oldPrice:
          type: integer
        discount:
          type: integer
        isLiked:
          type: boolean
        likeCount:
          type: integer
        withBadge:
          type: boolean
        formattedSize:
          type: string
        sizeType:
          $ref: '#/components/schemas/SizeType'
        sizes:
          type: array
          items:
            $ref: '#/components/schemas/SizeValueDTO'
        seller:
          $ref: '#/components/schemas/PrimaryPageUserDTO'
        split:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        tabbySplit:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        yandexPlus:
          $ref: './partial.yaml#/components/schemas/YandexPlusInfo'
        exclusiveSelectionTime:
          type: string
          format: date-time
        exclusiveSelectionTimeForLowStatuses:
          type: string
          format: date-time
    SizeValueDTO:
      title: SizeValueDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        productSizeType:
          $ref: '#/components/schemas/SizeType'
        productSizeValue:
          type: string
        categorySizeType:
          $ref: '#/components/schemas/SizeType'
        categorySizeValue:
          type: string
        interestingSizeType:
          $ref: '#/components/schemas/SizeType'
        interestingSizeValue:
          type: string
        count:
          type: integer
        additionalSizeValues:
          type: object
          additionalProperties:
            type: integer
        offer:
          $ref: '#/components/schemas/OfferDTO'
        payoutInfo:
          $ref: '#/components/schemas/PayoutInfoDTO'
        sku:
          type: string
    OfferDTO:
      title: OfferDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        price:
          type: integer
          format: int64
        offerStatus:
          $ref: '#/components/schemas/OfferStatus'
        negotiatedPrice:
          type: integer
          format: int64
        consumed:
          type: boolean
    PayoutInfoDTO:
      title: OfferDTO
      type: object
      properties:
        value:
          type: integer
          format: bigdecimal
        currencyCode:
          type: string
    BlogDTO:
      title: BlogDTO
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        imgPath:
          type: string
        link:
          type: string
    TextSlideDTO:
      title: TextSlideDTO
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        iconPath:
          type: string
        deepLinkUrl:
          type: string
    FilterDTO:
      title: FilterDTO
      type: object
      properties:
        title:
          type: string
        presets:
          type: object
          additionalProperties:
            $ref: './api.v2.products.filter.yaml#/components/schemas/FilterValue'
        baseCategory:
          type: integer
          format: int64
        source:
          type: string
          enum:
            - PRIMARY
            - BANNER
            - CONCIERGE_BANNER
            - PRODUCT_REQUEST_BANNER
            - CATALOG
            - PROFILE
        displayMode:
          type: string
          enum:
            - DEFAULT
            - ONLY_HOT_FILTERS

    StoriesDTO:
      title: StoriesDTO
      type: object
      properties:
        disableFeed:
          type: boolean
        showTopPanel:
          type: boolean
        showStreams:
          type: boolean
        users:
          type: array
          items:
            $ref: '#/components/schemas/PrimaryPageUserDTO'

    StoreezContentDTO:
      title: StoreezContentDTO
      type: object
      properties:
        storeezId:
          type: string

    InstagramFeedDTO:
      title: InstagramFeedDTO
      type: object
      properties:
        feedPath:
          type: string
          description: Путь с параметрами для получения ленты
          example: /api/v2/home/<USER>
        instagramPageUrl:
          type: string
          description: Url для перехода на страницу oskelly.co в instagram
        toInstagramPageButtonTitle:
          type: string
          description: Название кнопки для перехода в instagram
        indicatorsEnabled:
          type: boolean
          description: Отображать или нет количество лайков и комментов

    SocialDTO:
      title: StoriesDTO
      type: object
      properties:
        id:
          oneOf:
            - type: string
            - type: integer
              format: int64
        title:
          type: string
        imgPath:
          type: string
        isSeen:
          type: boolean
        link:
          type: string
        anchor:
          type: string
        contentType:
          $ref: '#/components/schemas/PrimaryContentType'

    OSocialPostsCollectionDTO:
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/OSocialPostsCollectionFilterDTO'

    OSocialPostsCollectionFilterDTO:
      type: object
      properties:
        name:
          type: string
        value:
          type: string

    AdditionalCollectionDTO:
      title: AdditionalCollectionDTO
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        imgPath:
          type: string
        link:
          type: string
        categoryDisplayName:
          type: string
        isSellerHidden:
          type: boolean

    ContentType:
      type: string
      enum:
        - ADV_BANNER
        - BANNER
        - SINGLE_BANNER
        - COLLECTION
        - ADDITIONAL_COLLECTION
        - CELEBRITY
        - RECENTLY_VIEW
        - BLOG
        - TEXT_SLIDES
        - INSTAGRAM
        - PRODUCT
        - SELECTION
        - H_SELECTION
        - H_SELECTION_WITH_BANNER
        - LIST_ITEMS
        - STORY
        - STREAMSALE
        - LINK
        - USERS_COLLECTION
        - ARTICLE
        - AD_PRODUCT
        - SOCIAL_COLLECTION
        - FILTERABLE_ITEMS
        - SQUARE_BANNER_WITH_BUTTON
        - SMALL_BANNER_WITH_BUTTON
        - PRODUCT_REQUEST_FILTERABLE_ITEMS
        - STORIES
        - STOREEZ
        - INSTAGRAM_FEED
        - VERTICAL_BANNER_COLLECTION
        - BUTTON_CONTROL
        - TEXT_CONTROL
        - FILTERABLE_SHELF
        - LINK_GROUPS_BLOCK
        - OSOCIAL_POSTS_COLLECTION
        - SPACER
        - ORDERS
        - PROMO_AUTO_COLLECTION
    SizeType:
      type: string
      enum:
        - RU
        - EU
        - US
        - INT
        - UK
        - FR
        - IT
        - DE
        - AU
        - JPN
        - INCHES
        - CENTIMETERS
        - COLLAR_CENTIMETERS
        - COLLAR_INCHES
        - RING_RUSSIAN
        - RING_EUROPEAN
        - JEANS
        - HEIGHT
        - AGE
        - NO_SIZE
        - BUST
    OfferStatus:
      type: string
      enum:
        - PENDING
        - ACCEPTED
        - REJECTED
    PromoBannerContent:
      title: PromoBannerContent
      type: object
      properties:
        id:
          type: string
        contentType:
          type: string
          enum:
            - ADV_BANNER
        content:
          $ref: '#/components/schemas/PromoBannerDTO'
      required:
        - id
        - contentType
        - content

    SocialContent:
      title: SocialContent
      type: object
      properties:
        contentList:
          type: array
          items:
            $ref: '#/components/schemas/SocialDTO'
        nextAnchor:
          type: string
      required:
        - contentList

    ButtonControlViewDTO:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        url:
          type: string
        imageUrl:
          type: string
      required:
        - id

    FooterButtonDTO:
      type: object
      properties:
        title:
          type: string
        link:
          type: string
        type:
          type: string
          enum:
            - DEEPLINK
            - SHARE
      required:
        - title
        - link
        - type

    SeoLinkGroupDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        links:
          type: array
          items:
            $ref: '#/components/schemas/SeoLinkDto'

    SeoLinkDto:
      type: object
      properties:
        text:
          type: string
        url:
          type: string

    TextControlViewDTO:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        text:
          type: string
        imageUrl:
          type: string
        imageWidth:
          type: integer
        imageHeight:
          type: integer
        userId:
          type: string
        userSubtitle:
          type: string
        userName:
          type: string
        userAvatarUrl:
          type: string

    ShelfWithFiltersDTO:
      type: object
      properties:
        presetFilter:
          $ref: '#/components/schemas/FilterDTO'
        options:
          type: array
          items:
            $ref: '#/components/schemas/ShelfWithFiltersOptionDTO'
        withFilterOptions:
          type: boolean

    ShelfWithFiltersOptionDTO:
      type: object
      properties:
        title:
          type: string
        filter:
          type: object
        contents:
          type: array
          items:
            $ref: '#/components/schemas/PrimaryContentDTO'

    InstagramFeedItem:
      title: InstagramFeedItem
      type: object
      properties:
        id:
          type: string
          description: Оригинальный ИД
        mediaUrl:
          type: string
          description: Url медиа файла
        thumbnailUrl:
          type: string
          description: Url превью медиа файла (заполняется для видео)
        caption:
          type: string
          description: Заголовок поста
        likesCount:
          type: integer
          format: int64
          description: Количество лайков
        commentsCount:
          type: integer
          format: int64
          description: Количество комментариев
        timestamp:
          type: string
          description: Дата создания
        userName:
          type: string
          description: Имя пользователя, разместившего пост
        url:
          type: string
          description: Url поста

    PrimaryContentType:
      type: string
      enum:
         - ADV_BANNER
         - BANNER
         - SINGLE_BANNER
         - COLLECTION
         - ADDITIONAL_COLLECTION
         - CELEBRITY
         - RECENTLY_VIEW
         - BLOG
         - INSTAGRAM
         - PRODUCT
         - SELECTION
         - H_SELECTION
         - H_SELECTION_WITH_BANNER
         - LIST_ITEMS
         - STORY
         - STREAMSALE
         - LINK
         - USERS_COLLECTION
         - ARTICLE
         - AD_PRODUCT
         - CONCIERGE_BANNER
         - SOCIAL_COLLECTION
         - FILTERABLE_ITEMS
         - PRODUCT_REQUEST
         - SQUARE_BANNER_WITH_BUTTON
         - SMALL_BANNER_WITH_BUTTON
         - PRODUCT_REQUEST_FILTERABLE_ITEMS
         - STORIES
         - STOREEZ
         - INSTAGRAM_FEED
         - BUTTON_CONTROL
         - TEXT_CONTROL
         - FILTERABLE_SHELF
         - OSOCIAL_POSTS_COLLECTION
         - SPACER
         - ORDERS
         - PROMO_AUTO_COLLECTION