openapi: 3.0.3
info:
  title: Oskelly API
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
paths:
  /api/v2/image/upload:
    post:
      tags:
        - image-controller-api-v-2
      summary: uploadImagesToCloud
      operationId: uploadImagesToCloudUsingPOST
      parameters:
        - name: context
          in: query
          description: context
          required: true
          schema:
            $ref: '#/components/schemas/ImageFolder'
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: array
                  items:
                    type: string
                    format: binary

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfUploadedImageDto'
  /api/v2/image/upload/single:
    post:
      tags:
        - image-controller-api-v-2
      summary: uploadImageToCloud
      operationId: uploadImageToCloudUsingPOST
      parameters:
        - name: context
          in: query
          description: context
          required: true
          schema:
            $ref: '#/components/schemas/ImageFolder'
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfUploadedImageDto'

components:
  schemas:
    Api2ResponseOfListOfUploadedImageDto:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/UploadedImageDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfListOfUploadedImageDto
    Api2ResponseOfUploadedImageDto:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/UploadedImageDto'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfUploadedImageDto
    UploadedImageDto:
      type: object
      properties:
        extension:
          type: string
        imageURL:
          type: string
        name:
          type: string
        path:
          type: string
      title: UploadedImageDto
    ImageFolder:
      type: string
      enum:
        - CONCIERGE
        - PRODUCT_REQUEST
        - SALE_REQUEST
        - PRODUCT
        - COMMENT
        - DEFECT
