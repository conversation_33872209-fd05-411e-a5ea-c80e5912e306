openapi: 3.0.3

info:
  title: Oskelly API v2, Integration Auth
  version: 1.0.0

tags:
  - name: integration-auth-controller-api-v-2
    description: Integration Auth Controller Api V 2

paths:
  /api/v2/integration/auth/getYandexUserJwt:
    get:
      tags:
        - integration-auth-controller-api-v-2
      summary: getYandexUserJwt
      operationId: getYandexUserJwtUsingGET
      security:
        - YandexAccessTokenHeader: [ ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Not Found

  /api/v2/integration/auth/revokeYandexAccessToken:
    get:
      tags:
        - integration-auth-controller-api-v-2
      summary: revokeYandexAccessToken
      operationId: revokeYandexAccessTokenUsingGET
      security:
        - YandexAccessTokenHeader: [ ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Not Found

  /api/v2/integration/auth/getGoogleUserJwt:
    get:
      tags:
        - integration-auth-controller-api-v-2
      summary: getGoogleUserJwt
      operationId: getGoogleUserJwtUsingGET
      parameters:
        - name: redirectUri
          in: query
          description: "redirect URI который which used for getting auth code"
          required: true
          schema:
            type: string
      security:
        - GoogleAuthCodeHeader: [ ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Not Found

  /api/v2/integration/auth/getAppleUserJwt:
    get:
      tags:
        - integration-auth-controller-api-v-2
      summary: getAppleUserJwt
      operationId: getAppleUserJwtUsingGET
      parameters:
        - name: appleKid
          in: query
          description: "redirect URI который which used for getting auth code"
          required: true
          schema:
            type: string
      security:
        - AppleAuthCodeHeader: [ ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Not Found

components:
  securitySchemes:
    YandexAccessTokenHeader:
      type: apiKey
      in: header
      name: Yandex-Access-Token
    GoogleAuthCodeHeader:
      type: apiKey
      in: header
      name: Google-Auth-Code
    AppleAuthCodeHeader:
      type: apiKey
      in: header
      name: Apple-Auth-Code

  schemas:
    Api2ResponseOfString:
      type: "object"
      properties:
        data:
          type: "string"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfString"
