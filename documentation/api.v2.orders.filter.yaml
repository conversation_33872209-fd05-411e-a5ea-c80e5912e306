openapi: 3.0.3
info:
  title: Order Filtration API
  version: '1.0'
servers:
  - url: 'http://localhost:8080'
paths:
  /api/v2/orders/filter:
    post:
      summary: getAvailableFilters
      operationId: post-api-v2-orders-filter
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfOrderFilters'
              examples: {}
      description: Получение доступных фильтров (+ опционально заказов) в зависимости от заданной фильтрации
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterRequest'
            examples: {}
        description: ''
  /api/v2/orders/filter/info:
    post:
      summary: getAvailableFilterInfo
      operationId: post-api-v2-orders-filter-info
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfOrderFilter'
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterInfoRequest'
            examples: {}
        description: ''
  /api/v2/orders/filter/items:
    post:
      summary: getItems
      operationId: post-api-v2-orders-filter-items
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfOrderDTO'
              examples: {}
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterItemsRequest'
            examples: {}
        description: ''
    parameters: []
  /api/v2/orders/filter/count:
    post:
      summary: getItemsCount
      operationId: post-api-v2-orders-filter-count
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfItemsCount'
              examples: {}
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterItemsRequest'
            examples: {}
        description: ''
      description: ''
    parameters: []
components:
  schemas:
    PriceValue:
      title: PriceValue
      x-stoplight:
        id: 4g2wpjptf9ysg
      type: object
      properties:
        lower:
          type: number
        upper:
          type: number
      description: Значение фильтра по цене для запроса сортировки
    FilterValue:
      title: FilterValue
      x-stoplight:
        id: 6rj9xibescb6f
      oneOf:
        - type: boolean
        - type: array
          items:
            type: integer
        - type: string
          properties: {}
        - type: array
          items:
            type: string
        - $ref: '#/components/schemas/PriceValue'
      x-examples: {}
      description: Значение фильтра для запроса сортировки
    ProductFilterInfoRequest:
      title: ProductFilterInfoRequest
      x-stoplight:
        id: wjt8znnhm9e4t
      type: object
      description: Запрос на получение доступных значений заданного фильтра
      properties:
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/FilterValue'
          description: |
            Коллекция фильтров со значениями. Доступные среди прочих фильтры:
            * offlineOnly: true | false - выдать только заказы, находящиеся offline | все заказы
        presets:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/FilterValue'
          description: |
            Коллекция пресетов со значениями. Доступные среди прочих пресеты:
            * offlineOnly: true | false - выдать только заказы, находящиеся offline | все заказы
        currencyCode:
          type: string
    ProductFilterItemsRequest:
      title: ProductFilterItemsRequest
      x-stoplight:
        id: 1eu1xjjexsbho
      description: 'Запрос на получение списка pfrfpjd, удовлетворяющих условиям фильтрации'
      allOf:
        - $ref: '#/components/schemas/ProductFilterInfoRequest'
        - type: object
          properties:
            sorting:
              type: string
              default: NEW
            page:
              type: integer
              default: 1
            pageLength:
              type: integer
              default: 60
      x-examples: {}
    ProductFilterRequest:
      title: ProductFilterRequest
      x-stoplight:
        id: 1eu1xjjexsbho
      description: Запрос на получение доступных значений фильтра и заказов по фильтрам
      allOf:
        - $ref: '#/components/schemas/ProductFilterItemsRequest'
        - type: object
          properties:
            withAvailableValues:
              type: boolean
              default: false
            withItems:
              type: boolean
              default: false
      x-examples: {}
    ProductSorting:
      title: ProductSorting
      x-stoplight:
        id: g69xaazp34b4z
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        isSelected:
          type: boolean
      required:
        - code
        - name
        - isSelected
    AbstractProductFilter:
      title: AbstractProductFilter
      x-stoplight:
        id: q0z1v269c3d6y
      type: object
      description: Базовая схема доступного фильтра
      properties:
        code:
          type: string
        type:
          type: string
        name:
          type: string
        description:
          type: string
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/FilterDescription'
      required:
        - code
        - type
        - name
    PriceFilter:
      title: PriceFilter
      x-stoplight:
        id: dliuywatjluww
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - PRICE
            lower:
              type: number
            upper:
              type: number
            selectedLower:
              type: number
            selectedUpper:
              type: number
          required:
            - type
      description: Доступный фильтр по цене
      x-examples: {}
    BooleanFilter:
      title: BooleanFilter
      x-stoplight:
        id: 93vcj5w2gkney
      description: Доступный фильтр по true/false значениям
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - BOOLEAN
            value:
              type: boolean
            isEnabled:
              type: boolean
          required:
            - type
            - value
            - isEnabled
      x-examples: {}
    ListValue:
      title: ListValue
      x-stoplight:
        id: 0s3vn03gwis7h
      type: object
      description: Значение из списка
      properties:
        id:
          type: integer
        value:
          type: string
      required:
        - id
        - value
    ListHotValue:
      title: ListHotValue
      x-stoplight:
        id: 16pggkk7w7fyr
      allOf:
        - $ref: '#/components/schemas/ListValue'
        - type: object
          properties:
            isSelected:
              type: boolean
          required:
            - isSelected
      description: Значение из списка значений быстрого выбора
    ListSectionEntry:
      title: ListSectionEntry
      x-stoplight:
        id: doh70gsrg14v5
      type: object
      description: Значение из набора элементов секции списка
      properties:
        id:
          type: integer
        value:
          type: string
        description:
          type: string
        isSelected:
          type: boolean
        image:
          type: string
      required:
        - id
        - value
        - isSelected
    ListSection:
      title: ListSection
      x-stoplight:
        id: an0c642x5nejh
      type: object
      description: Секция списка
      properties:
        name:
          type: string
        entries:
          type: array
          items:
            $ref: '#/components/schemas/ListSectionEntry'
      required:
        - name
        - entries
    MultiListFilter:
      title: MultiListFilter
      x-stoplight:
        id: rg853yzydym2c
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - MULTI_LIST
            values:
              type: array
              items:
                $ref: '#/components/schemas/ListSection'
            hotValues:
              type: array
              items:
                $ref: '#/components/schemas/ListHotValue'
            selectedValues:
              type: array
              items:
                $ref: '#/components/schemas/ListValue'
            searchableSections:
              type: array
              items:
                type: string
            hasMoreValues:
              type: boolean
          required:
            - type
            - searchableSections
            - hasMoreValues
      x-examples: {}
      description: Доступный фильтр по списочным значениям
    CategoryTreeValue:
      title: CategoryTreeValue
      x-stoplight:
        id: bid0vcnp9jyzb
      type: object
      description: Значение из фильтра с типом дерева категорий
      properties:
        id:
          type: integer
        name:
          type: string
        selectedValues:
          type: array
          items:
            $ref: '#/components/schemas/ListValue'
        isSelected:
          type: boolean
        children:
          type: array
          items:
            $ref: '#/components/schemas/CategoryTreeValue'
      required:
        - id
        - name
        - isSelected
      x-examples: {}
    CategoryTreeFilter:
      title: CategoryTreeFilter
      x-stoplight:
        id: n6lp172ryovo3
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - CATEGORY_TREE
            values:
              type: array
              items:
                $ref: '#/components/schemas/CategoryTreeValue'
            selectedValues:
              type: array
              items:
                $ref: '#/components/schemas/ListValue'
            hasMoreValues:
              type: boolean
          required:
            - type
            - hasMoreValues
      description: Доступный фильтр по значению категории
    ChartValue:
      title: ChartValue
      x-stoplight:
        id: 03ti06h5jpzc4
      type: object
      description: Значение из сетки размеров
      properties:
        code:
          type: string
        name:
          type: string
        value:
          type: string
      required:
        - code
        - name
        - value
    SizeValue:
      title: SizeValue
      x-stoplight:
        id: kq5r5xdeyw5qc
      type: object
      description: Значение доступного размера
      properties:
        id:
          type: integer
        name:
          type: string
        isSelected:
          type: boolean
        charts:
          type: array
          items:
            $ref: '#/components/schemas/ChartValue'
      required:
        - id
        - name
        - isSelected
    CategorySizeValue:
      title: CategorySizeValue
      x-stoplight:
        id: ci0qzuozvw4we
      type: object
      description: Доступные размеры в сетке категории
      properties:
        code:
          type: string
        name:
          type: string
        values:
          type: array
          items:
            $ref: '#/components/schemas/SizeValue'
      required:
        - code
        - name
    CategorySize:
      title: CategorySize
      x-stoplight:
        id: oqs4kvaehfge9
      type: object
      description: Доступные значения фильтра по размерам
      properties:
        id:
          type: integer
        name:
          type: string
        selectedValues:
          type: array
          items:
            $ref: '#/components/schemas/ListValue'
        sizesValue:
          $ref: '#/components/schemas/CategorySizeValue'
      required:
        - id
        - name
        - sizesValue
    SizeFilter:
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - SIZE
            values:
              type: array
              items:
                $ref: '#/components/schemas/CategorySize'
            selectedValues:
              type: array
              items:
                $ref: '#/components/schemas/ListValue'
            hasMoreValues:
              type: boolean
          required:
            - type
            - hasMoreValues
      description: Доступный фильтр по размерам
      title: SizeFilter
    ProductFilter:
      title: ProductFilter
      x-stoplight:
        id: vvaocjbrf47we
      oneOf:
        - $ref: '#/components/schemas/BooleanFilter'
        - $ref: '#/components/schemas/CategoryTreeFilter'
        - $ref: '#/components/schemas/MultiListFilter'
        - $ref: '#/components/schemas/PriceFilter'
        - $ref: '#/components/schemas/SizeFilter'
      x-examples: {}
      description: Доступный фильтр
    OrderFilters:
      title: OrderFilters
      x-stoplight:
        id: i47bpyhuj9t2j
      type: object
      description: 'Описание доступных фильтров, сортировок и заказов'
      properties:
        sorting:
          type: array
          items:
            $ref: '#/components/schemas/ProductSorting'
        filters:
          type: array
          items:
            $ref: '#/components/schemas/ProductFilter'
        hotFilters:
          type: array
          items:
            type: string
        itemsCount:
          type: integer
        items:
          $ref: '#/components/schemas/PageOfOrderDTO'
      required:
        - sorting
        - filters
        - hotFilters
        - itemsCount

    Api2ResponseOfPageOfOrderDTO:
      title: Api2ResponseOfPageOfOrderDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfOrderDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PageOfOrderDTO:
      title: PageOfOrderDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    OrderDTO:
      type: object
      properties:
        adminComment:
          type: string
        agentReport:
          $ref: './partial.yaml#/components/schemas/AgentReportDTO'
        agentReportConfirmed:
          type: boolean
        agentReportId:
          type: integer
          format: int64
        bonusesInfo:
          description: Информацию о начислении/списании бонусов
          $ref: './partial.yaml#/components/schemas/BonusesInfoDTO'
        boutiqueAddress:
          type: string
        effectiveBonusesInfo:
          description: Информацию об итоговом начислении/списании бонусов
          $ref: './partial.yaml#/components/schemas/BonusesInfoDTO'
        buyerCounterparty:
          $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
        clearAmount:
          type: number
        comment:
          type: string
        conciergeClientChannel:
          $ref: './partial.yaml#/components/schemas/ConciergeClientChannelDto'
        confirmedAmount:
          type: number
        count:
          type: integer
          format: int32
        counterpartyRequestType:
          type: string
          enum:
            - ORDER_CONFIRMATION
            - AGENT_REPORT_CONFIRMATION
            - ORDER_AND_AGENT_REPORT_CONFIRMATION
        deletable:
          type: boolean
        deliveryAddressEndpoint:
          $ref: './partial.yaml#/components/schemas/AddressEndpointDTO'
        deliveryAddressEndpointAggregation:
          $ref: './partial.yaml#/components/schemas/AddressEndpointAggregationDTO'
        deliveryComment:
          type: string
        deliveryCost:
          type: number
        deliveryDescription:
          type: string
        deliveryIcon:
          type: string
        deliveryInfo:
          type: string
        deliveryTitle:
          type: string
        deliveryToBuyerDateHint:
          type: string
          format: date-time
        discount:
          $ref: './partial.yaml#/components/schemas/Discount'
        duties:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/DutyDTO'
        dutiesAmount:
          type: number
        empty:
          type: boolean
        faulty:
          type: boolean
        finalAmount:
          type: number
        finalAmountWithoutDeliveryCost:
          type: number
        id:
          type: integer
          format: int64
        isMadeByNewUser:
          type: boolean
        isEffectiveBuyer:
          type: boolean
        items:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/OrderPositionDTO'
        linkedNotification:
          $ref: './partial.yaml#/components/schemas/NotificationDTO'
        orderCreationProblems:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/OrderCreationProblemDTO'
        orderSource:
          type: string
          enum:
            - ONLINE
            - BOUTIQUE
        orderSourceInfo:
          $ref: './partial.yaml#/components/schemas/OrderSourceInfoDTO'
        orderStateIcon:
          type: string
        orderStateSuccess:
          type: boolean
        orderStateTitle:
          type: string
        orderStatus:
          type: string
          enum:
            - UNDEFINED
            - UNCOMPLETED
            - ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS
            - ORDER_CONFIRMING
            - ORDER_REFUND
            - ORDER_CONFIRMED
            - CONCIERGE_ITEMS_WAITING_CONFIRMATION
            - SELLER_IN_MOSCOW
            - EXPECTING_COURIER_TO_SELLER
            - OURSELVES_PICKING_UP_FROM_SELLER
            - OURSELVES_FROM_SELLER_TO_OFFICE
            - LOGIST_ON_WAY_TO_SELLER
            - FROM_SELLER_TO_OFFICE
            - HAS_CONCIERGE_ITEMS
            - EXPERTISE_START
            - EXPERTISE_COMPLETED
            - CHOOSING_DELIVERY_METHOD_O2B
            - HOLD_COMPLETE_REJECTED
            - EXPECTING_COURIER_TO_BUYER
            - LOGIST_ON_WAY_TO_BUYER
            - BUYER_IN_MOSCOW
            - OURSELVES_DELIVERY_TO_BUYER
            - OURSELVES_FROM_OFFICE_TO_BUYER
            - ORDER_DELIVERED
            - HAS_DISPUTE
            - ORDER_IN_BOUTIQUE
            - ORDER_SOLD_IN_BOUTIQUE
            - EXPECTING_CONFIRM_AGENT_REPORT
            - WAIT_PAYMENT_MONEY_TO_SELLER
            - ORDER_COMPLETED
            - ORDER_COMPLETED_RETURN
            - RETURN_CREATED
            - RETURN_ON_WAY_TO_OFFICE
            - RETURN_EXPERTISE
            - RETURN_COMPLETED
            - BOUTIQUE_ORDER_ON_WAY_TO_OFFICE
            - BOUTIQUE_ORDER_ON_EXPERTISE
            - BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE
            - BOUTIQUE_ORDER_IN_BOUTIQUE
            - BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE
            - BOUTIQUE_ORDER_ONLINE_CONFIRM
            - BOUTIQUE_ORDER_ONLINE_PICKUP
        orderStatusTitle:
          type: string
        orderStepChain:
          $ref: './partial.yaml#/components/schemas/OrderStepChain'
        orderTrack:
          $ref: './partial.yaml#/components/schemas/OrderTrackDTO'
        ourselvesDeliveries:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/OurselvesDeliveryDTO'
        ourselvesDelivery:
          type: boolean
        ourselvesDeliveryFromSeller:
          $ref: './partial.yaml#/components/schemas/OurselvesDeliveryDTO'
        ourselvesDeliveryName:
          type: string
        ourselvesDeliveryPhone:
          type: string
        ourselvesDeliveryToBuyer:
          $ref: './partial.yaml#/components/schemas/OurselvesDeliveryDTO'
        pickupAddressEndpoint:
          $ref: './partial.yaml#/components/schemas/AddressEndpointDTO'
        pickupAddressEndpointAggregation:
          $ref: './partial.yaml#/components/schemas/AddressEndpointAggregationDTO'
        pickupComment:
          type: string
        pickupCountry:
          $ref: './partial.yaml#/components/schemas/CountryDTO'
        pickupDateFromSeller:
          type: integer
          format: int64
        pickupDateToBuyer:
          type: integer
          format: int64
        pickupIntervalFromSeller:
          type: string
        pickupTimeIntervalId:
          type: integer
          format: int64
        productLocation:
          type: string
          enum:
            - SELLER
            - BOUTIQUE
        rrpSum:
          type: number
        seller:
          $ref: './partial.yaml#/components/schemas/UserDTO'
        tabbySplit:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        sellerConciergeOrder:
          type: boolean
        counterpartyRequiredOnConfirmation:
          type: boolean
        counterpartyModificationAllowed:
          type: boolean
        sellerCounterparty:
          $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
        sellerReceivesAmount:
          type: number
        size:
          type: integer
          format: int32
        soldTime:
          type: string
          format: date-time
        state:
          type: string
          enum:
            - CREATED
            - CANCELED
            - HOLD_PROCESSING
            - HOLD_ERROR
            - HOLD
            - HOLD_COMPLETED
            - HOLD_COMPLETE_REJECTED
            - REFUND
            - MONEY_TRANSFERRED
            - MONEY_PAYMENT_ERROR
            - MONEY_PAYMENT_NOT_ENOUGH
            - MONEY_PAYMENT_TECHNICAL_ERROR
            - MONEY_PAYMENT_WAIT
            - SELLER_PAID
            - COMPLETED
            - RETURN
            - DELETED
        stateTime:
          type: integer
          format: int64
        trackingUrl:
          type: string
        vatIncluded:
          type: boolean
        waybillFromSeller:
          $ref: './partial.yaml#/components/schemas/WaybillDTO'
        waybillId:
          type: string
        waybillToBuyer:
          $ref: './partial.yaml#/components/schemas/WaybillDTO'
        waybills:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/WaybillDTO'
      title: OrderDTO

    ApiV2ResponseOfOrderFilters:
      title: ApiV2ResponseOfOrderFilters
      type: object
      properties:
        data:
          $ref: '#/components/schemas/OrderFilters'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ApiV2ResponseOfOrderFilter:
      title: ApiV2ResponseOfOrderFilter
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductFilter'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ApiV2ResponseOfItemsCount:
      title: ApiV2ResponseOfItemsCount
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ItemsCount'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      x-examples: {}
    ItemsCount:
      title: ItemsCount
      x-stoplight:
        id: 24rvwnfgy7nbf
      type: object
      properties:
        itemsCount:
          type: integer
      description: Количество заказов
    FilterDescription:
      title: FilterDescription
      x-stoplight:
        id: 9jem6h2at5zsu
      type: object
      properties:
        name:
          type: string
        description:
          type: string