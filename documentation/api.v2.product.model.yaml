openapi: 3.0.3
info:
  title: Oskelly Admin API v2, product model
  description: Oskelly Admin API v2, product model
  version: "1.0"
paths:
  /api/v2/product/model:
    get:
      tags:
        - productModel
        - get
        - byBrandId
      summary: Get primary model by brand id (if brandId is null, return all models)
      operationId: getAllProducModelByBrandId
      parameters:
        - in: query
          name: brandId
          schema:
            type: integer
            format: int64
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
components:
  schemas:
    ApiResponse:
      title: ApiResponse
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductModelDTO'

    ProductModelDTO:
      title: ProductModelDTO
      type: object
      required:
        - id
        - name
        - brandId
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        brandId:
          type: integer
          format: int64

