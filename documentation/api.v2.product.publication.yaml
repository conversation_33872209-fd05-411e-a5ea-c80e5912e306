openapi: 3.0.3
info:
  title: Oskelly API
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
paths:
  /api/v2/productpublication/conversion-to-base:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getConversionToBaseCurrency
      operationId: getConversionToBaseCurrencyUsingGET
      parameters:
        - name: currencyId
          in: query
          description: currencyId
          required: true
          type: integer
          format: int64
        - name: customCommissionValue
          in: query
          description: customCommissionValue
          required: true
          type: number
        - name: isCustomCommission
          in: query
          description: isCustomCommission
          required: true
          type: boolean
        - name: priceInCurrency
          in: query
          description: priceInCurrency
          required: true
          type: number
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfProductPriceDTO'

components:
  schemas:
    Api2ResponseOfProductPriceDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductPriceDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ProductPriceDTO:
      type: object
      properties:
        commission:
          type: number
        dutiesAmount:
          type: number
        explanation:
          type: string
        priceWithCommission:
          type: number
        priceWithoutCommission:
          type: number