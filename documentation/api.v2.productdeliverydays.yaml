{"openapi": "3.0.1", "info": {"title": "Oskelly Main Service", "description": "API Documentation", "version": "2.2.1"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "security": [{"cookieAuth": []}], "tags": [{"name": "Срок доставки товара покупателю", "description": "Получение срока доставки товара покупателю"}], "paths": {"/api/v2/productdeliverydays": {"get": {"tags": ["Срок доставки товара покупателю"], "summary": "Получение срока доставки товара покупателю", "description": "Поддерживается для авторизованного пользователя и для пользователя с guest token", "operationId": "getProductDeliveryDays", "parameters": [{"name": "productId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseProductDeliveryDaysDTO"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}}, "components": {"schemas": {"Api2Response": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "object"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ProductDeliveryDaysDTO": {"type": "object", "properties": {"minDeliveryDays": {"type": "integer", "format": "int32"}, "maxDeliveryDays": {"type": "integer", "format": "int32"}}}, "Api2ResponseProductDeliveryDaysDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductDeliveryDaysDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Введите куки в формате: JSESSIONID=your_session_id", "name": "<PERSON><PERSON>", "in": "header"}}}}