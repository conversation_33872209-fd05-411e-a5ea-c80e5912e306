swagger: '2.0'
info:
  version: 2.2.1
  title: Oskelly API
  description: Oskelly API
host: purchase.oskelly.tech
basePath: /
tags:
  - name: admin-panel-product-request-controller-api-v-2
    description: Admin Panel Product Request Controller Api V 2
  - name: admin-user-controller-api-v-2
    description: Admin User Controller Api V 2
  - name: product-publication-controller-api-v-2
    description: Product Publication Controller Api V 2
  - name: product-request-comment-controller
    description: Product Request Comment Controller
  - name: product-request-controller-api-v-2
    description: Product Request Controller Api V 2
  - name: product-request-publish-controller-api-v-2
    description: Product Request Publish Controller Api V 2
  - name: product-requests-filter-controller-api-v-2
    description: Product Requests Filter Controller Api V 2
  - name: product-response-controller-api-v-2
    description: Product Response Controller Api V 2
paths:
  /api/v2/admin/productRequest/list:
    post:
      tags:
        - admin-panel-product-request-controller-api-v-2
      summary: getProductRequestTotal
      operationId: getProductRequestTotalUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: filterContentDto
          description: filterContentDto
          required: true
          schema:
            $ref: '#/definitions/FilterContentDto'
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/productRequest/total:
    post:
      tags:
        - admin-panel-product-request-controller-api-v-2
      summary: getProductRequestTotal
      operationId: getProductRequestTotalUsingPOST_1
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: filterContentDto
          description: filterContentDto
          required: true
          schema:
            $ref: '#/definitions/FilterContentDto'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOflong'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/users:
    get:
      tags:
        - admin-user-controller-api-v-2
      summary: Get users list with filters
      operationId: getUsersUsingGET
      produces:
        - '*/*'
      parameters:
        - name: page
          in: query
          description: Номер страницы
          required: false
          type: integer
          format: int32
        - name: rowsPerPage
          in: query
          description: Размер страницы
          required: false
          type: integer
          format: int32
        - name: searchQuery
          in: query
          description: searchQuery
          required: true
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfUserDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/users/list:
    get:
      tags:
        - admin-user-controller-api-v-2
      summary: userList
      operationId: userListUsingGET
      produces:
        - '*/*'
      parameters:
        - name: ids
          in: query
          description: ids
          required: true
          type: array
          items:
            type: string
          collectionFormat: multi
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfUserDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequest/comments:
    post:
      tags:
        - product-request-comment-controller
      summary: publishComment
      operationId: publishCommentUsingPOST_1
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: parentCommentId
          in: query
          required: false
          type: integer
          format: int64
        - name: productId
          in: query
          required: false
          type: integer
          format: int64
        - name: productRequestId
          in: query
          required: false
          type: integer
          format: int64
        - name: text
          in: query
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/CommentDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequest/comments/base64-images:
    post:
      tags:
        - product-request-comment-controller
      summary: publishCommentWithEncodedImages
      operationId: publishCommentWithEncodedImagesUsingPOST_1
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: data
          description: data
          required: true
          schema:
            $ref: '#/definitions/ProductCommentBase64ImagesData'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/CommentDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequest/comments/{id}:
    delete:
      tags:
        - product-request-comment-controller
      summary: deleteComment
      operationId: deleteCommentUsingDELETE_1
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: commentId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            type: integer
            format: int64
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
    get:
      tags:
        - product-request-comment-controller
      summary: getProductComments
      operationId: getProductCommentsUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: id
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/CommentDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequest/comments/{productRequestId}/tree:
    get:
      tags:
        - product-request-comment-controller
      summary: getProductCommentsTree
      operationId: getProductCommentsTreeUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            type: array
            items:
              $ref: '#/definitions/CommentDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/dislike/{productRequestId}:
    put:
      tags:
        - product-request-controller-api-v-2
      summary: dislike
      operationId: dislikeUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfLike'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/filter:
    post:
      tags:
        - product-requests-filter-controller-api-v-2
      summary: getAvailableFilters
      operationId: getAvailableFiltersUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: productFilterRequest
          description: productFilterRequest
          required: true
          schema:
            $ref: '#/definitions/ProductFilterRequest'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestFilters'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/filter/count:
    post:
      tags:
        - product-requests-filter-controller-api-v-2
      summary: getItemsCount
      operationId: getItemsCountUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: productFilterInfoRequest
          description: productFilterInfoRequest
          required: true
          schema:
            $ref: '#/definitions/ProductFilterInfoRequest'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfItemsCount'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/filter/info:
    post:
      tags:
        - product-requests-filter-controller-api-v-2
      summary: getAvailableFilterInfo
      operationId: getAvailableFilterInfoUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: code
          in: query
          description: code
          required: true
          type: string
        - in: body
          name: productFilterInfoRequest
          description: productFilterInfoRequest
          required: true
          schema:
            $ref: '#/definitions/ProductFilterInfoRequest'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductFilter'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/filter/items:
    post:
      tags:
        - product-requests-filter-controller-api-v-2
      summary: getItems
      operationId: getItemsUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: productFilterItemsRequest
          description: productFilterItemsRequest
          required: true
          schema:
            $ref: '#/definitions/ProductFilterItemsRequest'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/like/{productRequestId}:
    put:
      tags:
        - product-request-controller-api-v-2
      summary: like
      operationId: likeUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfLike'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/liked-page:
    get:
      tags:
        - product-request-controller-api-v-2
      summary: getLikedProductRequestsPage
      operationId: getLikedProductRequestsPageUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          default: 0
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          default: 10
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/liked-page/{userId}:
    get:
      tags:
        - product-request-controller-api-v-2
      summary: getLikedProductRequestsPage
      operationId: getLikedProductRequestsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          default: 0
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          default: 10
          format: int64
        - name: userId
          in: path
          description: userId
          required: true
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/attributes/{categoryId}:
    get:
      tags:
        - product-request-publish-controller-api-v-2
      summary: getAttributes
      operationId: getAttributesUsingGET_2
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfAttributeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/publishAndGetResult:
    post:
      tags:
        - product-request-publish-controller-api-v-2
      summary: publishAndGet
      operationId: publishAndGetUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: productRequestUpdateDto
          description: productRequestUpdateDto
          required: true
          schema:
            $ref: '#/definitions/ProductRequestUpdateDTO'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/request/getByStates:
    get:
      tags:
        - product-request-publish-controller-api-v-2
      summary: getByStates
      operationId: getByStatesUsingGET
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: states
          in: query
          description: states
          required: false
          type: array
          items:
            type: string
            enum:
              - DRAFT
              - PUBLISHED
              - HIDDEN
              - MODERATION
              - REJECTED
          collectionFormat: multi
          enum:
            - DRAFT
            - PUBLISHED
            - HIDDEN
            - MODERATION
            - REJECTED
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/request/{productRequestId}:
    get:
      tags:
        - product-request-publish-controller-api-v-2
      summary: getById
      operationId: getByIdUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
    delete:
      tags:
        - product-request-publish-controller-api-v-2
      summary: deleteById
      operationId: deleteByIdUsingDELETE
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/productRequests/{productRequestId}:
    get:
      tags:
        - product-request-controller-api-v-2
      summary: getById
      operationId: getByIdUsingGET
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productResponses/find:
    get:
      tags:
        - product-response-controller-api-v-2
      summary: find
      operationId: findUsingGET
      produces:
        - '*/*'
      parameters:
        - name: notUserId
          in: query
          description: notUserId
          required: false
          type: integer
          format: int64
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          format: int64
        - name: productRequestId
          in: query
          description: productRequestId
          required: true
          type: integer
          format: int64
        - name: userId
          in: query
          description: userId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productResponses/listPublished:
    get:
      tags:
        - product-response-controller-api-v-2
      summary: listPublished
      operationId: listPublishedUsingGET
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productResponses/publishAndGetResult:
    post:
      tags:
        - product-response-controller-api-v-2
      summary: publishAndGet
      operationId: publishAndGetUsingPOST_1
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: dto
          description: dto
          required: true
          schema:
            $ref: '#/definitions/ProductResponseUpdateDTO'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductResponseDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/additional-sizes/{categoryId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: loadAdditionalSizesForCategory
      operationId: loadAdditionalSizesForCategoryUsingGET
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
        - name: sizeId
          in: query
          description: sizeId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfAdditionalSizeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/attributes/{categoryId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getAttributes
      operationId: getAttributesUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfAttributeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/brands:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getBrands
      operationId: getBrandsUsingGET_3
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/categories/{categoryId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getCategories
      operationId: getCategoriesUsingGET
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfCategoryDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/categoryTree:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getCategoryTree
      operationId: getCategoryTreeUsingGET_2
      produces:
        - '*/*'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfCategoryTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/commission:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getCommission
      operationId: getCommissionUsingGET
      produces:
        - '*/*'
      parameters:
        - name: price
          in: query
          description: price
          required: false
          type: number
        - name: salesChannel
          in: query
          description: salesChannel
          required: false
          type: string
          enum:
            - WEBSITE
            - BOUTIQUE_AND_WEBSITE
            - STOCK_AND_BOUTIQUE_AND_WEBSITE
            - BOUTIQUE
        - name: sellerId
          in: query
          description: sellerId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfdouble'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/conversion:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getConversion
      operationId: getConversionUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: priceWithCommission
          in: query
          description: priceWithCommission
          required: false
          type: number
        - name: priceWithoutCommission
          in: query
          description: priceWithoutCommission
          required: false
          type: number
        - name: salesChannel
          in: query
          description: salesChannel
          required: false
          type: string
          enum:
            - WEBSITE
            - BOUTIQUE_AND_WEBSITE
            - STOCK_AND_BOUTIQUE_AND_WEBSITE
            - BOUTIQUE
        - name: sellerId
          in: query
          description: sellerId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfConversion'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/conversion-to-base:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getConversionToBaseCurrency
      operationId: getConversionToBaseCurrencyUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyId
          in: query
          description: currencyId
          required: true
          type: integer
          format: int64
        - name: customCommissionValue
          in: query
          description: customCommissionValue
          required: true
          type: number
        - name: isCustomCommission
          in: query
          description: isCustomCommission
          required: true
          type: boolean
        - name: priceInCurrency
          in: query
          description: priceInCurrency
          required: true
          type: number
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductPriceDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/defectImageComment:
    patch:
      tags:
        - product-publication-controller-api-v-2
      summary: updateDefectImageComment
      operationId: updateDefectImageCommentUsingPATCH
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: comment
          in: query
          description: comment
          required: true
          type: string
        - name: imageOrder
          in: query
          description: imageOrder
          required: true
          type: integer
          format: int32
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductImageDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/productpublication/deleteDefectImage:
    post:
      tags:
        - product-publication-controller-api-v-2
      summary: deleteDefectImage
      operationId: deleteDefectImageUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: imageId
          in: query
          description: imageId
          required: true
          type: integer
          format: int64
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOflong'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/deleteProductImage:
    post:
      tags:
        - product-publication-controller-api-v-2
      summary: deleteProductImage
      operationId: deleteProductImageUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: imageId
          in: query
          description: imageId
          required: true
          type: integer
          format: int64
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOflong'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/drafts:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getDrafts
      operationId: getDraftsUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/draftsPage:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getDraftsPage
      operationId: getDraftsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          required: false
          type: integer
          format: int32
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/getPriceWithCommission:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getPriceWithCommission
      operationId: getPriceWithCommissionUsingGET
      produces:
        - '*/*'
      parameters:
        - name: priceWithoutCommission
          in: query
          description: priceWithoutCommission
          required: true
          type: number
        - name: salesChannel
          in: query
          description: salesChannel
          required: false
          type: string
          enum:
            - WEBSITE
            - BOUTIQUE_AND_WEBSITE
            - STOCK_AND_BOUTIQUE_AND_WEBSITE
            - BOUTIQUE
        - name: sellerId
          in: query
          description: sellerId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfbigdecimal'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/getPriceWithoutCommission:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getPriceWithoutCommission
      operationId: getPriceWithoutCommissionUsingGET
      produces:
        - '*/*'
      parameters:
        - name: priceWithCommission
          in: query
          description: priceWithCommission
          required: true
          type: number
        - name: salesChannel
          in: query
          description: salesChannel
          required: false
          type: string
          enum:
            - WEBSITE
            - BOUTIQUE_AND_WEBSITE
            - STOCK_AND_BOUTIQUE_AND_WEBSITE
            - BOUTIQUE
        - name: sellerId
          in: query
          description: sellerId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfbigdecimal'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/getPriceWithoutCommissionInBaseCurrency:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getPriceWithoutCommissionInBaseCurrency
      operationId: getPriceWithoutCommissionInBaseCurrencyUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyId
          in: query
          description: currencyId
          required: true
          type: integer
          format: int64
        - name: priceInCurrency
          in: query
          description: priceInCurrency
          required: true
          type: number
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfbigdecimal'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/moderationProducts:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getModeratingProducts
      operationId: getModeratingProductsUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/moderationProductsPage:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getModeratingProductsPage
      operationId: getModeratingProductsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          required: false
          type: integer
          format: int32
        - name: sortAttribute
          in: query
          description: sortAttribute
          required: false
          type: string
          enum:
            - ID
            - ID_DESC
            - PRICE
            - PRICE_DESC
            - PUBLISH_TIME_DESC
            - PROMOTION_TIME_DESC
            - PUBLISH_TIME
            - PRODUCT_STATE_TIME_DESC
            - PRODUCT_STATE_TIME
            - CHANGE_TIME_DESC
            - CHANGE_TIME
            - DISCOUNT_DESC
            - DISCOUNT
            - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/product-conditions:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProductConditions
      operationId: getProductConditionsUsingGET
      produces:
        - '*/*'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductConditionDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/product/{productId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProduct
      operationId: getProductUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: productId
          in: path
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
    delete:
      tags:
        - product-publication-controller-api-v-2
      summary: deleteProduct
      operationId: deleteProductUsingDELETE
      produces:
        - '*/*'
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOflong'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/productpublication/products:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProducts
      operationId: getProductsUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: productStates
          in: query
          description: productStates
          required: false
          type: array
          items:
            type: string
            enum:
              - DRAFT
              - SECOND_EDITION
              - NEED_MODERATION
              - NEED_RETOUCH
              - RETOUCH_DONE
              - REJECTED
              - PUBLISHED
              - HIDDEN
              - SOLD
              - DELETED
              - BANED
          collectionFormat: multi
          enum:
            - DRAFT
            - SECOND_EDITION
            - NEED_MODERATION
            - NEED_RETOUCH
            - RETOUCH_DONE
            - REJECTED
            - PUBLISHED
            - HIDDEN
            - SOLD
            - DELETED
            - BANED
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/productsCount:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProductsCount
      operationId: getProductsCountUsingGET
      produces:
        - '*/*'
      parameters:
        - name: productState
          in: query
          description: productState
          required: false
          type: string
          enum:
            - DRAFT
            - SECOND_EDITION
            - NEED_MODERATION
            - NEED_RETOUCH
            - RETOUCH_DONE
            - REJECTED
            - PUBLISHED
            - HIDDEN
            - SOLD
            - DELETED
            - BANED
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfint'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/productsCounts:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProductsCounts
      operationId: getProductsCountsUsingGET
      produces:
        - '*/*'
      parameters:
        - name: productStates
          in: query
          description: productStates
          required: false
          type: array
          items:
            type: string
            enum:
              - DRAFT
              - SECOND_EDITION
              - NEED_MODERATION
              - NEED_RETOUCH
              - RETOUCH_DONE
              - REJECTED
              - PUBLISHED
              - HIDDEN
              - SOLD
              - DELETED
              - BANED
          collectionFormat: multi
          enum:
            - DRAFT
            - SECOND_EDITION
            - NEED_MODERATION
            - NEED_RETOUCH
            - RETOUCH_DONE
            - REJECTED
            - PUBLISHED
            - HIDDEN
            - SOLD
            - DELETED
            - BANED
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfMapOfstringAndint'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/productsPage:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProductsPage
      operationId: getProductsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          required: false
          type: integer
          format: int32
        - name: productStates
          in: query
          description: productStates
          required: false
          type: array
          items:
            type: string
            enum:
              - DRAFT
              - SECOND_EDITION
              - NEED_MODERATION
              - NEED_RETOUCH
              - RETOUCH_DONE
              - REJECTED
              - PUBLISHED
              - HIDDEN
              - SOLD
              - DELETED
              - BANED
          collectionFormat: multi
          enum:
            - DRAFT
            - SECOND_EDITION
            - NEED_MODERATION
            - NEED_RETOUCH
            - RETOUCH_DONE
            - REJECTED
            - PUBLISHED
            - HIDDEN
            - SOLD
            - DELETED
            - BANED
        - name: sortAttribute
          in: query
          description: sortAttribute
          required: false
          type: string
          enum:
            - ID
            - ID_DESC
            - PRICE
            - PRICE_DESC
            - PUBLISH_TIME_DESC
            - PROMOTION_TIME_DESC
            - PUBLISH_TIME
            - PRODUCT_STATE_TIME_DESC
            - PRODUCT_STATE_TIME
            - CHANGE_TIME_DESC
            - CHANGE_TIME
            - DISCOUNT_DESC
            - DISCOUNT
            - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/publish:
    post:
      tags:
        - product-publication-controller-api-v-2
      summary: publish
      operationId: publishUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: request
          description: request
          required: true
          schema:
            $ref: '#/definitions/ProductDTO'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOflong'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/publishAndGetResult:
    post:
      tags:
        - product-publication-controller-api-v-2
      summary: publishAndGetResult
      operationId: publishAndGetResultUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: request
          description: request
          required: true
          schema:
            $ref: '#/definitions/ProductDTO'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/rejects:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getRejects
      operationId: getRejectsUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/rejectsPage:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getRejectsPage
      operationId: getRejectsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          required: false
          type: integer
          format: int32
        - name: sortAttribute
          in: query
          description: sortAttribute
          required: false
          type: string
          enum:
            - ID
            - ID_DESC
            - PRICE
            - PRICE_DESC
            - PUBLISH_TIME_DESC
            - PROMOTION_TIME_DESC
            - PUBLISH_TIME
            - PRODUCT_STATE_TIME_DESC
            - PRODUCT_STATE_TIME
            - CHANGE_TIME_DESC
            - CHANGE_TIME
            - DISCOUNT_DESC
            - DISCOUNT
            - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/samples/{categoryId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getProductPhotoSamples
      operationId: getProductPhotoSamplesUsingGET
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductImageDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/secondEditionProducts:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getSecondEditionProducts
      operationId: getSecondEditionProductsUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/secondEditionProductsPage:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getSecondEditionProductsPage
      operationId: getSecondEditionProductsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: currencyCode
          in: query
          description: currencyCode
          required: false
          type: string
        - name: page
          in: query
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          required: false
          type: integer
          format: int32
        - name: sortAttribute
          in: query
          description: sortAttribute
          required: false
          type: string
          enum:
            - ID
            - ID_DESC
            - PRICE
            - PRICE_DESC
            - PUBLISH_TIME_DESC
            - PROMOTION_TIME_DESC
            - PUBLISH_TIME
            - PRODUCT_STATE_TIME_DESC
            - PRODUCT_STATE_TIME
            - CHANGE_TIME_DESC
            - CHANGE_TIME
            - DISCOUNT_DESC
            - DISCOUNT
            - IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/sizes/{categoryId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getSizes
      operationId: getSizesUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfSizeTypeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/step/{productId}:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getPriceWithoutCommission
      operationId: getPriceWithoutCommissionUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfStepInfo'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/steps:
    get:
      tags:
        - product-publication-controller-api-v-2
      summary: getPriceWithoutCommission
      operationId: getPriceWithoutCommissionUsingGET_2
      produces:
        - '*/*'
      parameters:
        - name: ids
          in: query
          description: ids
          required: true
          type: array
          items:
            type: integer
            format: int64
          collectionFormat: multi
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfStepInfo'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/uploadDefectImage:
    post:
      tags:
        - product-publication-controller-api-v-2
      summary: uploadDefectImage
      operationId: uploadDefectImageUsingPOST
      consumes:
        - multipart/form-data
      produces:
        - '*/*'
      parameters:
        - name: comment
          in: query
          description: comment
          required: true
          type: string
        - name: image
          in: formData
          description: image
          required: false
          type: file
        - name: imageOrder
          in: query
          description: imageOrder
          required: true
          type: integer
          format: int32
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductImageDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productpublication/uploadProductImage:
    post:
      tags:
        - product-publication-controller-api-v-2
      summary: uploadProductImage
      operationId: uploadProductImageUsingPOST
      consumes:
        - multipart/form-data
      produces:
        - '*/*'
      parameters:
        - name: image
          in: formData
          description: image
          required: false
          type: file
        - name: imageOrder
          in: query
          description: imageOrder
          required: true
          type: integer
          format: int32
        - name: productId
          in: query
          description: productId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductImageDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/catalog/productmodel:
    get:
      tags:
        - catalog-controller-api-v-2
      summary: getAllByBrandId
      operationId: getAllByBrandIdUsingGET
      produces:
        - '*/*'
      parameters:
        - name: brandId
          in: query
          description: brandId
          required: false
          type: integer
          format: int64
        - name: brandIds
          in: query
          description: brandIds
          required: false
          type: array
          items:
            type: integer
            format: int64
          collectionFormat: multi
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfProductModelDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/image/upload:
    post:
      tags:
        - image-controller-api-v-2
      summary: uploadImagesToCloud
      operationId: uploadImagesToCloudUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: context
          in: query
          description: context
          required: true
          type: string
          enum:
            - CONCIERGE
            - PRODUCT_REQUEST
            - PRODUCT
            - COMMENT
            - DEFECT
        - name: image
          in: formData
          description: image
          required: true
          type: array
          items:
            type: string
          collectionFormat: multi
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfUploadedImageDto'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
definitions:
  AdditionalSizeDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      image:
        type: string
      isRequired:
        type: boolean
      name:
        type: string
      transliterateName:
        type: string
      value:
        type: integer
        format: int32
    title: AdditionalSizeDTO
  AddressAggregationDTO:
    type: object
    properties:
      address:
        type: string
      address2:
        type: string
      address3:
        type: string
      changeTime:
        type: string
        format: date-time
      cityData:
        $ref: '#/definitions/CityDTO'
      countryData:
        $ref: '#/definitions/CountryDTO'
      createTime:
        type: string
        format: date-time
      id:
        type: integer
        format: int64
      zipCode:
        type: string
    title: AddressAggregationDTO
  AddressAggregationEndpointDTO:
    type: object
    properties:
      address:
        $ref: '#/definitions/AddressAggregationDTO'
      firstName:
        type: string
      id:
        type: integer
        format: int64
      lastName:
        type: string
      patronymicName:
        type: string
      phone:
        type: string
    title: AddressAggregationEndpointDTO
  AddressDTO:
    type: object
    properties:
      address:
        type: string
      address2:
        type: string
      address3:
        type: string
      changeTime:
        type: integer
        format: int64
      city:
        type: string
      cityData:
        $ref: '#/definitions/CityDTO'
      cityFiasId:
        type: string
      country:
        type: string
      countryData:
        $ref: '#/definitions/CountryDTO'
      createTime:
        type: integer
        format: int64
      fullCityName:
        type: string
      id:
        type: integer
        format: int64
      region:
        type: string
      settlementFiasId:
        type: string
      zipCode:
        type: string
    title: AddressDTO
  AddressEndpointAggregationDTO:
    type: object
    properties:
      billingAddress:
        $ref: '#/definitions/AddressAggregationEndpointDTO'
      id:
        type: integer
        format: int64
      physicalAddress:
        $ref: '#/definitions/AddressAggregationEndpointDTO'
      usePhysicalAddressForBilling:
        type: boolean
    title: AddressEndpointAggregationDTO
  AddressEndpointDTO:
    type: object
    properties:
      address:
        $ref: '#/definitions/AddressDTO'
      firstName:
        type: string
      id:
        type: integer
        format: int64
      lastName:
        type: string
      patronymicName:
        type: string
      phone:
        type: string
    title: AddressEndpointDTO
  AgentReportDTO:
    type: object
    properties:
      baseAmount:
        type: number
      bik:
        type: string
      cardBrand:
        type: string
      cardHolder:
        type: string
      cardNumber:
        type: string
      cardRefId:
        type: string
      cleaningAmount:
        type: number
      createTime:
        type: integer
        format: int64
      dateContract:
        type: integer
        format: int64
      defectsDiscountAmount:
        type: number
      firstName:
        type: string
      iban:
        type: string
      id:
        type: integer
        format: int64
      inn:
        type: string
      isCard:
        type: boolean
      isConfirmed:
        type: boolean
      kpp:
        type: string
      name:
        type: string
      numberContract:
        type: string
      order:
        $ref: '#/definitions/OrderDTO'
      patronymic:
        type: string
      paymentAccount:
        type: string
      paymentAmount:
        type: number
      paymentDetails:
        type: string
      routingNumber:
        type: string
      secondName:
        type: string
      swiftCode:
        type: string
      userType:
        type: string
        enum:
          - SIMPLE_USER
          - IP
          - OOO
    title: AgentReportDTO
  Api2ResponseOfCategoryTree:
    type: object
    properties:
      data:
        $ref: '#/definitions/CategoryTree'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfCategoryTree
  Api2ResponseOfConversion:
    type: object
    properties:
      data:
        $ref: '#/definitions/Conversion'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfConversion
  Api2ResponseOfItemsCount:
    type: object
    properties:
      data:
        $ref: '#/definitions/ItemsCount'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfItemsCount
  Api2ResponseOfLike:
    type: object
    properties:
      data:
        $ref: '#/definitions/Like'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfLike
  Api2ResponseOfListOfAdditionalSizeDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/AdditionalSizeDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfAdditionalSizeDTO
  Api2ResponseOfListOfAttributeDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/AttributeDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfAttributeDTO
  Api2ResponseOfListOfBrandDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/BrandDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfBrandDTO
  Api2ResponseOfListOfCategoryDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/CategoryDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfCategoryDTO
  Api2ResponseOfListOfProductConditionDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/ProductConditionDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfProductConditionDTO
  Api2ResponseOfListOfProductDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/ProductDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfProductDTO
  Api2ResponseOfListOfProductImageDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfProductImageDTO
  Api2ResponseOfListOfSizeTypeDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/SizeTypeDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfSizeTypeDTO
  Api2ResponseOfListOfStepInfo:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/StepInfo'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfStepInfo
  Api2ResponseOfListOfUserDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/UserDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfUserDTO
  Api2ResponseOfMapOfstringAndint:
    type: object
    properties:
      data:
        type: object
        additionalProperties:
          type: integer
          format: int32
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfMapOfstringAndint
  Api2ResponseOfPageOfProductDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/PageOfProductDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfPageOfProductDTO
  Api2ResponseOfPageOfProductRequestDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/PageOfProductRequestDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfPageOfProductRequestDTO
  Api2ResponseOfPageOfProductResponseDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/PageOfProductResponseDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfPageOfProductResponseDTO
  Api2ResponseOfPageOfUserDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/PageOfUserDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfPageOfUserDTO
  Api2ResponseOfProductDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductDTO
  Api2ResponseOfProductFilter:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductFilter'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductFilter
  Api2ResponseOfProductImageDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductImageDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductImageDTO
  Api2ResponseOfProductPriceDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductPriceDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductPriceDTO
  Api2ResponseOfProductRequestDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductRequestDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductRequestDTO
  Api2ResponseOfProductRequestFilters:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductRequestFilters'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductRequestFilters
  Api2ResponseOfProductResponseDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductResponseDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductResponseDTO
  Api2ResponseOfStepInfo:
    type: object
    properties:
      data:
        $ref: '#/definitions/StepInfo'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfStepInfo
  Api2ResponseOfbigdecimal:
    type: object
    properties:
      data:
        type: number
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfbigdecimal
  Api2ResponseOfdouble:
    type: object
    properties:
      data:
        type: number
        format: double
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfdouble
  Api2ResponseOfint:
    type: object
    properties:
      data:
        type: integer
        format: int32
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfint
  Api2ResponseOflong:
    type: object
    properties:
      data:
        type: integer
        format: int64
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOflong
  AttributeDTO:
    type: object
    properties:
      attributeValues:
        type: array
        items:
          $ref: '#/definitions/AttributeValueDTO'
      id:
        type: integer
        format: int64
      isRequired:
        type: boolean
      kind:
        type: string
        enum:
          - GENERIC
          - MATERIAL
          - COLOR
      name:
        type: string
      showFilter:
        type: boolean
    title: AttributeDTO
  AttributeValueDTO:
    type: object
    properties:
      icon:
        type: string
      id:
        type: integer
        format: int64
      ofValue:
        type: string
      pluralGenitiveValue:
        type: string
      singularGenitiveValue:
        type: string
      transliterateValue:
        type: string
      value:
        type: string
    title: AttributeValueDTO
  AttributeWithValueDTO:
    type: object
    properties:
      attribute:
        $ref: '#/definitions/AttributeDTO'
      attributeValue:
        $ref: '#/definitions/AttributeValueDTO'
    title: AttributeWithValueDTO
  BrandDTO:
    type: object
    properties:
      brandDescriptions:
        type: array
        items:
          $ref: '#/definitions/BrandDescriptionDto'
      description:
        type: string
      hiddenDescription:
        type: string
      id:
        type: integer
        format: int64
      isHidden:
        type: boolean
      isLiked:
        type: boolean
      name:
        type: string
      products:
        type: array
        items:
          $ref: '#/definitions/ProductDTO'
      productsCount:
        type: integer
        format: int32
      title:
        type: string
      transliterateName:
        type: string
      urlName:
        type: string
    title: BrandDTO
  BrandDescriptionDto:
    type: object
    properties:
      category:
        $ref: '#/definitions/CategoryDisplayNameDTO'
      description:
        type: string
    title: BrandDescriptionDto
  CategoryDTO:
    type: object
    properties:
      additionalSizes:
        type: array
        items:
          $ref: '#/definitions/AdditionalSizeDTO'
      attributes:
        type: array
        items:
          $ref: '#/definitions/AttributeDTO'
      children:
        type: array
        items:
          $ref: '#/definitions/CategoryDTO'
      defaultSizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      displayName:
        type: string
      fullName:
        type: string
      hasChildren:
        type: boolean
      icon:
        type: string
      id:
        type: integer
        format: int64
      minPrice:
        type: integer
        format: int32
      pluralName:
        type: string
      productsCount:
        type: integer
        format: int32
      singularFullName:
        type: string
      singularName:
        type: string
      sizeValues:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      url:
        type: string
    title: CategoryDTO
  CategoryDisplayNameDTO:
    type: object
    properties:
      displayName:
        type: string
      id:
        type: integer
        format: int64
    title: CategoryDisplayNameDTO
  CategoryTree:
    type: object
    properties:
      rootCategory:
        $ref: '#/definitions/CategoryDTO'
    title: CategoryTree
  Charset:
    type: object
    properties:
      registered:
        type: boolean
    title: Charset
  CityDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
      region:
        type: string
    title: CityDTO
  CommentDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          type: string
      needsTranslate:
        type: boolean
      parentCommentId:
        type: integer
        format: int64
      productId:
        type: integer
        format: int64
      productRequestId:
        type: integer
        format: int64
      publishedAtTime:
        type: integer
        format: int64
      publisher:
        $ref: '#/definitions/UserDTO'
      replyTo:
        type: string
      subComments:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      text:
        type: string
      deletedAtTime:
        type: string
      editedAtTime:
        type: string
    title: CommentDTO
  CommentView:
    type: object
    properties:
      avatar:
        type: string
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          type: string
      isAnswer:
        type: boolean
      publishTime:
        type: string
      publishZonedDateTime:
        type: integer
        format: int64
      text:
        type: string
      user:
        type: string
      userId:
        type: integer
        format: int64
    title: CommentView
  ConciergeClientChannelDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
      orderNumber:
        type: integer
        format: int32
    title: ConciergeClientChannelDto
  Conversion:
    type: object
    properties:
      commission:
        type: number
        format: double
      commissionScaled:
        type: number
      dutiesAmount:
        type: number
      explanation:
        type: string
      priceWithCommission:
        type: number
      priceWithoutCommission:
        type: number
      fixedAmount:
        type: number
    title: Conversion
  CountryDTO:
    type: object
    properties:
      countryCounterpartyType:
        type: string
        enum:
          - UAE_COUNTERPARTY
          - DEFAULT_COUNTERPARTY
      currency:
        $ref: '#/definitions/CurrencyDTO'
      id:
        type: integer
        format: int64
      imageUrl:
        type: string
      isoCodeAlpha2:
        type: string
      name:
        type: string
      requireZipcode:
        type: boolean
    title: CountryDTO
  CurrencyDTO:
    type: object
    properties:
      active:
        type: boolean
      base:
        type: boolean
      id:
        type: integer
        format: int64
      isoCode:
        type: string
      isoNumber:
        type: integer
        format: int32
      name:
        type: string
      selectedByDefault:
        type: boolean
      sign:
        type: string
    title: CurrencyDTO
  DescriptionAttributeView:
    type: object
    properties:
      attributeValueId:
        type: integer
        format: int64
      title:
        type: string
      value:
        type: string
    title: DescriptionAttributeView
  Discount:
    type: object
    properties:
      baseAmount:
        type: integer
        format: int64
      code:
        type: string
      discountProc:
        type: number
      discountValue:
        type: number
      isValidYet:
        type: boolean
      optionalText:
        type: string
      resultAmount:
        type: integer
        format: int64
      resultAmountWithDeliveryCost:
        type: integer
        format: int64
      savingsValue:
        type: integer
        format: int64
      type:
        type: string
    title: Discount
  DutyDTO:
    type: object
    properties:
      amount:
        type: number
      description:
        type: string
      oskellyCommission:
        type: boolean
      sequence:
        type: integer
        format: int32
    title: DutyDTO
  ExpertiseDTO:
    type: object
    properties:
      cleaningPrice:
        type: number
      createTime:
        type: integer
        format: int64
      defectComment:
        type: string
      defectDiscount:
        type: number
      defectDiscountPrice:
        type: number
      id:
        type: integer
        format: int64
      isApproved:
        type: boolean
      pickupFrom:
        type: string
        enum:
          - OFFICE
          - SELLER
          - BUYER
      rejectionReason:
        type: string
    title: ExpertiseDTO
  FilterDescription:
    type: object
    properties:
      description:
        type: string
      name:
        type: string
    title: FilterDescription
  ItemsCount:
    type: object
    properties:
      itemsCount:
        type: integer
        format: int64
    title: ItemsCount
  JsonNode:
    type: object
    properties:
      array:
        type: boolean
      bigDecimal:
        type: boolean
      bigInteger:
        type: boolean
      binary:
        type: boolean
      boolean:
        type: boolean
      containerNode:
        type: boolean
      double:
        type: boolean
      float:
        type: boolean
      floatingPointNumber:
        type: boolean
      int:
        type: boolean
      integralNumber:
        type: boolean
      long:
        type: boolean
      missingNode:
        type: boolean
      nodeType:
        type: string
        enum:
          - ARRAY
          - BINARY
          - BOOLEAN
          - MISSING
          - 'NULL'
          - NUMBER
          - OBJECT
          - POJO
          - STRING
      'null':
        type: boolean
      number:
        type: boolean
      object:
        type: boolean
      pojo:
        type: boolean
      short:
        type: boolean
      textual:
        type: boolean
      valueNode:
        type: boolean
    title: JsonNode
  Like:
    type: object
    properties:
      count:
        type: integer
        format: int64
      isLiked:
        type: boolean
    title: Like
  MediaType:
    type: object
    properties:
      charset:
        $ref: '#/definitions/Charset'
      concrete:
        type: boolean
      parameters:
        type: object
        additionalProperties:
          type: string
      qualityValue:
        type: number
        format: double
      subtype:
        type: string
      type:
        type: string
      wildcardSubtype:
        type: boolean
      wildcardType:
        type: boolean
    title: MediaType
  MessageFormat:
    type: object
    properties:
      date:
        type: string
        format: date-time
      text:
        type: string
    title: MessageFormat
  Metadata:
    type: object
    properties:
      positiveCase:
        type: boolean
      priceDescription:
        type: string
      status:
        type: string
    title: Metadata
  ModificationMetadata:
    type: object
    properties:
      acceptMediaType:
        $ref: '#/definitions/MediaType'
      acceptMethod:
        type: string
        enum:
          - GET
          - HEAD
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
          - TRACE
      acceptParamFormat:
        type: string
      acceptParamName:
        type: string
      acceptUrl:
        type: string
      currentValue:
        type: object
      modifiable:
        type: boolean
      title:
        type: string
    title: ModificationMetadata
  NotificationDTO:
    type: object
    properties:
      actionCompleted:
        type: boolean
      actionCompletedTime:
        type: string
        format: date-time
      counter:
        type: integer
        format: int32
      createTime:
        type: string
        format: date-time
      deleted:
        type: boolean
      guestToken:
        type: string
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          type: string
      initiator:
        $ref: '#/definitions/UserDTO'
      mainIcon:
        type: string
      message:
        type: string
      messageFormat:
        $ref: '#/definitions/MessageFormat'
      metadata:
        type: object
      modificationMetadata:
        $ref: '#/definitions/ModificationMetadata'
      needAction:
        type: boolean
      read:
        type: boolean
      readTime:
        type: string
        format: date-time
      shortMessage:
        type: string
      subTitle:
        type: string
      targetObject:
        type: object
      targetObjectId:
        type: integer
        format: int64
      targetObjectImage:
        type: string
      targetObjectImageHint:
        type: string
      targetObjectType:
        type: string
      targetObjectUrl:
        type: string
      targetUser:
        $ref: '#/definitions/UserDTO'
      targetUserId:
        type: integer
        format: int64
      tinyIcon:
        type: string
      title:
        type: string
      type:
        type: string
    title: NotificationDTO
  OfferDTO:
    type: object
    properties:
      brandId:
        type: integer
        format: int64
      categoryId:
        type: integer
        format: int64
      consumed:
        type: boolean
      id:
        type: integer
        format: int64
      isSizeAvailable:
        type: boolean
      negotiatedPrice:
        type: number
      offerStatus:
        type: string
        enum:
          - PENDING
          - ACCEPTED
          - REJECTED
      offerorId:
        type: integer
        format: int64
      price:
        type: number
      product:
        $ref: '#/definitions/ProductDTO'
      productId:
        type: integer
        format: int64
      productState:
        type: string
        enum:
          - DRAFT
          - SECOND_EDITION
          - NEED_MODERATION
          - NEED_RETOUCH
          - RETOUCH_DONE
          - REJECTED
          - PUBLISHED
          - HIDDEN
          - SOLD
          - DELETED
          - BANED
      sizeId:
        type: integer
        format: int64
    title: OfferDTO
  OrderCreationProblemDTO:
    type: object
    properties:
      data:
        type: object
      type:
        type: string
        enum:
          - NOT_ENOUGH_AMOUNT
          - CANT_DELIVERY_ITEM_TO_FOREIGN_COUNTRY
      description:
        type: string
    title: OrderCreationProblemDTO
  OrderDTO:
    type: object
    properties:
      adminComment:
        type: string
      agentReport:
        $ref: '#/definitions/AgentReportDTO'
      agentReportConfirmed:
        type: boolean
      agentReportId:
        type: integer
        format: int64
      buyerCounterparty:
        $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
      clearAmount:
        type: number
      comment:
        type: string
      conciergeClientChannel:
        $ref: '#/definitions/ConciergeClientChannelDto'
      confirmedAmount:
        type: number
      count:
        type: integer
        format: int32
      counterpartyRequestType:
        type: string
        enum:
          - ORDER_CONFIRMATION
          - AGENT_REPORT_CONFIRMATION
          - ORDER_AND_AGENT_REPORT_CONFIRMATION
      deletable:
        type: boolean
      deliveryAddressEndpoint:
        $ref: '#/definitions/AddressEndpointDTO'
      deliveryAddressEndpointAggregation:
        $ref: '#/definitions/AddressEndpointAggregationDTO'
      deliveryComment:
        type: string
      deliveryCost:
        type: number
      deliveryDescription:
        type: string
      deliveryIcon:
        type: string
      deliveryInfo:
        type: string
      deliveryTitle:
        type: string
      deliveryToBuyerDateHint:
        type: string
        format: date-time
      discount:
        $ref: '#/definitions/Discount'
      duties:
        type: array
        items:
          $ref: '#/definitions/DutyDTO'
      dutiesAmount:
        type: number
      empty:
        type: boolean
      faulty:
        type: boolean
      finalAmount:
        type: number
      finalAmountWithoutDeliveryCost:
        type: number
      id:
        type: integer
        format: int64
      isMadeByNewUser:
        type: boolean
      items:
        type: array
        items:
          $ref: '#/definitions/OrderPositionDTO'
      linkedNotification:
        $ref: '#/definitions/NotificationDTO'
      orderCreationProblems:
        type: array
        items:
          $ref: '#/definitions/OrderCreationProblemDTO'
      orderSource:
        type: string
        enum:
          - ONLINE
          - BOUTIQUE
      orderStateIcon:
        type: string
      orderStateSuccess:
        type: boolean
      orderStateTitle:
        type: string
      orderStatus:
        type: string
        enum:
          - UNDEFINED
          - UNCOMPLETED
          - ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS
          - ORDER_CONFIRMING
          - ORDER_REFUND
          - ORDER_CONFIRMED
          - CONCIERGE_ITEMS_WAITING_CONFIRMATION
          - SELLER_IN_MOSCOW
          - EXPECTING_COURIER_TO_SELLER
          - OURSELVES_PICKING_UP_FROM_SELLER
          - OURSELVES_FROM_SELLER_TO_OFFICE
          - LOGIST_ON_WAY_TO_SELLER
          - FROM_SELLER_TO_OFFICE
          - HAS_CONCIERGE_ITEMS
          - EXPERTISE_START
          - EXPERTISE_COMPLETED
          - CHOOSING_DELIVERY_METHOD_O2B
          - HOLD_COMPLETE_REJECTED
          - EXPECTING_COURIER_TO_BUYER
          - LOGIST_ON_WAY_TO_BUYER
          - BUYER_IN_MOSCOW
          - OURSELVES_DELIVERY_TO_BUYER
          - OURSELVES_FROM_OFFICE_TO_BUYER
          - ORDER_DELIVERED
          - HAS_DISPUTE
          - ORDER_IN_BOUTIQUE
          - ORDER_SOLD_IN_BOUTIQUE
          - EXPECTING_CONFIRM_AGENT_REPORT
          - WAIT_PAYMENT_MONEY_TO_SELLER
          - ORDER_COMPLETED
          - ORDER_COMPLETED_RETURN
          - RETURN_CREATED
          - RETURN_ON_WAY_TO_OFFICE
          - RETURN_EXPERTISE
          - RETURN_COMPLETED
          - BOUTIQUE_ORDER_ON_WAY_TO_OFFICE
          - BOUTIQUE_ORDER_ON_EXPERTISE
          - BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE
          - BOUTIQUE_ORDER_IN_BOUTIQUE
          - BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE
          - BOUTIQUE_ORDER_ONLINE_CONFIRM
          - BOUTIQUE_ORDER_ONLINE_PICKUP
      orderStatusTitle:
        type: string
      orderStepChain:
        $ref: '#/definitions/OrderStepChain'
      ourselvesDeliveries:
        type: array
        items:
          $ref: '#/definitions/OurselvesDeliveryDTO'
      ourselvesDelivery:
        type: boolean
      ourselvesDeliveryFromSeller:
        $ref: '#/definitions/OurselvesDeliveryDTO'
      ourselvesDeliveryName:
        type: string
      ourselvesDeliveryPhone:
        type: string
      ourselvesDeliveryToBuyer:
        $ref: '#/definitions/OurselvesDeliveryDTO'
      pickupAddressEndpoint:
        $ref: '#/definitions/AddressEndpointDTO'
      pickupAddressEndpointAggregation:
        $ref: '#/definitions/AddressEndpointAggregationDTO'
      pickupComment:
        type: string
      pickupCountry:
        $ref: '#/definitions/CountryDTO'
      pickupDateFromSeller:
        type: integer
        format: int64
      pickupDateToBuyer:
        type: integer
        format: int64
      pickupIntervalFromSeller:
        type: string
      pickupTimeIntervalId:
        type: integer
        format: int64
      productLocation:
        type: string
        enum:
          - SELLER
          - BOUTIQUE
      rrpSum:
        type: number
      seller:
        $ref: '#/definitions/UserDTO'
      sellerCounterparty:
        $ref: './partial.yaml#/components/schemas/CounterpartyDTO'
      sellerReceivesAmount:
        type: number
      size:
        type: integer
        format: int32
      state:
        type: string
        enum:
          - CREATED
          - CANCELED
          - HOLD_PROCESSING
          - HOLD_ERROR
          - HOLD
          - HOLD_COMPLETED
          - HOLD_COMPLETE_REJECTED
          - REFUND
          - MONEY_TRANSFERRED
          - MONEY_PAYMENT_ERROR
          - MONEY_PAYMENT_NOT_ENOUGH
          - MONEY_PAYMENT_TECHNICAL_ERROR
          - MONEY_PAYMENT_WAIT
          - SELLER_PAID
          - COMPLETED
          - RETURN
          - DELETED
      stateTime:
        type: integer
        format: int64
      trackingUrl:
        type: string
      vatIncluded:
        type: boolean
      waybillFromSeller:
        $ref: '#/definitions/WaybillDTO'
      waybillId:
        type: string
      waybillToBuyer:
        $ref: '#/definitions/WaybillDTO'
      waybills:
        type: array
        items:
          $ref: '#/definitions/WaybillDTO'
    title: OrderDTO
  OrderPositionDTO:
    type: object
    properties:
      afterAcceptedOffer:
        type: boolean
      amount:
        type: number
      amountInForeignCurrency:
        type: number
      available:
        type: boolean
      availableSizes:
        type: array
        items:
          $ref: '#/definitions/Size'
      brandId:
        type: integer
        format: int64
      brandName:
        type: string
      buyerCommentsCount:
        type: integer
        format: int32
      buyerFollowsSeller:
        type: boolean
      categoryId:
        type: integer
        format: int64
      categoryName:
        type: string
      commission:
        type: number
      count:
        type: integer
        format: int32
      countryOfOrigin:
        $ref: '#/definitions/CountryDTO'
      countryOfOriginRequired:
        type: boolean
      datamatrix:
        type: string
      description:
        type: string
      discount:
        type: integer
        format: int32
      expertisePass:
        type: boolean
      expertises:
        type: array
        items:
          $ref: '#/definitions/ExpertiseDTO'
      finalAmount:
        type: number
      foreignCurrencyId:
        type: integer
        format: int64
      foreignCurrencyRate:
        type: number
      hasSimilar:
        type: boolean
      id:
        type: integer
        format: int64
      imageUrl:
        type: string
      isConfirmed:
        type: boolean
      isLiked:
        type: boolean
      isMoneyReturned:
        type: boolean
      isNeedDatamatrix:
        type: boolean
      metadata:
        $ref: '#/definitions/Metadata'
      productCondition:
        type: string
      productConditionId:
        type: integer
        format: int64
      productHasDiscount:
        type: boolean
      productId:
        type: integer
        format: int64
      productModel:
        $ref: '#/definitions/ProductModelDTO'
      productName:
        type: string
      promocodeAmount:
        type: number
      rrp:
        type: number
      sellerReceivesAmount:
        type: number
      size:
        $ref: '#/definitions/Size'
      sizes:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      state:
        type: string
        enum:
          - INITIAL
          - PURCHASE_REQUEST
          - SALE_CONFIRMED
          - SALE_REJECTED
          - PICKUP_DECLINED
          - HQ_WAREHOUSE
          - ON_VERIFICATION
          - VERIFICATION_OK
          - VERIFICATION_NEED_CLEANING
          - VERIFICATION_BAD_STATE
          - REJECTED_AFTER_VERIFICATION
          - READY_TO_SHIP
          - CREATE_WAYBILL_TO_BUYER
          - SHIPPED_TO_CLIENT
          - REQUESTED_TO_RETURN
          - RETURN_ACCEPTED
          - RETURN_DECLINED
          - RETURN_VERIFICATION_OK
          - RETURN_VERIFICATION_REJECTED
    title: OrderPositionDTO
  OrderStepChain:
    type: object
    properties:
      steps:
        type: array
        items:
          $ref: '#/definitions/OrderStepDTO'
    title: OrderStepChain
  OrderStepDTO:
    type: object
    properties:
      description:
        type: string
      descriptionItemIds:
        type: array
        items:
          type: integer
          format: int64
      descriptionTitle:
        type: string
      itemDescriptions:
        type: object
        additionalProperties:
          type: string
      message:
        type: string
      temporaryMessage:
        type: string
      time:
        type: integer
        format: int64
      title:
        type: string
      type:
        type: string
        enum:
          - DISABLED
          - WAITING
          - COMPLETE
          - FAILED
          - CONFIRMATION
          - EXPERTISE
          - DELIVERING
      typeIcon:
        type: string
      warning:
        type: string
      warningIcon:
        type: string
    title: OrderStepDTO
  OurselvesDeliveryDTO:
    type: object
    properties:
      courierName:
        type: string
      courierPhone:
        type: string
    title: OurselvesDeliveryDTO
  PageOfProductDTO:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/ProductDTO'
      itemsCount:
        type: integer
        format: int32
      totalAmount:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: PageOfProductDTO
  PageOfProductRequestDTO:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/ProductRequestDTO'
      itemsCount:
        type: integer
        format: int32
      totalAmount:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: PageOfProductRequestDTO
  PageOfProductResponseDTO:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/ProductResponseDTO'
      itemsCount:
        type: integer
        format: int32
      totalAmount:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: PageOfProductResponseDTO
  PageOfUserDTO:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/UserDTO'
      itemsCount:
        type: integer
        format: int32
      totalAmount:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: PageOfUserDTO
  ProductCommentBase64ImagesData:
    type: object
    properties:
      imagesBase64:
        type: array
        items:
          type: string
      parentCommentId:
        type: integer
        format: int64
      productId:
        type: integer
        format: int64
      productRequestId:
        type: integer
        format: int64
      text:
        type: string
    title: ProductCommentBase64ImagesData
  ProductConditionDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
    title: ProductConditionDTO
  ProductDTO:
    type: object
    properties:
      countryOfOrigin:
        $ref: '#/definitions/CountryDTO'
      attributeValueIds:
        type: array
        items:
          type: integer
          format: int64
      attributeWithValues:
        type: array
        items:
          $ref: '#/definitions/AttributeWithValueDTO'
      attributes:
        type: array
        items:
          $ref: '#/definitions/DescriptionAttributeView'
      availabilityForBargainDate:
        type: string
        format: date-time
      brand:
        $ref: '#/definitions/BrandDTO'
      brandId:
        type: integer
        format: int64
      category:
        $ref: '#/definitions/CategoryDTO'
      categoryId:
        type: integer
        format: int64
      changeTime:
        type: string
        format: date-time
      changeTimestamp:
        type: integer
        format: int64
      commentPostMode:
        type: string
        enum:
          - TEXT_AND_PHOTOS
          - TEXT
          - NONE
      comments:
        type: array
        items:
          $ref: '#/definitions/CommentView'
      commentsCount:
        type: integer
        format: int32
      commentsDTO:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      commissionProc:
        type: number
      conditionId:
        type: integer
        format: int64
      conditionName:
        type: string
      createTime:
        type: string
        format: date-time
      createTimestamp:
        type: integer
        format: int64
      currency:
        $ref: '#/definitions/CurrencyDTO'
      currencyCode:
        type: string
      currentPriceCurrencyId:
        type: integer
        format: int64
      currentPriceInCurrency:
        type: number
      defectImages:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      description:
        type: string
      discount:
        type: integer
        format: int32
      fieldsLackingForModeration:
        type: array
        items:
          type: string
      sectionsLackingForModeration:
        type: object
        additionalProperties:
          type: array
          items:
            type: string
      hasSimilar:
        type: boolean
      higherPrice:
        type: number
      images:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      inBoutique:
        type: boolean
      isAtOffice:
        type: boolean
      isAvailable:
        type: boolean
      isBeegz:
        type: boolean
      isConcierge:
        type: boolean
      isLiked:
        type: boolean
      isNewCollection:
        type: boolean
      isOurChoice:
        type: boolean
      isReadyForBargain:
        type: boolean
      isReadyForModeration:
        type: boolean
      isSold:
        type: boolean
      isVintage:
        type: boolean
      lastCommentsTree:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      lastPriceConvertTime:
        type: string
        format: date-time
      fixedCommissionAmount:
        type: number
      likesCount:
        type: integer
        format: int32
      model:
        type: string
      moderationHoursRemains:
        type: integer
        format: int64
      name:
        type: string
      needsTranslateDescription:
        type: boolean
      origin:
        type: string
      ourChoiceStatusTime:
        type: string
        format: date-time
      parentCategories:
        type: array
        items:
          $ref: '#/definitions/CategoryDTO'
      pickupAddressEndpoint:
        $ref: '#/definitions/AddressEndpointDTO'
      pickupAddressEndpointAggregation:
        $ref: '#/definitions/AddressEndpointAggregationDTO'
      pickupAddressEndpointAggregationId:
        type: integer
        format: int64
      pickupAddressEndpointId:
        type: integer
        format: int64
      pickupCountry:
        $ref: '#/definitions/CountryDTO'
      prettyDiscount:
        type: integer
        format: int32
      prettyPrice:
        type: number
      price:
        type: number
      priceUpdateSubscribersCount:
        type: integer
        format: int32
      priceWithoutCommission:
        type: number
      primaryImageUrl:
        type: string
      productId:
        type: integer
        format: int64
      productModel:
        $ref: '#/definitions/ProductModelDTO'
      productModelId:
        type: integer
        format: int64
      productResponse:
        $ref: '#/definitions/ProductResponseLiteDTO'
      productResponseCount:
        type: integer
        format: int64
      productState:
        type: string
        enum:
          - DRAFT
          - SECOND_EDITION
          - NEED_MODERATION
          - NEED_RETOUCH
          - RETOUCH_DONE
          - REJECTED
          - PUBLISHED
          - HIDDEN
          - SOLD
          - DELETED
          - BANED
      productStateTime:
        type: string
        format: date-time
      productStateTimestamp:
        type: integer
        format: int64
      productWasPublishedByNewPublisher:
        type: boolean
      publishTime:
        type: string
        format: date-time
      publishTimestamp:
        type: integer
        format: int64
      purchasePrice:
        type: number
      purchaseYear:
        type: integer
        format: int32
      rejectReason:
        $ref: '#/definitions/ProductRejectReasonDTO'
      rrpPrice:
        type: number
      rrpPriceCurrencyId:
        type: integer
        format: int64
      rrpPriceInCurrency:
        type: number
      salesChannel:
        type: string
        enum:
          - WEBSITE
          - BOUTIQUE_AND_WEBSITE
          - STOCK_AND_BOUTIQUE_AND_WEBSITE
          - BOUTIQUE
      seller:
        $ref: '#/definitions/UserDTO'
      sellerRecievesSum:
        type: number
      exclusiveSelectionTime:
        type: string
        format: date-time
      exclusiveSelectionTimeForLowStatuses:
        type: string
        format: date-time
      sendToModeratorTime:
        type: string
        format: date-time
      sentToModeratorTimestamp:
        type: integer
        format: int64
      serialNumber:
        type: string
      sex:
        type: string
        enum:
          - MALE
          - FEMALE
          - BOY
          - GIRL
          - ADULT
          - CHILD
      sizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      sizes:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      sourceLink:
        type: string
      startPrice:
        type: number
      storeCode:
        type: string
      subscribedOnPriceUpdates:
        type: boolean
      url:
        type: string
      vendorCode:
        type: string
    title: ProductDTO
  ProductFilter:
    type: object
    properties:
      code:
        type: string
      description:
        type: string
      descriptions:
        type: array
        items:
          $ref: '#/definitions/FilterDescription'
      name:
        type: string
      type:
        type: string
    title: ProductFilter
  ProductFilterInfoRequest:
    type: object
    properties:
      baseCategory:
        type: integer
        format: int64
      currencyCode:
        type: string
      filters:
        type: object
        additionalProperties:
          $ref: '#/definitions/JsonNode'
      presets:
        type: object
        additionalProperties:
          $ref: '#/definitions/JsonNode'
    title: ProductFilterInfoRequest
  ProductFilterItemsRequest:
    type: object
    properties:
      baseCategory:
        type: integer
        format: int64
      currencyCode:
        type: string
      filters:
        type: object
        additionalProperties:
          $ref: '#/definitions/JsonNode'
      page:
        type: integer
        format: int32
      pageLength:
        type: integer
        format: int32
      presets:
        type: object
        additionalProperties:
          $ref: '#/definitions/JsonNode'
      sorting:
        type: string
    title: ProductFilterItemsRequest
  ProductFilterRequest:
    type: object
    properties:
      baseCategory:
        type: integer
        format: int64
      currencyCode:
        type: string
      filters:
        type: object
        additionalProperties:
          $ref: '#/definitions/JsonNode'
      page:
        type: integer
        format: int32
      pageLength:
        type: integer
        format: int32
      presets:
        type: object
        additionalProperties:
          $ref: '#/definitions/JsonNode'
      sorting:
        type: string
      withAvailableValues:
        type: boolean
      withItems:
        type: boolean
    title: ProductFilterRequest
  ProductImageDTO:
    type: object
    properties:
      comment:
        type: string
      id:
        type: integer
        format: int64
      order:
        type: integer
        format: int32
      path:
        type: string
    title: ProductImageDTO
  ProductModelDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
    title: ProductModelDTO
  ProductPriceDTO:
    type: object
    properties:
      commission:
        type: number
      dutiesAmount:
        type: number
      explanation:
        type: string
      priceWithCommission:
        type: number
      priceWithoutCommission:
        type: number
    title: ProductPriceDTO
  ProductRejectReasonDTO:
    type: object
    properties:
      descriptionComment:
        type: string
      id:
        type: integer
        format: int64
      imageComment:
        type: string
      images:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      oldDescription:
        type: string
      oldPrice:
        type: number
      otherComment:
        type: string
      price:
        type: number
      priceComment:
        type: string
      rejectorId:
        type: integer
        format: int64
      timestamp:
        type: integer
        format: int64
    title: ProductRejectReasonDTO
  ProductRequestDTO:
    type: object
    properties:
      attributes:
        type: array
        items:
          $ref: '#/definitions/AttributeDTO'
      brands:
        type: array
        items:
          $ref: '#/definitions/BrandDTO'
      category:
        $ref: '#/definitions/CategoryDTO'
      commentsCount:
        type: integer
        format: int32
      conditions:
        type: array
        items:
          $ref: '#/definitions/ProductConditionDTO'
      createTime:
        type: string
        format: date-time
      currencyCode:
        type: string
      description:
        type: string
      fromPrice:
        type: number
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          $ref: '#/definitions/ProductRequestImageDTO'
      isResponded:
        type: boolean
      lastCommentsTree:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      like:
        $ref: '#/definitions/Like'
      parentCategory:
        $ref: '#/definitions/CategoryDTO'
      productModels:
        type: array
        items:
          $ref: '#/definitions/ProductModelDTO'
      progress:
        type: number
        format: double
      responseCount:
        type: integer
        format: int32
      sharingLink:
        type: string
      similarProductLink:
        type: string
      sizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      sizes:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      state:
        type: string
        enum:
          - DRAFT
          - PUBLISHED
          - HIDDEN
          - MODERATION
          - REJECTED
      stateTime:
        type: string
        format: date-time
      title:
        type: string
      toPrice:
        type: number
      user:
        $ref: '#/definitions/UserDTO'
      needsTranslateDescription:
        type: boolean
    title: ProductRequestDTO
  ProductRequestFilters:
    type: object
    properties:
      filters:
        type: array
        items:
          $ref: '#/definitions/ProductFilter'
      hotFilters:
        type: array
        items:
          type: string
      items:
        $ref: '#/definitions/PageOfProductRequestDTO'
      itemsCount:
        type: integer
        format: int64
      sorting:
        type: array
        items:
          $ref: '#/definitions/ProductSorting'
    title: ProductRequestFilters
  ProductRequestImageDTO:
    type: object
    properties:
      extension:
        type: string
      fileName:
        type: string
      imageURL:
        type: string
      isPrimary:
        type: boolean
    title: ProductRequestImageDTO
  ProductRequestLiteDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      userId:
        type: integer
        format: int64
    title: ProductRequestLiteDTO
  ProductRequestUpdateDTO:
    type: object
    properties:
      currencyCode:
        type: string
      attributeValueIds:
        type: array
        items:
          type: integer
          format: int64
      brandIds:
        type: array
        items:
          type: integer
          format: int64
      categoryId:
        type: integer
        format: int64
      conditionIds:
        type: array
        items:
          type: integer
          format: int64
      description:
        type: string
      fromPrice:
        type: number
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          $ref: '#/definitions/ProductRequestImageDTO'
      modelIds:
        type: array
        items:
          type: integer
          format: int64
      sizeIds:
        type: array
        items:
          type: integer
          format: int64
      sizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      state:
        type: string
        enum:
          - DRAFT
          - PUBLISHED
          - HIDDEN
          - MODERATION
          - REJECTED
      toPrice:
        type: number
    title: ProductRequestUpdateDTO
  ProductResponseDTO:
    type: object
    properties:
      comment:
        type: string
      id:
        type: integer
        format: int64
      product:
        $ref: '#/definitions/ProductDTO'
      productRequestId:
        type: integer
        format: int64
      user:
        $ref: '#/definitions/UserDTO'
    title: ProductResponseDTO
  ProductResponseLiteDTO:
    type: object
    properties:
      comment:
        type: string
      id:
        type: integer
        format: int64
      productRequest:
        $ref: '#/definitions/ProductRequestLiteDTO'
      userId:
        type: integer
        format: int64
    title: ProductResponseLiteDTO
  ProductResponseUpdateDTO:
    type: object
    properties:
      comment:
        type: string
      productId:
        type: integer
        format: int64
      productRequestId:
        type: integer
        format: int64
    title: ProductResponseUpdateDTO
  ProductSorting:
    type: object
    properties:
      code:
        type: string
      isSelected:
        type: boolean
      name:
        type: string
    title: ProductSorting
  Size:
    type: object
    properties:
      id:
        type: integer
        format: int64
      value:
        type: string
    title: Size
  SizeDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
      optionalValuesForAllSizeTypes:
        type: string
    title: SizeDTO
  SizeTypeDTO:
    type: object
    properties:
      isDefault:
        type: boolean
      sizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      sizeTypeAbbreviation:
        type: string
      sizeTypeDescription:
        type: string
      values:
        type: array
        items:
          $ref: '#/definitions/SizeDTO'
    title: SizeTypeDTO
  SizeTypeLocalized:
    type: object
    properties:
      abbreviation:
        type: string
      description:
        type: string
      sizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
    title: SizeTypeLocalized
  PayoutInfoDTO:
    title: OfferDTO
    type: object
    properties:
      value:
        type: integer
        format: bigdecimal
      currencyCode:
        type: string
  SizeValueDTO:
    type: object
    properties:
      payoutInfo:
        $ref: '#/definitions/PayoutInfoDTO'
      sku:
        type: string
      additionalSizeValues:
        type: object
        additionalProperties:
          type: integer
          format: int32
      categorySizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      categorySizeValue:
        type: string
      count:
        type: integer
        format: int32
      id:
        type: integer
        format: int64
      interestingSizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      interestingSizeValue:
        type: string
      offer:
        $ref: '#/definitions/OfferDTO'
      productSizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      productSizeValue:
        type: string
    title: SizeValueDTO
  StepInfo:
    type: object
    properties:
      orderId:
        type: integer
        format: int64
      stepNumber:
        type: integer
        format: int32
    title: StepInfo
  UserDTO:
    type: object
    properties:
      addressEndpoints:
        type: array
        items:
          $ref: '#/definitions/AddressEndpointDTO'
      adminProfileUrl:
        type: string
      avatarPath:
        type: string
      birthDate:
        type: integer
        format: int64
      brandLikesCount:
        type: integer
        format: int32
      canPublishMultiSizes:
        type: boolean
      email:
        type: string
      firstChar:
        type: string
      fullName:
        type: string
      id:
        type: integer
        format: int64
      isAgentSeller:
        type: boolean
      isFollowed:
        type: boolean
      isPro:
        type: boolean
      isTrusted:
        type: boolean
      likesCount:
        type: integer
        format: int32
      name:
        type: string
      nickname:
        type: string
      orderCount:
        type: integer
        format: int32
      productLikesCount:
        type: integer
        format: int32
      productsCount:
        type: integer
        format: int32
      registrationTime:
        type: integer
        format: int64
      sex:
        type: string
        enum:
          - MALE
          - FEMALE
          - BOY
          - GIRL
          - ADULT
          - CHILD
      sellerType:
        $ref: './partial.yaml#/components/schemas/SellerType'
      totalProductCount:
        type: integer
        format: int32
    title: UserDTO
  WaybillDTO:
    type: object
    properties:
      deliveryDestinationType:
        type: string
        enum:
          - OFFICE
          - SELLER
          - BUYER
      externalSystemId:
        type: string
      id:
        type: integer
        format: int64
      pickupDate:
        type: string
        format: date-time
      pickupDestinationType:
        type: string
        enum:
          - OFFICE
          - SELLER
          - BUYER
      pickupInterval:
        type: string
    title: WaybillDTO
  FilterContentDto:
    type: object
    properties:
      categoryAndUserFilter:
        $ref: '#/definitions/CategoryAndUserFilterDto'
      enableFiltration:
        type: boolean
      productListFilter:
        $ref: '#/definitions/ProductListFilterDto'
      productRequestCategoryAndUserFilter:
        $ref: '#/definitions/ProductRequestCategoryAndUserFilterDto'
      productRequestListFilter:
        $ref: '#/definitions/ProductRequestListFilterDto'
      title:
        type: string
      userProfileFilter:
        $ref: '#/definitions/UserProfileFilterDto'
    title: FilterContentDto
  CategoryAndUserFilterDto:
    type: object
    properties:
      brandIds:
        type: array
        items:
          type: integer
          format: int64
      categoryIds:
        type: array
        items:
          type: integer
          format: int64
      colorIds:
        type: array
        items:
          type: integer
          format: int64
      conditionIds:
        type: array
        items:
          type: integer
          format: int64
      isBeegz:
        type: boolean
      isInBoutique:
        type: boolean
      isInOskellyStock:
        type: boolean
      isNewCollection:
        type: boolean
      isOskellyChoice:
        type: boolean
      isVintage:
        type: boolean
      materialIds:
        type: array
        items:
          type: integer
          format: int64
      price:
        $ref: '#/definitions/PriceDto'
      sellerTypes:
        items:
          $ref: './partial.yaml#/components/schemas/SellerType'
      users:
        type: array
        items:
          $ref: '#/definitions/UserDto'
    title: CategoryAndUserFilterDto
  ProductListFilterDto:
    type: object
    properties:
      productIds:
        type: array
        items:
          type: integer
          format: int64
    title: ProductListFilterDto
  ProductRequestCategoryAndUserFilterDto:
    type: object
    properties:
      brandIds:
        type: array
        items:
          type: integer
          format: int64
      categoryIds:
        type: array
        items:
          type: integer
          format: int64
      colorIds:
        type: array
        items:
          type: integer
          format: int64
      conditionIds:
        type: array
        items:
          type: integer
          format: int64
      materialIds:
        type: array
        items:
          type: integer
          format: int64
      price:
        $ref: '#/definitions/PriceDto'
      users:
        type: array
        items:
          $ref: '#/definitions/UserDto'
    title: ProductRequestCategoryAndUserFilterDto
  ProductRequestListFilterDto:
    type: object
    properties:
      productRequestIds:
        type: array
        items:
          type: integer
          format: int64
    title: ProductRequestListFilterDto
  UserProfileFilterDto:
    type: object
    properties:
      userId:
        type: integer
        format: int64
    title: UserProfileFilterDto

  PriceDto:
    type: object
    properties:
      max:
        type: number
        format: double
      min:
        type: number
        format: double
    title: PriceDto
  UserDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
      isPro:
        type: boolean
      nickname:
        type: string
    title: UserDto
  Api2ResponseOfListOfProductModelDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/ProductModelDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfProductModelDTO
  Api2ResponseOfListOfUploadedImageDto:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/UploadedImageDto'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfUploadedImageDto
  UploadedImageDto:
    type: object
    properties:
      extension:
        type: string
      imageURL:
        type: string
      name:
        type: string
    title: UploadedImageDto