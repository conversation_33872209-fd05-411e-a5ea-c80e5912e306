swagger: '2.0'
info:
  version: 2.2.1
  title: Oskelly Main Service
host: 0.0.0.0:8080
basePath: /
tags:
  - name: product-request-controller-api-v-2
    description: Product Request Controller Api V 2
  - name: product-request-publish-controller-api-v-2
    description: Product Request Publish Controller Api V 2
  - name: product-response-controller-api-v-2
    description: Product Response Controller Api V 2
paths:
  /api/v2/productRequests/dislike/{productRequestId}:
    put:
      tags:
        - product-request-controller-api-v-2
      summary: dislike
      operationId: dislikeUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfLike'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/like/{productRequestId}:
    put:
      tags:
        - product-request-controller-api-v-2
      summary: like
      operationId: likeUsingPUT
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfLike'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/liked-page:
    get:
      tags:
        - product-request-controller-api-v-2
      summary: getLikedProductRequestsPage
      operationId: getLikedProductRequestsPageUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          default: 0
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          default: 10
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/liked-page/{userId}:
    get:
      tags:
        - product-request-controller-api-v-2
      summary: getLikedProductRequestsPage
      operationId: getLikedProductRequestsPageUsingGET
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          default: 0
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          default: 10
          format: int64
        - name: userId
          in: path
          description: userId
          required: true
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/attributes/{categoryId}:
    get:
      tags:
        - product-request-publish-controller-api-v-2
      summary: getAttributes
      operationId: getAttributesUsingGET
      produces:
        - '*/*'
      parameters:
        - name: categoryId
          in: path
          description: categoryId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfListOfAttributeDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/publishAndGetResult:
    post:
      tags:
        - product-request-publish-controller-api-v-2
      summary: publishAndGet
      operationId: publishAndGetUsingPOST
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: productRequestUpdateDto
          description: productRequestUpdateDto
          required: true
          schema:
            $ref: '#/definitions/ProductRequestUpdateDTO'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/request/getByStates:
    get:
      tags:
        - product-request-publish-controller-api-v-2
      summary: getByStates
      operationId: getByStatesUsingGET
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
        - name: states
          in: query
          description: states
          required: false
          type: array
          items:
            type: string
            enum:
              - DRAFT
              - PUBLISHED
              - HIDDEN
              - MODERATION
              - REJECTED
          collectionFormat: multi
          enum:
            - DRAFT
            - PUBLISHED
            - HIDDEN
            - MODERATION
            - REJECTED
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productRequests/publish/request/{productRequestId}:
    get:
      tags:
        - product-request-publish-controller-api-v-2
      summary: getById
      operationId: getByIdUsingGET_1
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
    delete:
      tags:
        - product-request-publish-controller-api-v-2
      summary: deleteById
      operationId: deleteByIdUsingDELETE
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/productRequests/{productRequestId}:
    get:
      tags:
        - product-request-controller-api-v-2
      summary: getById
      operationId: getByIdUsingGET
      produces:
        - '*/*'
      parameters:
        - name: productRequestId
          in: path
          description: productRequestId
          required: true
          type: integer
          format: int64
        - name: currencyCode
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productResponses/find:
    get:
      tags:
        - product-response-controller-api-v-2
      summary: find
      operationId: findUsingGET
      produces:
        - '*/*'
      parameters:
        - name: notUserId
          in: query
          description: notUserId
          required: false
          type: integer
          format: int64
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          format: int64
        - name: productRequestId
          in: query
          description: productRequestId
          required: true
          type: integer
          format: int64
        - name: userId
          in: query
          description: userId
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productResponses/listPublished:
    get:
      tags:
        - product-response-controller-api-v-2
      summary: listPublished
      operationId: listPublishedUsingGET
      produces:
        - '*/*'
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          type: integer
          format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          type: integer
          format: int64
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfPageOfProductResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/productResponses/publishAndGetResult:
    post:
      tags:
        - product-response-controller-api-v-2
      summary: publishAndGet
      operationId: publishAndGetUsingPOST_1
      consumes:
        - application/json
      produces:
        - '*/*'
      parameters:
        - in: body
          name: dto
          description: dto
          required: true
          schema:
            $ref: '#/definitions/ProductResponseUpdateDTO'
      responses:
        '200':
          description: OK
          schema:
            $ref: '#/definitions/Api2ResponseOfProductResponseDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
definitions:
  AdditionalSizeDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      image:
        type: string
      isRequired:
        type: boolean
      name:
        type: string
      transliterateName:
        type: string
      value:
        type: integer
        format: int32
    title: AdditionalSizeDTO
  AddressAggregationDTO:
    type: object
    properties:
      address:
        type: string
      address2:
        type: string
      address3:
        type: string
      changeTime:
        type: string
        format: date-time
      cityData:
        $ref: '#/definitions/CityDTO'
      countryData:
        $ref: '#/definitions/CountryDTO'
      createTime:
        type: string
        format: date-time
      id:
        type: integer
        format: int64
      zipCode:
        type: string
    title: AddressAggregationDTO
  AddressAggregationEndpointDTO:
    type: object
    properties:
      address:
        $ref: '#/definitions/AddressAggregationDTO'
      firstName:
        type: string
      id:
        type: integer
        format: int64
      lastName:
        type: string
      patronymicName:
        type: string
      phone:
        type: string
    title: AddressAggregationEndpointDTO
  AddressDTO:
    type: object
    properties:
      address:
        type: string
      address2:
        type: string
      address3:
        type: string
      changeTime:
        type: integer
        format: int64
      city:
        type: string
      cityData:
        $ref: '#/definitions/CityDTO'
      cityFiasId:
        type: string
      country:
        type: string
      countryData:
        $ref: '#/definitions/CountryDTO'
      createTime:
        type: integer
        format: int64
      fullCityName:
        type: string
      id:
        type: integer
        format: int64
      region:
        type: string
      settlementFiasId:
        type: string
      zipCode:
        type: string
    title: AddressDTO
  AddressEndpointAggregationDTO:
    type: object
    properties:
      billingAddress:
        $ref: '#/definitions/AddressAggregationEndpointDTO'
      id:
        type: integer
        format: int64
      physicalAddress:
        $ref: '#/definitions/AddressAggregationEndpointDTO'
      usePhysicalAddressForBilling:
        type: boolean
    title: AddressEndpointAggregationDTO
  AddressEndpointDTO:
    type: object
    properties:
      address:
        $ref: '#/definitions/AddressDTO'
      firstName:
        type: string
      id:
        type: integer
        format: int64
      lastName:
        type: string
      patronymicName:
        type: string
      phone:
        type: string
    title: AddressEndpointDTO
  Api2ResponseOfLike:
    type: object
    properties:
      data:
        $ref: '#/definitions/Like'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfLike
  Api2ResponseOfListOfAttributeDTO:
    type: object
    properties:
      data:
        type: array
        items:
          $ref: '#/definitions/AttributeDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfListOfAttributeDTO
  Api2ResponseOfPageOfProductRequestDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/PageOfProductRequestDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfPageOfProductRequestDTO
  Api2ResponseOfPageOfProductResponseDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/PageOfProductResponseDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfPageOfProductResponseDTO
  Api2ResponseOfProductRequestDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductRequestDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductRequestDTO
  Api2ResponseOfProductResponseDTO:
    type: object
    properties:
      data:
        $ref: '#/definitions/ProductResponseDTO'
      errorData:
        type: object
      executionTimeMillis:
        type: integer
        format: int64
      humanMessage:
        type: string
      message:
        type: string
      timestamp:
        type: integer
        format: int64
      validationMessages:
        type: object
        additionalProperties:
          type: string
    title: Api2ResponseOfProductResponseDTO
  AttributeDTO:
    type: object
    properties:
      attributeValues:
        type: array
        items:
          $ref: '#/definitions/AttributeValueDTO'
      id:
        type: integer
        format: int64
      isRequired:
        type: boolean
      kind:
        type: string
        enum:
          - GENERIC
          - MATERIAL
          - COLOR
      name:
        type: string
      showFilter:
        type: boolean
    title: AttributeDTO
  AttributeValueDTO:
    type: object
    properties:
      icon:
        type: string
      id:
        type: integer
        format: int64
      ofValue:
        type: string
      pluralGenitiveValue:
        type: string
      singularGenitiveValue:
        type: string
      transliterateValue:
        type: string
      value:
        type: string
    title: AttributeValueDTO
  AttributeWithValueDTO:
    type: object
    properties:
      attribute:
        $ref: '#/definitions/AttributeDTO'
      attributeValue:
        $ref: '#/definitions/AttributeValueDTO'
    title: AttributeWithValueDTO
  BargainCategory:
    type: object
    required:
      - id
      - title
    properties:
      id:
        type: integer
        format: int64
        description: Идентификатор категории
      title:
        type: string
        description: Название категории
    title: BargainCategory
    description: Категория товар контрторга
  BargainLite:
    type: object
    required:
      - attemptsLeft
      - basePrice
      - buyer
      - createTime
      - id
      - lastPrice
      - product
      - seller
      - size
      - state
    properties:
      attemptsLeft:
        type: integer
        format: int32
        description: Сколько попыток осталось
      basePrice:
        type: integer
        format: int32
        description: Первоначальная цена на момент создания контрторга
      buyer:
        description: Покупатель
        $ref: '#/definitions/BargainUser'
      changeTime:
        type: string
        format: date-time
        description: Дата обновления
      createTime:
        type: string
        format: date-time
        description: Дата создания
      id:
        type: integer
        format: int64
        description: Идентификатор контрторга
      lastPrice:
        type: integer
        format: int32
        description: Последняя предложенная цена
      product:
        description: Товар
        $ref: '#/definitions/BargainProduct'
      seller:
        description: Продавец
        $ref: '#/definitions/BargainUser'
      size:
        description: Размер
        $ref: '#/definitions/BargainSize'
      state:
        description: Статус контрторга
        $ref: '#/definitions/BargainState'
      timeLeft:
        type: integer
        format: int64
        description: Сколько времени осталось до принятия решения покупателем или продавцом (секунды)
    title: BargainLite
    description: Сокращенная информация о контрторге
  BargainProduct:
    type: object
    required:
      - category
      - commentsCount
      - id
      - image
      - inBoutique
      - isLiked
      - isSubscribed
      - likesCount
      - subscribersCount
      - title
      - withBadge
    properties:
      category:
        description: Категория
        $ref: '#/definitions/BargainCategory'
      commentsCount:
        type: integer
        format: int32
        description: Количество комментариев
      id:
        type: integer
        format: int64
        description: Идентификатор товара
      image:
        type: string
        description: Изображение товара
      inBoutique:
        type: boolean
        description: Товар находится в бутике
      isLiked:
        type: boolean
        description: Лайкнут текущим пользователем
      isSubscribed:
        type: boolean
        description: Наличие подписки от текущего пользователя
      likesCount:
        type: integer
        format: int32
        description: Количество лайков
      subscribersCount:
        type: integer
        format: int32
        description: Количество подписчиков
      title:
        type: string
        description: Название товара
      withBadge:
        type: boolean
        description: С биркой
    title: BargainProduct
    description: Товар контрторга
  BargainSize:
    type: object
    required:
      - id
      - title
    properties:
      id:
        type: integer
        format: int64
        description: Идентификатор размера
      title:
        type: string
        description: Название размера
    title: BargainSize
    description: Размер товара
  BargainState:
    type: object
    required:
      - decoration
      - name
      - title
    properties:
      decoration:
        type: string
        description: Декорация статуса
        enum:
          - NEGATIVE
          - POSITIVE
          - NEUTRAL
      name:
        type: string
        description: Статус контрторга. OFFER/COUNTER_OFFER - активный, DECLINED - отклоненный, CONFIRMED - подтвержденный, EXPIRED - просроченный, LOW_PRICE_CLOSED - отменен (снижение цены товара)
        enum:
          - INITIAL
          - OFFER
          - COUNTER_OFFER
          - DECLINED
          - CONFIRMED
          - SOLD
          - CONSUMED
          - EXPIRED
          - CANCELLED
          - UNDEFINED
      title:
        type: string
        description: Человекочитаемое название статуса
    title: BargainState
    description: Статус контрторга
  BargainUser:
    type: object
    required:
      - id
      - isPro
      - nickname
      - type
    properties:
      avatar:
        type: string
        description: Аватар
      id:
        type: integer
        format: int64
        description: Идентификатор пользователя
      isPro:
        type: boolean
        description: Является бутиком (PRO-продавцом)
      nickname:
        type: string
        description: Никнейм
      type:
        type: string
        description: Тип продавца для отображения, н.п. Бутик
    title: BargainUser
    description: Участник контрторга (продавец или покупатель)
  BrandDTO:
    type: object
    properties:
      brandDescriptions:
        type: array
        items:
          $ref: '#/definitions/BrandDescriptionDto'
      description:
        type: string
      hiddenDescription:
        type: string
      id:
        type: integer
        format: int64
      isHidden:
        type: boolean
      isLiked:
        type: boolean
      name:
        type: string
      products:
        type: array
        items:
          $ref: '#/definitions/ProductDTO'
      productsCount:
        type: integer
        format: int32
      title:
        type: string
      transliterateName:
        type: string
      urlName:
        type: string
    title: BrandDTO
  BrandDescriptionDto:
    type: object
    properties:
      category:
        $ref: '#/definitions/CategoryDisplayNameDTO'
      description:
        type: string
    title: BrandDescriptionDto
  CategoryDTO:
    type: object
    properties:
      additionalSizes:
        type: array
        items:
          $ref: '#/definitions/AdditionalSizeDTO'
      attributes:
        type: array
        items:
          $ref: '#/definitions/AttributeDTO'
      children:
        type: array
        items:
          $ref: '#/definitions/CategoryDTO'
      defaultSizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      displayName:
        type: string
      fullName:
        type: string
      hasChildren:
        type: boolean
      icon:
        type: string
      id:
        type: integer
        format: int64
      minPrice:
        type: integer
        format: int32
      pluralName:
        type: string
      productsCount:
        type: integer
        format: int32
      singularFullName:
        type: string
      singularName:
        type: string
      sizeValues:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      url:
        type: string
    title: CategoryDTO
  CategoryDisplayNameDTO:
    type: object
    properties:
      displayName:
        type: string
      id:
        type: integer
        format: int64
    title: CategoryDisplayNameDTO
  CityDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
      region:
        type: string
    title: CityDTO
  CommentDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          type: string
      needsTranslate:
        type: boolean
      parentCommentId:
        type: integer
        format: int64
      productId:
        type: integer
        format: int64
      productRequestId:
        type: integer
        format: int64
      publishedAtTime:
        type: integer
        format: int64
      publisher:
        $ref: '#/definitions/UserDTO'
      replyTo:
        type: string
      subComments:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      text:
        type: string,
      deletedAtTime:
        type: string
      editedAtTime:
        type: string
    title: CommentDTO
  CommentView:
    type: object
    properties:
      avatar:
        type: string
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          type: string
      isAnswer:
        type: boolean
      publishTime:
        type: string
      publishZonedDateTime:
        type: integer
        format: int64
      text:
        type: string
      user:
        type: string
      userId:
        type: integer
        format: int64
    title: CommentView
  CountryDTO:
    type: object
    properties:
      countryCounterpartyType:
        type: string
        enum:
          - UAE_COUNTERPARTY
          - DEFAULT_COUNTERPARTY
      currency:
        $ref: '#/definitions/CurrencyDTO'
      id:
        type: integer
        format: int64
      imageUrl:
        type: string
      isoCodeAlpha2:
        type: string
      name:
        type: string
      requireZipcode:
        type: boolean
    title: CountryDTO
  CurrencyDTO:
    type: object
    properties:
      active:
        type: boolean
      base:
        type: boolean
      id:
        type: integer
        format: int64
      isoCode:
        type: string
      isoNumber:
        type: integer
        format: int32
      name:
        type: string
      selectedByDefault:
        type: boolean
      sign:
        type: string
    title: CurrencyDTO
  DescriptionAttributeView:
    type: object
    properties:
      attributeValueId:
        type: integer
        format: int64
      title:
        type: string
      value:
        type: string
    title: DescriptionAttributeView
  Like:
    type: object
    properties:
      count:
        type: integer
        format: int64
      isLiked:
        type: boolean
    title: Like
  OfferDTO:
    type: object
    properties:
      brandId:
        type: integer
        format: int64
      categoryId:
        type: integer
        format: int64
      consumed:
        type: boolean
      id:
        type: integer
        format: int64
      isSizeAvailable:
        type: boolean
      negotiatedPrice:
        type: number
      offerStatus:
        type: string
        enum:
          - PENDING
          - ACCEPTED
          - REJECTED
      offerorId:
        type: integer
        format: int64
      price:
        type: number
      product:
        $ref: '#/definitions/ProductDTO'
      productId:
        type: integer
        format: int64
      productState:
        type: string
        enum:
          - DRAFT
          - SECOND_EDITION
          - NEED_MODERATION
          - NEED_RETOUCH
          - RETOUCH_DONE
          - REJECTED
          - PUBLISHED
          - HIDDEN
          - SOLD
          - DELETED
          - BANED
      sizeId:
        type: integer
        format: int64
    title: OfferDTO
  PageOfProductRequestDTO:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/ProductRequestDTO'
      itemsCount:
        type: integer
        format: int32
      totalAmount:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: PageOfProductRequestDTO
  PageOfProductResponseDTO:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: '#/definitions/ProductResponseDTO'
      itemsCount:
        type: integer
        format: int32
      totalAmount:
        type: integer
        format: int64
      totalPages:
        type: integer
        format: int32
    title: PageOfProductResponseDTO
  ProductConditionDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
    title: ProductConditionDTO
  ProductDTO:
    type: object
    properties:
      attributeValueIds:
        type: array
        items:
          type: integer
          format: int64
      attributeWithValues:
        type: array
        items:
          $ref: '#/definitions/AttributeWithValueDTO'
      attributes:
        type: array
        items:
          $ref: '#/definitions/DescriptionAttributeView'
      availabilityForBargainDate:
        type: string
        format: date-time
      brand:
        $ref: '#/definitions/BrandDTO'
      brandId:
        type: integer
        format: int64
      category:
        $ref: '#/definitions/CategoryDTO'
      categoryId:
        type: integer
        format: int64
      changeTime:
        type: string
        format: date-time
      changeTimestamp:
        type: integer
        format: int64
      commentPostMode:
        type: string
        enum:
          - TEXT_AND_PHOTOS
          - TEXT
          - NONE
      comments:
        type: array
        items:
          $ref: '#/definitions/CommentView'
      commentsCount:
        type: integer
        format: int32
      commentsDTO:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      commissionProc:
        type: number
      conditionId:
        type: integer
        format: int64
      conditionName:
        type: string
      createTime:
        type: string
        format: date-time
      createTimestamp:
        type: integer
        format: int64
      currency:
        $ref: '#/definitions/CurrencyDTO'
      currencyCode:
        type: string
      currentPriceCurrencyId:
        type: integer
        format: int64
      currentPriceInCurrency:
        type: number
      defectImages:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      description:
        type: string
      discount:
        type: integer
        format: int32
      fieldsLackingForModeration:
        type: array
        items:
          type: string
      sectionsLackingForModeration:
        type: object
        additionalProperties:
          type: array
          items:
            type: string
      hasSimilar:
        type: boolean
      higherPrice:
        type: number
      images:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      inBoutique:
        type: boolean
      isAtOffice:
        type: boolean
      isAvailable:
        type: boolean
      isBeegz:
        type: boolean
      isBonusesProhibited:
        type: boolean
      isCarryOver:
        type: boolean
      isConcierge:
        type: boolean
      isInvestment:
        type: boolean
      isLiked:
        type: boolean
      isNewCollection:
        type: boolean
      isOurChoice:
        type: boolean
      isReadyForBargain:
        type: boolean
      isReadyForModeration:
        type: boolean
      isSold:
        type: boolean
      isVintage:
        type: boolean
      lastCommentsTree:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      lastPriceConvertTime:
        type: string
        format: date-time
      fixedCommissionAmount:
        type: number
      likesCount:
        type: integer
        format: int32
      model:
        type: string
      moderationHoursRemains:
        type: integer
        format: int64
      name:
        type: string
      needsTranslateDescription:
        type: boolean
      origin:
        type: string
      ourChoiceStatusTime:
        type: string
        format: date-time
      parentCategories:
        type: array
        items:
          $ref: '#/definitions/CategoryDTO'
      pickupAddressEndpoint:
        $ref: '#/definitions/AddressEndpointDTO'
      pickupAddressEndpointAggregation:
        $ref: '#/definitions/AddressEndpointAggregationDTO'
      pickupAddressEndpointAggregationId:
        type: integer
        format: int64
      pickupAddressEndpointId:
        type: integer
        format: int64
      pickupCountry:
        $ref: '#/definitions/CountryDTO'
      prettyDiscount:
        type: integer
        format: int32
      prettyPrice:
        type: number
      price:
        type: number
      priceUpdateSubscribersCount:
        type: integer
        format: int32
      priceWithoutCommission:
        type: number
      primaryImageUrl:
        type: string
      productId:
        type: integer
        format: int64
      productModel:
        $ref: '#/definitions/ProductModelDTO'
      productModelId:
        type: integer
        format: int64
      productResponse:
        $ref: '#/definitions/ProductResponseLiteDTO'
      productResponseCount:
        type: integer
        format: int64
      productState:
        type: string
        enum:
          - DRAFT
          - SECOND_EDITION
          - NEED_MODERATION
          - NEED_RETOUCH
          - RETOUCH_DONE
          - REJECTED
          - PUBLISHED
          - HIDDEN
          - SOLD
          - DELETED
          - BANED
      productStateTime:
        type: string
        format: date-time
      productStateTimestamp:
        type: integer
        format: int64
      productWasPublishedByNewPublisher:
        type: boolean
      publishTime:
        type: string
        format: date-time
      publishTimestamp:
        type: integer
        format: int64
      purchasePrice:
        type: number
      purchaseYear:
        type: integer
        format: int32
      rejectReason:
        $ref: '#/definitions/ProductRejectReasonDTO'
      rrpPrice:
        type: number
      rrpPriceCurrencyId:
        type: integer
        format: int64
      rrpPriceInCurrency:
        type: number

      salesChannel:
        type: string
        enum:
          - WEBSITE
          - BOUTIQUE_AND_WEBSITE
          - STOCK_AND_BOUTIQUE_AND_WEBSITE
          - BOUTIQUE
      seasonId:
        type: integer
        format: int64
      season:
        $ref: '#/definitions/SeasonDTO'
      seller:
        $ref: '#/definitions/UserDTO'
      sellerRecievesSum:
        type: number
      exclusiveSelectionTime:
        type: string
        format: date-time
      exclusiveSelectionTimeForLowStatuses:
        type: string
        format: date-time
      sendToModeratorTime:
        type: string
        format: date-time
      sentToModeratorTimestamp:
        type: integer
        format: int64
      serialNumber:
        type: string
      sex:
        type: string
        enum:
          - MALE
          - FEMALE
          - BOY
          - GIRL
          - ADULT
          - CHILD
      sizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      sizes:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      sourceLink:
        type: string
      startPrice:
        type: number
      storeCode:
        type: string
      subscribedOnPriceUpdates:
        type: boolean
      url:
        type: string
      vendorCode:
        type: string
    title: ProductDTO
  ProductImageDTO:
    type: object
    properties:
      comment:
        type: string
      id:
        type: integer
        format: int64
      order:
        type: integer
        format: int32
      path:
        type: string
    title: ProductImageDTO
  ProductModelDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      name:
        type: string
    title: ProductModelDTO
  ProductRejectReasonDTO:
    type: object
    properties:
      descriptionComment:
        type: string
      id:
        type: integer
        format: int64
      imageComment:
        type: string
      images:
        type: array
        items:
          $ref: '#/definitions/ProductImageDTO'
      oldDescription:
        type: string
      oldPrice:
        type: number
      otherComment:
        type: string
      price:
        type: number
      priceComment:
        type: string
      rejectorId:
        type: integer
        format: int64
      timestamp:
        type: integer
        format: int64
    title: ProductRejectReasonDTO
  ProductRequestDTO:
    type: object
    properties:
      attributes:
        type: array
        items:
          $ref: '#/definitions/AttributeDTO'
      brands:
        type: array
        items:
          $ref: '#/definitions/BrandDTO'
      category:
        $ref: '#/definitions/CategoryDTO'
      commentsCount:
        type: integer
        format: int32
      conditions:
        type: array
        items:
          $ref: '#/definitions/ProductConditionDTO'
      createTime:
        type: string
        format: date-time
      currencyCode:
        type: string
      description:
        type: string
      fromPrice:
        type: number
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          $ref: '#/definitions/ProductRequestImageDTO'
      isResponded:
        type: boolean
      lastCommentsTree:
        type: array
        items:
          $ref: '#/definitions/CommentDTO'
      like:
        $ref: '#/definitions/Like'
      parentCategory:
        $ref: '#/definitions/CategoryDTO'
      productModels:
        type: array
        items:
          $ref: '#/definitions/ProductModelDTO'
      progress:
        type: number
        format: double
      responseCount:
        type: integer
        format: int32
      sharingLink:
        type: string
      similarProductLink:
        type: string
      sizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      sizes:
        type: array
        items:
          $ref: '#/definitions/SizeValueDTO'
      state:
        type: string
        enum:
          - DRAFT
          - PUBLISHED
          - HIDDEN
          - MODERATION
          - REJECTED
      stateTime:
        type: string
        format: date-time
      title:
        type: string
      toPrice:
        type: number
      user:
        $ref: '#/definitions/UserDTO'
      needsTranslateDescription:
        type: boolean
    title: ProductRequestDTO
  ProductRequestImageDTO:
    type: object
    properties:
      extension:
        type: string
      fileName:
        type: string
      imageURL:
        type: string
      isPrimary:
        type: boolean
    title: ProductRequestImageDTO
  ProductRequestLiteDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      userId:
        type: integer
        format: int64
    title: ProductRequestLiteDTO
  ProductRequestUpdateDTO:
    type: object
    properties:
      currencyCode:
        type: string
      attributeValueIds:
        type: array
        items:
          type: integer
          format: int64
      brandIds:
        type: array
        items:
          type: integer
          format: int64
      categoryId:
        type: integer
        format: int64
      conditionIds:
        type: array
        items:
          type: integer
          format: int64
      description:
        type: string
      fromPrice:
        type: number
      id:
        type: integer
        format: int64
      images:
        type: array
        items:
          $ref: '#/definitions/ProductRequestImageDTO'
      modelIds:
        type: array
        items:
          type: integer
          format: int64
      sizeIds:
        type: array
        items:
          type: integer
          format: int64
      sizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      state:
        type: string
        enum:
          - DRAFT
          - PUBLISHED
          - HIDDEN
          - MODERATION
          - REJECTED
      toPrice:
        type: number
    title: ProductRequestUpdateDTO
  ProductResponseDTO:
    type: object
    properties:
      comment:
        type: string
      id:
        type: integer
        format: int64
      product:
        $ref: '#/definitions/ProductDTO'
      productRequestId:
        type: integer
        format: int64
      user:
        $ref: '#/definitions/UserDTO'
    title: ProductResponseDTO
  ProductResponseLiteDTO:
    type: object
    properties:
      comment:
        type: string
      id:
        type: integer
        format: int64
      productRequest:
        $ref: '#/definitions/ProductRequestLiteDTO'
      userId:
        type: integer
        format: int64
    title: ProductResponseLiteDTO
  ProductResponseUpdateDTO:
    type: object
    properties:
      comment:
        type: string
      productId:
        type: integer
        format: int64
      productRequestId:
        type: integer
        format: int64
    title: ProductResponseUpdateDTO
  SizeTypeLocalized:
    type: object
    properties:
      abbreviation:
        type: string
      description:
        type: string
      sizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
    title: SizeTypeLocalized
  PayoutInfoDTO:
    title: OfferDTO
    type: object
    properties:
      value:
        type: integer
        format: bigdecimal
      currencyCode:
        type: string
  SeasonDTO:
    type: object
    properties:
      id:
        type: integer
        format: int64
      seasonType:
        type: string
        enum:
          - SS
          - AW
      year:
        type: integer
        format: int32
      name:
        type: string
    required:
      - id
      - seasonType
      - year
      - name
  SizeValueDTO:
    type: object
    properties:
      payoutInfo:
        $ref: '#/definitions/PayoutInfoDTO'
      sku:
        type: string
      additionalSizeValues:
        type: object
        additionalProperties:
          type: integer
          format: int32
      bargainLite:
        $ref: '#/definitions/BargainLite'
      categorySizeType:
        $ref: '#/definitions/SizeTypeLocalized'
      categorySizeValue:
        type: string
      count:
        type: integer
        format: int32
      id:
        type: integer
        format: int64
      interestingSizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      interestingSizeValue:
        type: string
      offer:
        $ref: '#/definitions/OfferDTO'
      productSizeType:
        type: string
        enum:
          - RU
          - EU
          - US
          - INT
          - UK
          - FR
          - IT
          - DE
          - AU
          - JPN
          - INCHES
          - CENTIMETERS
          - COLLAR_CENTIMETERS
          - COLLAR_INCHES
          - RING_RUSSIAN
          - RING_EUROPEAN
          - JEANS
          - HEIGHT
          - AGE
          - NO_SIZE
          - BUST
      productSizeValue:
        type: string
    title: SizeValueDTO
  UserDTO:
    type: object
    properties:
      addressEndpoints:
        type: array
        items:
          $ref: '#/definitions/AddressEndpointDTO'
      adminProfileUrl:
        type: string
      avatarPath:
        type: string
      birthDate:
        type: integer
        format: int64
      brandLikesCount:
        type: integer
        format: int32
      canPublishMultiSizes:
        type: boolean
      email:
        type: string
      firstChar:
        type: string
      fullName:
        type: string
      id:
        type: integer
        format: int64
      isAgentSeller:
        type: boolean
      isFollowed:
        type: boolean
      isPro:
        type: boolean
      isTrusted:
        type: boolean
      likesCount:
        type: integer
        format: int32
      name:
        type: string
      nickname:
        type: string
      orderCount:
        type: integer
        format: int32
      productLikesCount:
        type: integer
        format: int32
      productsCount:
        type: integer
        format: int32
      registrationTime:
        type: integer
        format: int64
      sex:
        type: string
        enum:
          - MALE
          - FEMALE
          - BOY
          - GIRL
          - ADULT
          - CHILD
      totalProductCount:
        type: integer
        format: int32
      sellerType:
        $ref: './partial.yaml#/components/schemas/SellerType'
    title: UserDTO
