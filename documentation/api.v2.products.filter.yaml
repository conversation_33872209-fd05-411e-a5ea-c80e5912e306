openapi: 3.0.3
x-stoplight:
  id: tgw59tbxihx92
info:
  title: Product Filtration API
  version: '1.0'
servers:
  - url: 'http://localhost:3000'
paths:
  /api/v2/products/filter:
    post:
      summary: getAvailableFilters
      operationId: post-api-v2-products-filter
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfProductFilters'
              examples: {}
      description: Получение доступных фильтров (+ опционально товаров) в зависимости от заданной фильтрации
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterRequest'
            examples: {}
        description: ''
  /api/v2/products/filter/info:
    post:
      summary: getAvailableFilterInfo
      operationId: post-api-v2-products-filter-info
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfProductFilter'
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterInfoRequest'
            examples: {}
        description: ''
  /api/v2/products/filter/items:
    post:
      summary: getItems
      operationId: post-api-v2-products-filter-items
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
              examples: {}
        '':
          content:
            application/json:
              schema:
                $ref: ./partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterItemsRequest'
            examples: {}
        description: ''
    parameters: []
  /api/v2/products/filter/count:
    post:
      summary: getItemsCount
      operationId: post-api-v2-products-filter-count
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiV2ResponseOfItemsCount'
              examples: {}
        '':
          content:
            application/json:
              schema:
                $ref: ./partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductFilterItemsRequest'
            examples: {}
        description: ''
      description: ''
    parameters: []
components:
  schemas:
    PriceValue:
      title: PriceValue
      x-stoplight:
        id: 4g2wpjptf9ysg
      type: object
      properties:
        lower:
          type: number
        upper:
          type: number
      description: Значение фильтра по цене для запроса сортировки
    FilterValue:
      title: FilterValue
      x-stoplight:
        id: 6rj9xibescb6f
      oneOf:
        - type: boolean
        - type: array
          items:
            type: integer
        - type: string
          properties: {}
        - type: array
          items:
            type: string
        - $ref: '#/components/schemas/PriceValue'
      x-examples: {}
      description: Значение фильтра для запроса сортировки
    ProductFilterInfoRequest:
      title: ProductFilterInfoRequest
      x-stoplight:
        id: wjt8znnhm9e4t
      type: object
      description: Запрос на получение доступных значений заданного фильтра
      properties:
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/FilterValue'
          description: |
            Коллекция фильтров со значениями. Доступные среди прочих фильтры:
            * offlineOnly: true | false - выдать только товары, находящиеся offline | все товары
        presets:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/FilterValue'
          description: |
            Коллекция пресетов со значениями. Доступные среди прочих пресеты:
            * offlineOnly: true | false - выдать только товары, находящиеся offline | все товары
        baseCategory:
          type: integer
        currencyCode:
          type: string
        contexts:
          type: array
          items:
            $ref: '#/components/schemas/ClientProductFiltrationContext'
    ProductFilterItemsRequest:
      title: ProductFilterItemsRequest
      x-stoplight:
        id: 1eu1xjjexsbho
      description: 'Запрос на получение списка товаров, удовлетворяющих условиям фильтрации'
      allOf:
        - $ref: '#/components/schemas/ProductFilterInfoRequest'
        - type: object
          properties:
            sorting:
              type: string
              default: NEW
            page:
              type: integer
              default: 1
            pageLength:
              type: integer
              default: 60
      x-examples: {}
    ProductFilterRequest:
      title: ProductFilterRequest
      x-stoplight:
        id: 1eu1xjjexsbho
      description: Запрос на получение доступных значений фильтра и товаров по фильтрам
      allOf:
        - $ref: '#/components/schemas/ProductFilterItemsRequest'
        - type: object
          properties:
            withAvailableValues:
              type: boolean
              default: false
            withItems:
              type: boolean
              default: false
      x-examples: {}
    ProductSorting:
      title: ProductSorting
      x-stoplight:
        id: g69xaazp34b4z
      type: object
      properties:
        code:
          type: string
        name:
          type: string
        isSelected:
          type: boolean
      required:
        - code
        - name
        - isSelected
    AbstractProductFilter:
      title: AbstractProductFilter
      x-stoplight:
        id: q0z1v269c3d6y
      type: object
      description: Базовая схема доступного фильтра
      properties:
        code:
          type: string
        type:
          type: string
        name:
          type: string
        description:
          type: string
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/FilterDescription'
      required:
        - code
        - type
        - name
    PriceFilter:
      title: PriceFilter
      x-stoplight:
        id: dliuywatjluww
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - PRICE
            lower:
              type: number
            upper:
              type: number
            selectedLower:
              type: number
            selectedUpper:
              type: number
          required:
            - type
      description: Доступный фильтр по цене
      x-examples: {}
    BooleanFilter:
      title: BooleanFilter
      x-stoplight:
        id: 93vcj5w2gkney
      description: Доступный фильтр по true/false значениям
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - BOOLEAN
            value:
              type: boolean
            isEnabled:
              type: boolean
          required:
            - type
            - value
            - isEnabled
      x-examples: {}
    ListValue:
      title: ListValue
      x-stoplight:
        id: 0s3vn03gwis7h
      type: object
      description: Значение из списка
      properties:
        id:
          type: integer
        value:
          type: string
      required:
        - id
        - value
    ListHotValue:
      title: ListHotValue
      x-stoplight:
        id: 16pggkk7w7fyr
      allOf:
        - $ref: '#/components/schemas/ListValue'
        - type: object
          properties:
            isSelected:
              type: boolean
          required:
            - isSelected
      description: Значение из списка значений быстрого выбора
    ListSectionEntry:
      title: ListSectionEntry
      x-stoplight:
        id: doh70gsrg14v5
      type: object
      description: Значение из набора элементов секции списка
      properties:
        id:
          type: integer
        value:
          type: string
        description:
          type: string
        isSelected:
          type: boolean
        image:
          type: string
      required:
        - id
        - value
        - isSelected
    ListSection:
      title: ListSection
      x-stoplight:
        id: an0c642x5nejh
      type: object
      description: Секция списка
      properties:
        name:
          type: string
        entries:
          type: array
          items:
            $ref: '#/components/schemas/ListSectionEntry'
      required:
        - name
        - entries
    MultiListFilter:
      title: MultiListFilter
      x-stoplight:
        id: rg853yzydym2c
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - MULTI_LIST
            values:
              type: array
              items:
                $ref: '#/components/schemas/ListSection'
            hotValues:
              type: array
              items:
                $ref: '#/components/schemas/ListHotValue'
            selectedValues:
              type: array
              items:
                $ref: '#/components/schemas/ListValue'
            searchableSections:
              type: array
              items:
                type: string
            hasMoreValues:
              type: boolean
          required:
            - type
            - searchableSections
            - hasMoreValues
      x-examples: {}
      description: Доступный фильтр по списочным значениям
    CategoryTreeValue:
      title: CategoryTreeValue
      x-stoplight:
        id: bid0vcnp9jyzb
      type: object
      description: Значение из фильтра с типом дерева категорий
      properties:
        id:
          type: integer
        name:
          type: string
        selectedValues:
          type: array
          items:
            $ref: '#/components/schemas/ListValue'
        isSelected:
          type: boolean
        children:
          type: array
          items:
            $ref: '#/components/schemas/CategoryTreeValue'
      required:
        - id
        - name
        - isSelected
      x-examples: {}
    CategoryTreeFilter:
      title: CategoryTreeFilter
      x-stoplight:
        id: n6lp172ryovo3
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - CATEGORY_TREE
            values:
              type: array
              items:
                $ref: '#/components/schemas/CategoryTreeValue'
            selectedValues:
              type: array
              items:
                $ref: '#/components/schemas/ListValue'
            hasMoreValues:
              type: boolean
          required:
            - type
            - hasMoreValues
      description: Доступный фильтр по значению категории
    ChartValue:
      title: ChartValue
      x-stoplight:
        id: 03ti06h5jpzc4
      type: object
      description: Значение из сетки размеров
      properties:
        code:
          type: string
        name:
          type: string
        value:
          type: string
      required:
        - code
        - name
        - value
    SizeValue:
      title: SizeValue
      x-stoplight:
        id: kq5r5xdeyw5qc
      type: object
      description: Значение доступного размера
      properties:
        id:
          type: integer
        name:
          type: string
        isSelected:
          type: boolean
        charts:
          type: array
          items:
            $ref: '#/components/schemas/ChartValue'
      required:
        - id
        - name
        - isSelected
    CategorySizeValue:
      title: CategorySizeValue
      x-stoplight:
        id: ci0qzuozvw4we
      type: object
      description: Доступные размеры в сетке категории
      properties:
        code:
          type: string
        name:
          type: string
        values:
          type: array
          items:
            $ref: '#/components/schemas/SizeValue'
      required:
        - code
        - name
    CategorySize:
      title: CategorySize
      x-stoplight:
        id: oqs4kvaehfge9
      type: object
      description: Доступные значения фильтра по размерам
      properties:
        id:
          type: integer
        name:
          type: string
        selectedValues:
          type: array
          items:
            $ref: '#/components/schemas/ListValue'
        sizesValue:
          $ref: '#/components/schemas/CategorySizeValue'
      required:
        - id
        - name
        - sizesValue
    SizeFilter:
      allOf:
        - $ref: '#/components/schemas/AbstractProductFilter'
        - type: object
          properties:
            type:
              type: string
              enum:
                - SIZE
            values:
              type: array
              items:
                $ref: '#/components/schemas/CategorySize'
            selectedValues:
              type: array
              items:
                $ref: '#/components/schemas/ListValue'
            hasMoreValues:
              type: boolean
          required:
            - type
            - hasMoreValues
      description: Доступный фильтр по размерам
      title: SizeFilter
    ProductFilter:
      title: ProductFilter
      x-stoplight:
        id: vvaocjbrf47we
      oneOf:
        - $ref: '#/components/schemas/BooleanFilter'
        - $ref: '#/components/schemas/CategoryTreeFilter'
        - $ref: '#/components/schemas/MultiListFilter'
        - $ref: '#/components/schemas/PriceFilter'
        - $ref: '#/components/schemas/SizeFilter'
      x-examples: {}
      description: Доступный фильтр
    ProductFilters:
      title: ProductFilters
      x-stoplight:
        id: i47bpyhuj9t2j
      type: object
      description: 'Описание доступных фильтров, сортировок и товаров'
      properties:
        sorting:
          type: array
          items:
            $ref: '#/components/schemas/ProductSorting'
        filters:
          type: array
          items:
            $ref: '#/components/schemas/ProductFilter'
        hotFilters:
          type: array
          items:
            type: string
        newResaleFilter:
          $ref: '#/components/schemas/ProductFilter'
        itemsCount:
          type: integer
        items:
          $ref: './partial.yaml#/components/schemas/PageOfProductDTO'
      required:
        - sorting
        - filters
        - hotFilters
        - itemsCount
    ApiV2ResponseOfProductFilters:
      title: ApiV2ResponseOfProductFilters
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductFilters'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ApiV2ResponseOfProductFilter:
      title: ApiV2ResponseOfProductFilter
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductFilter'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ApiV2ResponseOfItemsCount:
      title: ApiV2ResponseOfItemsCount
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ItemsCount'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      x-examples: {}
    ItemsCount:
      title: ItemsCount
      x-stoplight:
        id: 24rvwnfgy7nbf
      type: object
      properties:
        itemsCount:
          type: integer
      description: Количество товаров
    FilterDescription:
      title: FilterDescription
      x-stoplight:
        id: 9jem6h2at5zsu
      type: object
      properties:
        name:
          type: string
        description:
          type: string
    ClientProductFiltrationContext:
      title: Context
      type: string
      enum:
        - ACCOUNT_PUBLISHED_OFFLINE_PRODUCTS