openapi: 3.0.3
x-stoplight:
  id: tgw59tbxihx92
info:
  title: Product Search API
  version: '1.0'
servers:
  - url: 'http://localhost:3000'
paths:
  /api/v2/products/search:
    post:
      summary: getAvailableFilters
      operationId: post-api-v2-products-search
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './api.v2.products.filter.yaml#/components/schemas/ApiV2ResponseOfProductFilters'
              examples: {}
      description: Получение доступных фильтров (+ опционально товаров) в зависимости от заданной фильтрации
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductFilterRequest'
            examples: {}
        description: ''
  /api/v2/products/search/info:
    post:
      summary: getAvailableFilterInfo
      operationId: post-api-v2-products-search-info
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './api.v2.products.filter.yaml#/components/schemas/ApiV2ResponseOfProductFilter'
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductFilterInfoRequest'
            examples: {}
        description: ''
  /api/v2/products/search/items:
    post:
      summary: getItems
      operationId: post-api-v2-products-search-items
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSearchPageOfProductDTO'
              examples: {}
        'default':
          description: "Поиск по продуктам"
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfSearchPageOfProductDTO'
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductFilterItemsRequest'
            examples: {}
        description: ''
    parameters: []
  /api/v2/products/search/count:
    post:
      summary: getItemsCount
      operationId: post-api-v2-products-search-count
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './api.v2.products.filter.yaml#/components/schemas/ApiV2ResponseOfItemsCount'
              examples: {}
        'default':
          description: "Получение кол-ва продуктов по фильтрам и поисковой фразе"
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'
      parameters:
        - schema:
            type: string
          in: query
          name: code
          description: Код фильтра
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductFilterInfoRequest'
            examples: {}
        description: ''
      description: ''
    parameters: []
  /api/v2/products/search/history:
    get:
      summary: getSearchHistory
      operationId: get-api-v2-products-search-get-history
      parameters:
        - name: categoryId
          in: query
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfString'
  /api/v2/products/search/suggestions:
    get:
      summary: getSuggestions
      operationId: get-api-v2-products-search-get-suggestions
      parameters:
        - name: userQuery
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfString'

components:
  schemas:
    SearchProductFilterInfoRequest:
      title: SearchProductFilterInfoRequest
      x-stoplight:
        id: wjt8znnhm9e4t
      type: object
      description: Запрос на получение доступных значений заданного фильтра
      properties:
        search:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/SearchQuery'
          description: |
            Пользовательский поисковой запрос
        filters:
          type: object
          additionalProperties:
            $ref: './api.v2.products.filter.yaml#/components/schemas/FilterValue'
          description: |
            Коллекция фильтров со значениями. Доступные среди прочих фильтры:
            * offlineOnly: true | false - выдать только товары, находящиеся offline | все товары
        hiddenFilters:
          type: object
          additionalProperties:
            $ref: './api.v2.products.filter.yaml#/components/schemas/FilterValue'
        presets:
          type: object
          additionalProperties:
            $ref: './api.v2.products.filter.yaml#/components/schemas/FilterValue'
          description: |
            Коллекция пресетов со значениями. Доступные среди прочих пресеты:
            * offlineOnly: true | false - выдать только товары, находящиеся offline | все товары
        baseCategory:
          type: integer
        countryId:
          type: integer
        currencyCode:
          type: string
        contexts:
          type: array
          items:
            $ref: './api.v2.products.filter.yaml#/components/schemas/ClientProductFiltrationContext'
    SearchProductFilterItemsRequest:
      title: SearchProductFilterItemsRequest
      x-stoplight:
        id: 1eu1xjjexsbho
      description: 'Запрос на получение списка товаров, удовлетворяющих условиям фильтрации'
      allOf:
        - $ref: '#/components/schemas/SearchProductFilterInfoRequest'
        - type: object
          properties:
            sorting:
              type: string
              default: NEW
            page:
              type: integer
              default: 1
            pageLength:
              type: integer
              default: 60
      x-examples: {}
    SearchProductFilterRequest:
      title: SearchProductFilterRequest
      x-stoplight:
        id: 1eu1xjjexsbho
      description: Запрос на получение доступных значений фильтра и товаров по фильтрам
      allOf:
        - $ref: '#/components/schemas/SearchProductFilterItemsRequest'
        - type: object
          properties:
            withAvailableValues:
              type: boolean
              default: false
            withItems:
              type: boolean
              default: false
      x-examples: {}

    SearchQuery:
      title: SearchQuery
      type: object
      properties:
        query:
          type: string
        useCorrection:
          type: boolean
          default: true
        suggestionNumber:
          type: integer
          format: int32
    Api2ResponseOfSearchPageOfProductDTO:
      title: Api2ResponseOfSearchPageOfProductDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/SearchPageOfProductDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    SuggestingResult:
      title: SuggestingResult
      type: object
      properties:
        values:
          type: array
          items:
            type: string
        id:
          type: string
    SuggestingHistoryResult:
      title: SuggestingHistoryResult
      type: object
      properties:
        values:
          type: array
          items:
            $ref: '#/components/schemas/HistoryDTO'
        id:
          type: string
    HistoryDTO:
      title: HistoryDTO
      type: object
      properties:
        search_value:
          type: string
        category_id:
          type: integer
          format: int64
    SearchPageOfProductDTO:
      title: SearchPageOfProductDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/ProductDTORes'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
        correctedValue:
          type: string
        queryHash:
          type: string

    Api2ResponseOfListOfString:
      title: Api2ResponseOfListOfString
      type: object
      properties:
        data:
          type: array
          items:
            type: string
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string