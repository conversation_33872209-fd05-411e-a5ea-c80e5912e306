openapi: 3.0.3
info:
  title: Oskelly passport API
  description: Generated DTO, Controller, Swagger Documentation, Client
  version: "1.0"
paths:
  /api/v2/account/id/profile:
    get:
      summary: Get profiles
      tags:
        - Profiles
      operationId: getProfiles
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetProfilesResponse"
    post:
      summary: Create profile
      tags:
        - Profiles
      operationId: createProfile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateProfileRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateProfileResponse"
    put:
      summary: Update profile
      tags:
        - Profiles
      operationId: updateProfile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProfileRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateProfileResponse"
    delete:
      summary: Delete profile
      tags:
        - Profiles
      operationId: deleteProfile
      parameters:
        - $ref: "#/components/parameters/ProfileIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                type: string
  /api/v2/account/id/profile/orders/filter:
    post:
      summary: Get orders
      tags:
        - Orders
      operationId: getOrders
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetOrderRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetOrderResponse"
  /api/v2/account/id/profile/orders:
    post:
      summary: Create order
      tags:
        - Orders
      operationId: createOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrderResponse"
    put:
      summary: Update order
      tags:
        - Orders
      operationId: updateOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrderResponse"
components:
  parameters:
    UserIdParam:
      in: query
      name: userId
      required: true
      schema:
        type: integer
        format: int64
    ProfileIdParam:
      in: query
      name: profileId
      required: true
      schema:
        type: string
    PassportIdParam:
      name: passportId
      in: query
      required: true
      schema:
        type: string
    OrderIdParam:
      name: orderId
      in: query
      required: true
      schema:
        type: string
  schemas:
    Inn:
      properties:
        number:
          type: string
    PassportRu:
      properties:
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
        seriesNumber:
          type: string
        issuedBy:
          type: string
        issuedAt:
          type: string
          format: date-time
    Phone:
      properties:
        phoneNumber:
          type: string
    Profile:
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: integer
          format: int64
        inn:
          $ref: "#/components/schemas/Inn"
        passportRu:
          $ref: "#/components/schemas/PassportRu"
        phone:
          $ref: "#/components/schemas/Phone"
    Order:
      properties:
        orderId:
          type: integer
          format: int64
        profileId:
          type: string
          format: uuid
        userId:
          type: integer
          format: int64
        inn:
          $ref: "#/components/schemas/Inn"
        passportRu:
          $ref: "#/components/schemas/PassportRu"
        phone:
          $ref: "#/components/schemas/Phone"
    CreateProfileRequest:
      properties:
        profile:
          $ref: "#/components/schemas/Profile"
    CreateProfileResponse:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Profile'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
    UpdateProfileRequest:
      properties:
        profile:
          $ref: "#/components/schemas/Profile"
    UpdateProfileResponse:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Profile'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
    GetOrderRequest:
      properties:
        orderIds:
          type: array
          items:
            type: integer
            format: int64
    GetOrderResponse:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          properties:
            orders:
             type: array
             items:
               $ref: "#/components/schemas/Order"
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    CreateOrderRequest:
      properties:
        order:
          $ref: "#/components/schemas/Order"
    CreateOrderResponse:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Order'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
    GetProfilesResponse:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          properties:
            profiles:
             type: array
             items:
               $ref: "#/components/schemas/Profile"
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string