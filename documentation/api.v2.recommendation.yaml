openapi: 3.0.3

info:
  title: Oskelly API v2, recommendation products
  description: Oskelly API v2, recommendation products
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

tags:
  - name: recommendation-products-controller-api-v-2
    description: Recommendation Products Api V 2

paths:
  /api/v2/recommendation/similar:
    get:
      tags:
        - recommendation-products-controller-api-v-2
        - similar
      summary: getSimilar
      operationId: getSimilarUsingGET
      parameters:
        - name: productId
          in: query
          required: true
          schema:
            type: integer
            format: int64
          description: Product id
        - name: limit
          in: query
          required: false
          schema:
            type: integer
          description: maximum products in result
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'

  /api/v2/recommendation/similarPage:
    get:
      tags:
        - recommendation-products-controller-api-v-2
        - similar
      summary: getSimilar
      operationId: getSimilarPageUsingGET
      parameters:
        - name: productId
          in: query
          required: true
          schema:
            type: integer
            format: int64
          description: Product id
        - name: pageNumber
          in: query
          required: false
          schema:
            type: integer
          description: page number
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
          description: page size
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfProductDTO'

  /api/v2/recommendation/recommended:
    get:
      tags:
        - recommendation-products-controller-api-v-2
        - recommended
      summary: getRecommended
      operationId: getRecommendedUsingGET
      parameters:
        - name: limit
          in: query
          required: false
          schema:
            type: integer
          description: maximum products in result
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'
  /api/v2/recommendation/popular:
    get:
      tags:
        - recommendation-products-controller-api-v-2
        - popular
      summary: getPopularProducts
      operationId: getPopularProductsUsingGET
      parameters:
        - name: limit
          in: query
          required: false
          schema:
            type: integer
          description: maximum products in result
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'