openapi: 3.0.3
info:
  title: Oskelly Main Service
  version: 2.2.1
servers:
  - url: http://127.0.0.1:8080
    description: Inferred Url
tags:
  - name: admin-sale-request-controller-api-v-2
    description: Admin Sale Request Controller Api V 2
  - name: sale-request-controller-api-v-2
    description: User Sale Request Controller Api V 2
paths:
  /api/v2/admin/saleRequests:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: createRequest
      operationId: createRequestUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CoreCreateSaleRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/items:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: createRequest
      operationId: filterSaleRequestsUsingPOST
      parameters:
        - name: descending
          in: query
          required: false
          schema:
            type: boolean
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: rowsPerPage
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: sortBy
          in: query
          required: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaleRequestFilter'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfAdminCoreSaleRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}:
    get:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: getById
      operationId: getByIdUsingGET
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
    patch:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: patchRequest
      operationId: patchRequestUsingPATCH
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CorePatchSaleRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/state:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: updateState
      operationId: updateStateUsingPOST
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CoreChangeStateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/product:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: createProduct
      operationId: createProductUsingPATCH
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CoreCreateSaleRequestProductDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestProductDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/product/{productId}:
    patch:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: patchProduct
      operationId: patchProductUsingPATCH
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          schema:
            type: integer
            format: int64
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CorePatchSaleRequestProductDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestProductDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/product/{productId}/confirm:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: confirmProduct
      operationId: confirmProductUsingPOST
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          schema:
            type: integer
            format: int64
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestProductDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/product/{productId}/decline:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: declineProduct
      operationId: declineProductUsingPOST
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          schema:
            type: integer
            format: int64
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CoreDeclineProductParamsDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestProductDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/product/{productId}/cancel-decision:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: cancelProductDecision
      operationId: cancelProductDecisionUsingPOST
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          schema:
            type: integer
            format: int64
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestProductDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/order:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: createSaleRequestOrder
      operationId: createSaleRequestOrder
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminCoreSaleRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/history:
    get:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: history
      operationId: getSaleRequestHistory
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCoreSaleRequestHistoryItemDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/admin/saleRequests/{requestId}/comments:
    post:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: Создает комментарий к заявке
      operationId: createSaleRequestComment
      parameters:
        - $ref: '#/components/parameters/requestId'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CoreCreateSaleRequestCommentDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCoreSaleRequestCommentDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    get:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: Выдает список комментариев по заявке
      operationId: getSaleRequestComments
      parameters:
        - $ref: '#/components/parameters/requestId'
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: rowsPerPage
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: sortBy
          in: query
          required: false
          schema:
            type: string
        - name: descending
          in: query
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfCoreSaleRequestCommentDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/admin/saleRequests/{requestId}/comments/{commentId}:
    delete:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: Удаляет коммент
      operationId: deleteSaleRequestComment
      parameters:
        - $ref: '#/components/parameters/requestId'
        - $ref: '#/components/parameters/commentId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: string
                default: OK
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v2/admin/saleRequests/{requestId}/comments/{commentId}/pin:
    patch:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: Закрепляет коммент
      operationId: pinSaleRequestComment
      parameters:
        - $ref: '#/components/parameters/requestId'
        - $ref: '#/components/parameters/commentId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCoreSaleRequestCommentDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v2/admin/saleRequests/{requestId}/comments/{commentId}/unpin:
    patch:
      tags:
        - admin-sale-request-controller-api-v-2
      summary: Открепляет коммент
      operationId: unpinSaleRequestComment
      parameters:
        - $ref: '#/components/parameters/requestId'
        - $ref: '#/components/parameters/commentId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCoreSaleRequestCommentDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v2/saleRequests:
    get:
      tags:
        - sale-request-controller-api-v-2
      summary: getAll
      operationId: getAllUsingGET
      parameters:
        - name: descending
          in: query
          required: false
          schema:
            type: boolean
        - name: page
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: rowsPerPage
          in: query
          required: false
          schema:
            type: integer
            format: int32
        - name: sortBy
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfCoreSaleRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
    post:
      tags:
        - sale-request-controller-api-v-2
      summary: createRequest
      operationId: createRequestUsingPOST_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CoreCreateSaleRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCoreSaleRequestDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/saleRequests/{requestId}:
    get:
      tags:
        - sale-request-controller-api-v-2
      summary: getById
      operationId: getByIdUsingGET_3
      parameters:
        - name: requestId
          in: path
          description: requestId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCoreSaleRequestDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/saleRequests/products/brands/available:
    get:
      tags:
        - sale-request-controller-api-v-2
      summary: getAvailableProductBrands
      operationId: getAvailableProductBrands
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBrandDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/saleRequests/products/category-tree/available:
    get:
      tags:
        - sale-request-controller-api-v-2
      summary: getAvailableProductCategoryTree
      operationId: getAvailableProductCategoryTree
      parameters:
        - name: brandId
          in: query
          description: brandId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfCategoryTree'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
  /api/v2/saleRequests/products/conditions/available:
    get:
      tags:
        - sale-request-controller-api-v-2
      summary: getAvailableProductConditions
      operationId: getAvailableProductConditions
      parameters:
        - name: brandId
          in: query
          description: brandId
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductConditionDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
components:
  parameters:
    requestId:
      name: requestId
      in: path
      description: ИД заявки
      required: true
      schema:
        type: integer
        format: int64
    commentId:
      name: commentId
      in: path
      description: ИД заявки
      required: true
      schema:
        type: integer
        format: int64
  schemas:
    Api2ResponseOfCoreSaleRequestDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CoreSaleRequestDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfCoreSaleRequestDTO
    Api2ResponseOfAdminCoreSaleRequestDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AdminCoreSaleRequestDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfAdminCoreSaleRequestDTO
    Api2ResponseOfCoreSaleRequestProductDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CoreSaleRequestProductDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfCoreSaleRequestProductDTO
    Api2ResponseOfAdminCoreSaleRequestProductDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AdminCoreSaleRequestProductDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfAdminCoreSaleRequestProductDTO
    Api2ResponseOfPageOfCoreSaleRequestDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfCoreSaleRequestDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfPageOfCoreSaleRequestDTO
    Api2ResponseOfPageOfAdminCoreSaleRequestDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfAdminCoreSaleRequestDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfPageOfAdminCoreSaleRequestDTO
    Api2ResponseOfCoreSaleRequestHistoryItemDTO:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CoreSaleRequestHistoryItemDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfCoreSaleRequestDTO
    Api2ResponseOfCoreSaleRequestCommentDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CoreSaleRequestCommentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfPageOfCoreSaleRequestCommentDTO:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfCoreSaleRequestCommentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    CoreCreateSaleRequestDTO:
      type: object
      properties:
        contactName:
          type: string
        description:
          type: string
        images:
          type: array
          items:
            type: string
        itemsNumber:
          type: integer
          format: int32
        phone:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/CoreCreateSaleRequestProductDTO'
        source:
          type: string
        userId:
          type: integer
          format: int64
          description: Пользователь, являющийся владельцем заявки (может не указываться, если владельцем заявки является текущий пользователь)
        bitrixDealId:
          type: integer
          format: int64
          description: ИД сделки по консьержу продавца в битриксе
      title: CoreCreateSaleRequestDTO
    CorePatchSaleRequestDTO:
      type: object
      properties:
        contactName:
          type: string
        description:
          type: string
        images:
          type: array
          items:
            type: string
        itemsNumber:
          type: integer
          format: int32
        phone:
          type: string
        source:
          type: string
        orderId:
          type: integer
          format: int64
        bitrixDealId:
          type: integer
          format: int64
          description: ИД сделки по консьержу продавца в битриксе
      title: CorePatchSaleRequestDTO
    CoreCreateSaleRequestProductDTO:
      type: object
      properties:
        brandId:
          type: integer
          format: int64
        categoryId:
          type: integer
          format: int64
        conditionId:
          type: integer
          format: int64
        description:
          type: string
        fromPrice:
          type: number
        image:
          type: string
        toPrice:
          type: number
        currentPrice:
          type: number
        sellerPrice:
          type: number
        customCommission:
          type: boolean
      title: CoreCreateSaleRequestProductDTO
    CorePatchSaleRequestProductDTO:
      type: object
      properties:
        brandId:
          type: integer
          format: int64
        categoryId:
          type: integer
          format: int64
        conditionId:
          type: integer
          format: int64
        description:
          type: string
        fromPrice:
          type: number
        image:
          type: string
        toPrice:
          type: number
        currentPrice:
          type: number
        sellerPrice:
          type: number
        customCommission:
          type: boolean
      title: CorePatchSaleRequestProductDTO
    CoreSaleRequestDTO:
      type: object
      properties:
        contactName:
          type: string
        createTime:
          type: string
          format: date-time
        description:
          type: string
        id:
          type: integer
          format: int64
        images:
          type: array
          items:
            $ref: '#/components/schemas/CoreSaleRequestImageDTO'
        itemsNumber:
          type: integer
          format: int32
        phone:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/CoreSaleRequestProductDTO'
        source:
          type: string
        state:
          $ref: '#/components/schemas/SaleRequestState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestStateDTO'
        totalAmount:
          type: number
        user:
          $ref: './partial.yaml#/components/schemas/UserDTO'
        order:
          $ref: '#/components/schemas/CoreSaleRequestOrderDTO'
        bitrixDealId:
          type: integer
          format: int64
          description: ИД сделки по консьержу продавца в битриксе
        bitrixDealUrl:
          type: string
          description: Url сделки по консьержу продавца в битриксе
      title: CoreSaleRequestDTO
    AdminCoreSaleRequestDTO:
      type: object
      properties:
        contactName:
          type: string
        createTime:
          type: string
          format: date-time
        description:
          type: string
        id:
          type: integer
          format: int64
        images:
          type: array
          items:
            $ref: '#/components/schemas/CoreSaleRequestImageDTO'
        itemsNumber:
          type: integer
          format: int32
        phone:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/AdminCoreSaleRequestProductDTO'
        source:
          type: string
        state:
          $ref: '#/components/schemas/SaleRequestState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestStateDTO'
        totalAmount:
          type: number
        user:
          $ref: './partial.yaml#/components/schemas/UserDTO'
        order:
          $ref: '#/components/schemas/CoreSaleRequestOrderDTO'
        bitrixDealId:
          type: integer
          format: int64
          description: ИД сделки по консьержу продавца в битриксе
        bitrixDealUrl:
          type: string
          description: Url сделки по консьержу продавца в битриксе
      title: AdminCoreSaleRequestDTO
    CoreSaleRequestProductDTO:
      type: object
      properties:
        brand:
          $ref: './partial.yaml#/components/schemas/BrandDTO'
        category:
          $ref: './partial.yaml#/components/schemas/CategoryDTO'
        condition:
          $ref: './partial.yaml#/components/schemas/ProductConditionDTO'
        createTime:
          type: string
          format: date-time
        declineReason:
          type: string
        fromPrice:
          type: number
        id:
          type: integer
          format: int64
        image:
          $ref: '#/components/schemas/CoreSaleRequestImageDTO'
        product:
          $ref: './partial.yaml#/components/schemas/ProductDTO'
        state:
          $ref: '#/components/schemas/SaleRequestProductState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestProductStateDTO'
        toPrice:
          type: number
        order:
          $ref: '#/components/schemas/CoreSaleRequestOrderDTO'
      title: CoreSaleRequestProductDTO
    AdminCoreSaleRequestProductDTO:
      type: object
      properties:
        brand:
          $ref: './partial.yaml#/components/schemas/BrandDTO'
        category:
          $ref: './partial.yaml#/components/schemas/CategoryDTO'
        condition:
          $ref: './partial.yaml#/components/schemas/ProductConditionDTO'
        createTime:
          type: string
          format: date-time
        declineReason:
          type: string
        fromPrice:
          type: number
        id:
          type: integer
          format: int64
        image:
          $ref: '#/components/schemas/CoreSaleRequestImageDTO'
        product:
          $ref: './partial.yaml#/components/schemas/ProductDTOV3'
        state:
          $ref: '#/components/schemas/SaleRequestProductState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestProductStateDTO'
        toPrice:
          type: number
        order:
          $ref: '#/components/schemas/CoreSaleRequestOrderDTO'
      title: CoreSaleRequestProductDTO
    CoreSaleRequestOrderDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        state:
          type: string
        orderStateTitle:
          type: string
        status:
          type: string
        orderStatusTitle:
          type: string
        stage:
          type: string
        finalAmount:
          type: number
        createTime:
          type: string
          format: date-time
      title: CoreSaleRequestOrderDTO
    PageOfCoreSaleRequestDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CoreSaleRequestDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
      title: PageOfCoreSaleRequestDTO
    PageOfAdminCoreSaleRequestDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AdminCoreSaleRequestDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
      title: PageOfAdminCoreSaleRequestDTO
    PageOfCoreSaleRequestCommentDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CoreSaleRequestCommentDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    SaleRequestFilter:
      type: object
      properties:
        ownerUserIds:
          type: array
          items:
            type: integer
            format: int64
        states:
          type: array
          items:
            $ref: '#/components/schemas/SaleRequestState'
        idContains:
          type: string
    SaleRequestProductStateDTO:
      type: object
      properties:
        code:
          type: string
        title:
          type: string
      title: SaleRequestProductStateDTO
    SaleRequestStateDTO:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
        title:
          type: string
      title: SaleRequestStateDTO
    CoreSaleRequestHistoryItemDTO:
      type: object
      properties:
        id:
          type: integer
          description: History item id
          format: int64
          example: 10
        enteredByUser:
          $ref: './partial.yaml#/components/schemas/UserDTO'
        enteredTime:
          type: string
          description: Date of enter into state
          format: date-time
        state:
          $ref: '#/components/schemas/SaleRequestState'
        stateInfo:
          $ref: '#/components/schemas/SaleRequestStateDTO'
    CoreChangeStateRequestDTO:
      type: object
      properties:
        state:
          $ref: '#/components/schemas/SaleRequestState'
      title: CoreChangeStateRequestDTO
    CoreDeclineProductParamsDTO:
      type: object
      properties:
        reason:
          type: string
      title: CoreChangeStateRequestDTO
    CoreSaleRequestImageDTO:
      type: object
      properties:
        url:
          type: string
        path:
          type: string
      title: CoreSaleRequestImageDTO
    CoreCreateSaleRequestCommentDTO:
      type: object
      properties:
        text:
          type: string
          minLength: 1
      required:
        - text
    CoreSaleRequestCommentDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        text:
          type: string
          minLength: 1
        createTime:
          type: string
          format: date-time
        user:
          $ref: './partial.yaml#/components/schemas/UserDTO'
        pinned:
          type: boolean
      required:
        - text
        - createTime
        - user
        - pinned
    SaleRequestState:
      type: string
      enum:
        - CREATED
        - IN_PROGRESS
        - CONFIRMED
        - DECLINED
    SaleRequestProductState:
      type: string
      enum:
        - CREATED
        - IN_PROGRESS
        - CONFIRMED
        - DECLINED