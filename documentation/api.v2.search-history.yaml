openapi: 3.0.0
info:
  title: Oskelly Search History API
  description: API для работы с сервисом истории поиска.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: localhost
tags:
  - name: search-history-controller
    description: Search History Controller API
paths:
  /api/v2/search/history:
    get:
      operationId: getSearchHistory
      parameters:
        - in: query
          name: limit
          schema:
            type: integer
            format: int
          required: false
        - in: query
          name: type
          required: false
          schema:
            type: string
      tags:
        - search-history-controller
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfSearchHistory'
    post:
      operationId: createSearchHistoryItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSearchHistoryRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2Response'
          description: OK
      tags:
        - search-history-controller
  /api/v2/search/history/delete:
    post:
      operationId: deleteSearchHistoryItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteSearchHistoryRequest'
        required: true
      tags:
        - search-history-controller
      responses:
        '200':
          description: OK
  /api/v2/search/history/delete/all:
    post:
      operationId: deleteSearchHistory
      tags:
        - search-history-controller
      responses:
        '200':
          description: OK

components:
  schemas:
    Api2Response:
      type: object
      properties:
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseListOfSearchHistory:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/SearchHistory'
    SearchHistory:
      type: object
      properties:
        data:
          oneOf:
            - $ref: '#/components/schemas/UserSearchHistoryData'
            - $ref: '#/components/schemas/ProductSearchHistoryData'
        query:
          type: string
        type:
          type: string
      required:
        - query
        - type
    UserSearchHistoryData:
      type: object
      properties:
        id:
          type: integer
          format: int64
        nickname:
          type: string
          description: nickname в oskelly
        avatarPath:
          type: string
        communityBadge:
          $ref: './partial.yaml#/components/schemas/CommunityBadge'
        isTrusted:
          type: boolean
        productsCount:
          type: integer
      required:
        - id
        - nickname
        - productsCount
        - isTrusted
    ProductSearchHistoryData:
      type: object
      properties:
        baseCategory:
          type: integer
          format: int64
    CreateSearchHistoryRequest:
      type: object
      properties:
        data:
          type: object
          additionalProperties:
            type: object
        query:
          type: string
        type:
          type: string
      required:
        - query
        - type
    DeleteSearchHistoryRequest:
      type: object
      properties:
        type:
          type: string
        query:
          type: string
      required:
        - type
