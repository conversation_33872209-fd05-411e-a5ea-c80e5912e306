openapi: 3.0.0
info:
  title: Oskelly Search API
  description: API для поиска.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: localhost
tags:
  - name: search-controller-api
    description: Search Controller API
paths:
  /api/v2/search/tabs:
    get:
      summary: Плашки для поиска
      operationId: getSearchTabs
      parameters:
        - name: contextType
          in: query
          description: Контекст поиска
          required: false
          schema:
            type: string
            enum:
              - DEFAULT
              - SOCIAL
      tags:
        - search-controller-api
      responses:
        '200':
          description: Список плашек
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfSearchTabDTO'
components:
  schemas:
    Api2ResponseOfListOfSearchTabDTO:
      title: Api2ResponseOfBonusesBonusCardInfoDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SearchTabDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    SearchTabDTO:
      description: Плашка поиска
      type: object
      properties:
        code:
          nullable: false
          type: string
        name:
          nullable: false
          type: string
