openapi: 3.0.3

info:
  title: Oskelly API v2, social
  description: Oskelly API v2, social
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

paths:
  /api/v2/social/media:
    post:
      summary: Загрузить медиа (иллюстрации | видео)
      operationId: uploadMedia
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                media:
                  type: array
                  items:
                    type: string
                    format: binary
                metaInfo:
                  $ref: '#/components/schemas/MediaUploadMetaInfo'
              required:
                - media
                - metaInfo
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfMedia'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/media/upload-urls:
    post:
      summary: Создать список url для загрузки медиа
      operationId: createUploadUrls
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMediaUploadUrlsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseMediaUploadUrls'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/media/process:
    post:
      summary: Запустить обработку загруженных в файловый сервис медиа
      operationId: processMedia
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProcessMediaRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfMedia'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/media/{id}/similar-products/{token}:
    get:
      summary: Вернуть похожие продукты
      parameters:
        - in: path
          name: id
          schema:
            type: integer
            format: int64
          required: true
        - in: path
          name: token
          schema:
            type: string
          required: true
      operationId: getSimilarProducts
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchProductPhotoResults'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    post:
      summary: Вернуть похожие продукты
      parameters:
        - in: path
          name: id
          schema:
            type: integer
            format: int64
          required: true
        - in: path
          name: token
          schema:
            type: string
          required: true
      operationId: getSimilarProductsByPost
      deprecated: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchProductPhotoResults'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts:
    post:
      summary: Создать и опубликовать пост
      operationId: createPost
      parameters:
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePostRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePostExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    get:
      summary: Вернуть коллекцию постов с учетом необязательных параметров
      operationId: getPosts
      parameters:
        - in: query
          name: authorId
          description: Пользователь-автор
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfPostFeedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/posts/{postId}:
    parameters:
      - $ref: '#/components/parameters/PostId'
    get:
      summary: Вернуть пост по ИД
      operationId: getPost
      parameters:
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePostExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    put:
      deprecated: true
      summary: Обновить существующий пост (сокращенная версия)
      operationId: updatePost
      parameters:
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePostRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePostExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    delete:
      summary: Удалить пост по ИД
      operationId: deletePost
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/full:
    parameters:
      - $ref: '#/components/parameters/PostId'
    put:
      summary: Обновить существующий пост (полная версия)
      operationId: updatePostFull
      parameters:
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePostFullRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePostExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/like:
    patch:
      summary: Лайкнуть пост от имени текущего пользователя
      operationId: likePost
      parameters:
        - $ref: '#/components/parameters/PostId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseLikeState'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/unlike:
    patch:
      summary: Снять лайк с поста от имени текущего пользователя
      operationId: unlikePost
      parameters:
        - $ref: '#/components/parameters/PostId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseLikeState'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/mentioned-users:
    get:
      summary: Вернуть коллекцию упомянутых в посте пользователей
      operationId: getPostMentionedUsers
      parameters:
        - $ref: '#/components/parameters/PostId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfUser'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/liked-users:
    get:
      summary: Вернуть коллекцию лайкнувших пост пользователей
      operationId: getLikedUsers
      parameters:
        - $ref: '#/components/parameters/PostId'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfUser'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/ranked:
    get:
      summary: |
        Вернуть коллекцию постов с пагинацией по токену и ранжированием, аналогичным ранжированию в ленте.
        По умолчанию возвращаются посты со статусом PUBLISHED, у который hidden == false, просмотренные посты учитываются.
        Поведение по умолчанию переопределяется указанием параметра context.
        Метод заменяет собой метод get /api/v2/social/feed/posts
      operationId: getRankedPosts
      parameters:
        - in: query
          name: id
          description: ИД поста
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: authorId
          description: ИД автора поста
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: tagId
          description: ИД тега
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: productId
          description: Товар, с которым связан пост
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: publishedAtFrom
          description: Нижняя граница даты публикации
          required: false
          schema:
            type: string
            format: date-time
        - in: query
          name: publishedAtTo
          description: Верхняя граница даты публикации
          required: false
          schema:
            type: string
            format: date-time
        - in: query
          name: context
          description: |
            Контекст выдачи постов, возможные значения:
            * FEED_POST - пост в ленте
            * PRODUCT_CARD - карточка товара
            * FEED_POSTS_COLLECTION - полка постов в ленте
            * HOME_POSTS_COLLECTION - полка постов на главной
          required: false
          schema:
            $ref: '#/components/schemas/RankedPostReturningContext'
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
        - $ref: '#/components/parameters/NextAnchor'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTokenizedPageOfPostFeedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/posts/ranked/search:
    get:
      summary: Искать посты
      operationId: searchRankedPosts
      parameters:
        - in: query
          name: query
          description: строка поиска
          required: false
          schema:
            type: string
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
        - $ref: '#/components/parameters/NextAnchor'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTokenizedPageOfPostSearchView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/posts/{postId}/comments:
    parameters:
      - $ref: '#/components/parameters/PostId'
    get:
      summary: Вернуть коллекцию комментариев к посту
      operationId: getPostComments
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/Feature'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfComment'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    post:
      summary: Добавить комментарий к посту
      operationId: createPostComment
      parameters:
        - $ref: '#/components/parameters/Feature'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommentRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseComment'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/comments/extended-result:
    parameters:
      - $ref: '#/components/parameters/PostId'
      - $ref: '#/components/parameters/Feature'
    post:
      summary: Добавить комментарий к посту с возвратом структуры с доп полями
      operationId: createPostCommentWithExtendedResult
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommentRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCommentCreationExtendedResult'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/comments/{commentId}:
    parameters:
      - $ref: '#/components/parameters/PostId'
      - $ref: '#/components/parameters/CommentId'
    delete:
      summary: Удалить комментарий к посту
      operationId: deletePostComment
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/comments/{commentId}/extended-result:
    parameters:
      - $ref: '#/components/parameters/PostId'
      - $ref: '#/components/parameters/CommentId'
      - $ref: '#/components/parameters/Feature'
    delete:
      summary: Удалить комментарий к посту с возвратом структуры с доп полями
      operationId: deletePostCommentWithExtendedResult
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCommentDeletionExtendedResult'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/shared:
    patch:
      summary: пометить, что постом поделились
      operationId: markPostAsShared
      parameters:
        - $ref: '#/components/parameters/PostId'
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/{postId}/viewed:
    patch:
      summary: пометить пост просмотренным
      operationId: markPostAsViewed
      parameters:
        - $ref: '#/components/parameters/PostId'
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/viewed:
    patch:
      summary: пометить посты просмотренными
      operationId: markPostsAsViewed
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                format: int64
        required: true
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/posts/products:
    get:
      summary: Вернуть коллекцию товаров к посту
      operationId: getPostComments
      parameters:
        - in: query
          name: postId
          description: ИД поста
          required: true
          schema:
            type: integer
            format: int64
        - $ref: '#/components/parameters/CurrencyCode'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfProductDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/story-items:
    post:
      summary: Создать и опубликовать элемент стори
      operationId: createStoryItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStoryItemRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseStoryItem'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/story-items/{storyItemId}:
    delete:
      summary: Удалить элемент стори по ИД
      operationId: deleteStoryItem
      parameters:
        - $ref: '#/components/parameters/StoryItemId'
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/story-items/{storyItemId}/viewed:
    patch:
      summary: Пометить элемент стори просмотренным
      operationId: markStoryItemAsViewed
      parameters:
        - $ref: '#/components/parameters/StoryItemId'
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/story-items/{storyItemId}/like:
    patch:
      summary: Лайкнуть элемент стори от имени текущего пользователя
      operationId: likeStoryItem
      parameters:
        - $ref: '#/components/parameters/StoryItemId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseLikeState'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/story-items/{storyItemId}/unlike:
    patch:
      summary: Снять лайк с элемента стори от имени текущего пользователя
      operationId: likeStoryItem
      parameters:
        - $ref: '#/components/parameters/StoryItemId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseLikeState'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/story-items/{storyItemId}/viewed-users:
    get:
      summary: Вернуть коллекцию пользователей, просмотревших элемент стори, с информацией о лайке
      operationId: getStoryItemViewedUsers
      parameters:
        - $ref: '#/components/parameters/StoryItemId'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfStoryItemViewedUser'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/feed/posts:
    get:
      deprecated: true
      summary: Вернуть коллекцию постов для ленты. Метод заменен методом get /api/v2/social/posts/ranked.
      operationId: getFeedPosts
      parameters:
        - in: query
          name: id
          description: ИД поста
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: authorId
          description: ИД автора поста
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: tagId
          description: ИД тега
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: productId
          description: Товар, с которым связан пост
          required: false
          schema:
            type: integer
            format: int64
        - in: query
          name: considerViewed
          description: Сдвинуть просмотренные вниз
          required: false
          schema:
            type: integer
            format: int64
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/NextAnchor'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTokenizedPageOfPostFeedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/feed/stories:
    get:
      summary: Вернуть коллекцию сторис для ленты
      operationId: getFeedStories
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfStoryShortView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/feed/stories/extended:
    get:
      summary: Вернуть коллекцию стори расширенной структуры для ленты
      operationId: getFeedExtendedStories
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfStory'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/feed/tags:
    get:
      summary: Вернуть коллекцию тегов (тем) для ленты
      operationId: getFeedTags
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfTagShortView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/feed/sections:
    get:
      summary: Вернуть коллекцию рубрик ленты
      operationId: getFeedSections
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/feed/sections/{feedSectionCode}:
    get:
      summary: Вернуть рубрику ленты по коду
      operationId: getFeedSection
      parameters:
        - $ref: '#/components/parameters/FeedSectionCode'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/feed:
    get:
      summary: Вернуть ленту
      description: Лента состоит из блоков разных типов, каждый тип блока имеет специфичные характеристики
      operationId: getFeed
      parameters:
        - in: query
          name: sectionCode
          schema:
            type: string
          required: false
        - in: query
          name: tagId
          schema:
            type: integer
            format: int64
          required: false
        - in: query
          name: forceFirstPostId
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: excludedPostId
          schema:
            type: array
            items:
              type: integer
              format: int64
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
        - $ref: '#/components/parameters/NextAnchor'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTokenizedPageOfFeedItem'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/discovery:
    get:
      summary: Вернуть элементы для дискавери
      operationId: getDiscovery
      parameters:
        - $ref: '#/components/parameters/NextAnchor'
        - $ref: '#/components/parameters/PageSize'
        - in: query
          name: sectionCode
          schema:
            type: string
          required: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTokenizedPageOfDiscoveryItem'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/discovery/posts/{postId}/similar/by-image:
    get:
      summary: Вернуть коллекцию похожих элементов по медиа поста
      operationId: getSimilarItems
      parameters:
        - $ref: '#/components/parameters/PostId'
        - in: query
          name: mediaId
          required: false
          schema:
            type: integer
            format: int64
        - $ref: '#/components/parameters/NextAnchor'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTokenizedPageOfDiscoveryItem'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/tags/{tagId}:
    get:
      summary: Вернуть тег по ИД
      operationId: getTag
      parameters:
        - $ref: '#/components/parameters/TagId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTagExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/tags/extended:
    get:
      summary: Вернуть коллекцию тегов (тем). Теги по умолчанию отсортированы по количеству постов в обратном порядке.
      operationId: getExtendedTags
      parameters:
        - in: query
          name: name
          schema:
            type: string
        - in: query
          name: withCalcIsBound
          required: false
          schema:
            type: boolean
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfTagExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/social/users/{userId}/story:
    get:
      summary: Вернуть стори пользователя
      operationId: getUserStory
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseStoryShortView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/users/{userId}/story/extended:
    get:
      summary: Вернуть стори расширенной структуры пользователя
      operationId: getUserExtendedStory
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseStory'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/users/{userId}/favorites/posts:
    get:
      summary: Вернуть коллекцию избранных постов пользователя
      operationId: getFavoritePosts
      parameters:
        - $ref: '#/components/parameters/UserId'
        - $ref: '#/components/parameters/CountryId'
        - $ref: '#/components/parameters/CurrencyCode'
        - $ref: '#/components/parameters/Feature'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfFavoritePost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/social/users/favorites/posts/{postId}:
    parameters:
      - $ref: '#/components/parameters/PostId'
    post:
      summary: Добавить пост в избранное от имени текущего пользователя
      operationId: addPostToFavorites
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    delete:
      summary: Убрать пост из избранного от имени текущего пользователя
      operationId: removePostToFavorites
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
  /api/v2/social/users/tags/{tagId}:
    parameters:
      - $ref: '#/components/parameters/TagId'
    post:
      summary: Подписаться текущему пользователю на тег
      operationId: bindTagToUser
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    delete:
      summary: Убрать подписку на тег текущему пользователю
      operationId: unbindTagFromUser
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
  /api/v2/social/users/{userId}/tags:
    get:
      summary: Вернуть коллекцию тегов, на которые подписан пользователь
      operationId: getUserTags
      parameters:
        - $ref: '#/components/parameters/UserId'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfTagExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  # Admin paths:

  /api/v2/admin/social/media:
    post:
      summary: Загрузить медиа (иллюстрации | видео)
      operationId: uploadAdminMedia
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                media:
                  type: array
                  items:
                    type: string
                    format: binary
                metaInfo:
                  $ref: '#/components/schemas/MediaUploadMetaInfo'
              required:
                - media
                - metaInfo
            encoding:
              media:
                contentType: application/octet-stream
              metaInfo:
                contentType: application/json
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfMedia'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/comments:
    get:
      operationId: getAdminComments
      parameters:
        - in: query
          name: pageNumber
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: pageSize
          required: false
          schema:
            type: integer
            format: int32
        - in: query
          name: sort
          required: false
          schema:
            $ref: '#/components/schemas/CommentSortingOption'
        - in: query
          name: search
          description: Строка поиска
          required: false
          schema:
            type: string
        - in: query
          name: authorId
          description: ИД автора
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfAdminCommentView'
          description: OK

  /api/v2/admin/social/comments/count:
    get:
      operationId: getAdminCommentsCount
      parameters:
        - in: query
          name: authorId
          description: ИД автора
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseLong'
          description: OK

  /api/v2/admin/social/comments/delete:
    patch:
      operationId: deleteCommentsByAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteCommentsByAdminRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminComment'
          description: OK

  /api/v2/admin/social/comments/publishing/users:
    get:
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfShortUser'
          description: OK

  /api/v2/admin/social/posts:
    get:
      summary: Вернуть коллекцию постов с учетом необязательных параметров
      operationId: getAdminPosts
      parameters:
        # Вкладка Лента:                  status == PUBLISHED and hidden = false and deleted == false
        # Вкладка Требует согласования:   status == NEED_MODERATION and hidden = false and deleted == false
        # Вкладка Скрытые:                hidden == true and deleted == false
        # Вкладка Отклоненные:            status == REJECTED and hidden = false and deleted == false
        # Вкладка Удалено:                deleted == true
        # Вкладка В обработке:            status == CREATING and hidden = false and deleted == false
        - in: query
          name: status
          description: Статус
          required: false
          schema:
            $ref: '#/components/schemas/PostStatus'
        - in: query
          name: hidden
          description: Флаг скрытости
          required: false
          schema:
            type: boolean
            default: false
        - in: query
          name: deleted
          description: Флаг удаленности
          required: false
          schema:
            type: boolean
            default: false
        - in: query
          name: authorId
          description: ИД автора
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: publishedAtFrom
          description: Нижняя граница даты публикации
          required: false
          schema:
            type: string
            format: date-time
        - in: query
          name: publishedAtTo
          description: Верхняя граница даты публикации
          required: false
          schema:
            type: string
            format: date-time
        - in: query
          name: tagId
          description: ИД тега
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: productId
          description: ИД продукта
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: linkSet
          description: Есть ли ссылка
          required: false
          schema:
            type: boolean
        - in: query
          name: boostingSet
          description: Есть ли бустинг
          required: false
          schema:
            type: boolean
        - in: query
          name: search
          description: Строка поиска
          required: false
          schema:
            type: string
        - in: query
          name: id
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
        - in: query
          name: sort
          description: Сортировка
          required: false
          schema:
            $ref: '#/components/schemas/PostWithExtraDataSortingOption'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/posts/count:
    get:
      summary: Вернуть количество постов с учетом необязательных параметров
      operationId: getAdminPostsCount
      parameters:
        # Вкладка Лента:                  status == PUBLISHED and hidden = false and deleted == false
        # Вкладка Требует согласования:   status == NEED_MODERATION and hidden = false and deleted == false
        # Вкладка Скрытые:                hidden == true and deleted == false
        # Вкладка Отклоненные:            status == REJECTED and hidden = false and deleted == false
        # Вкладка Удалено:                deleted == true
        # Вкладка В обработке:            status == CREATING and hidden = false and deleted == false
        - in: query
          name: status
          description: Статус
          required: false
          schema:
            $ref: '#/components/schemas/PostStatus'
        - in: query
          name: hidden
          description: Флаг скрытости
          required: false
          schema:
            type: boolean
            default: false
        - in: query
          name: deleted
          description: Флаг удаленности
          required: false
          schema:
            type: boolean
            default: false
        - in: query
          name: authorId
          description: ИД автора
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseLong'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/posts/list/short:
    get:
      parameters:
        - in: query
          name: query
          required: false
          schema:
            type: string
        - in: query
          name: id
          required: false
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfAdminShortPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/posts/{postId}:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Обновить данные поста
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchPostByAdminRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/approve:
    patch:
      operationId: approvePostsByAdmin
      parameters:
        - in: query
          name: postId
          required: true
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminPost'
          description: OK

  /api/v2/admin/social/posts/delete:
    patch:
      operationId: deletePostsByAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeletePostsByAdminRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminPost'
          description: OK

  /api/v2/admin/social/posts/hide:
    patch:
      operationId: hidePostsByAdmin
      parameters:
        - in: query
          name: postId
          required: true
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminPost'
          description: OK

  /api/v2/admin/social/posts/reject:
    patch:
      operationId: rejectPostsByAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RejectPostsByAdminRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminPost'
          description: OK

  /api/v2/admin/social/posts/restore:
    patch:
      operationId: restorePostsByAdmin
      parameters:
        - in: query
          name: postId
          required: true
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminPost'
          description: OK

  /api/v2/admin/social/posts/show:
    patch:
      operationId: showPostsByAdmin
      parameters:
        - in: query
          name: postId
          required: true
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfOperationResultContainerAdminPost'
          description: OK

  /api/v2/admin/social/posts/{postId}/delete:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Удалить пост по идентификатору
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeletePostByAdminRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/restore:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Восстановить пост по идентификатору
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/reject:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Отклонить пост по идентификатору
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RejectPostByAdminRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/approve:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Согласовать пост по идентификатору
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/hide:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Скрыть пост по идентификатору
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/history:
    parameters:
      - $ref: '#/components/parameters/PostId'
      - in: query
        name: sort
        description: Сортировка
        required: false
        schema:
          $ref: '#/components/schemas/PostHistorySortingOption'
    get:
      description: Получить историю изменения поста
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfPostHistory'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/show:
    parameters:
      - $ref: '#/components/parameters/PostId'
    patch:
      description: Показать пост (отменить скрытие поста) по идентификатору
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/short:
    parameters:
      - $ref: '#/components/parameters/PostId'
    get:
      description: Получить пост по идентификатору
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminShortPost'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/comments:
    parameters:
      - $ref: '#/components/parameters/PostId'
    get:
      summary: Вернуть коллекцию комментариев к посту
      operationId: getAdminPostComments
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfAdminComment'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommentByAdminRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminComment'
          description: OK

  /api/v2/admin/social/posts/{postId}/comments/{commentId}:
    parameters:
      - $ref: '#/components/parameters/PostId'
      - $ref: '#/components/parameters/CommentId'
    delete:
      summary: Удалить комментарий к посту
      operationId: deleteAdminPostComment
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminComment'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/comments/{commentId}/restore:
    parameters:
      - $ref: '#/components/parameters/PostId'
      - $ref: '#/components/parameters/CommentId'
    patch:
      summary: Восстановить удаленный комментарий к посту
      operationId: restoreAdminPostComment
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminComment'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/comments/media:
    parameters:
      - $ref: '#/components/parameters/PostId'
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminCommentMediaUploadInfo'
        required: true
      responses:
        '200':
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfMedia'
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/posts/{postId}/activity:
    get:
      summary: Вернуть активность по посту
      operationId: getAdminPostActivity
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/PostId'
        - in: query
          name: type
          schema:
            $ref: '#/components/schemas/ActivityType'
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfAdminPostActivity'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/posts/{postId}/rating/explanation:
    get:
      summary: Вернуть описание расчета формула
      operationId: getAdminPostRatingExplanation
      parameters:
        - $ref: '#/components/parameters/PostId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminPostRatingExplanation'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/users/{userId}/activity/posts:
    get:
      summary: Вернуть активность пользователя по постам
      operationId: getAdminUserPostsActivity
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
        - $ref: '#/components/parameters/UserId'
        - in: query
          name: type
          schema:
            $ref: '#/components/schemas/ActivityType'
          required: true
        - in: query
          name: search
          schema:
            type: string
          required: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfAdminUserPostsActivity'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/users/{userId}/activity/posts/count:
    get:
      summary: Вернуть каунтер активности пользователя по постам
      operationId: getActivityCount
      parameters:
        - $ref: '#/components/parameters/UserId'
        - in: query
          name: type
          schema:
            $ref: '#/components/schemas/ActivityType'
          required: true
        - in: query
          name: search
          schema:
            type: string
          required: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseLong'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/configured-feed-sections:
    get:
      summary: Вернуть коллекцию рубрик ленты
      operationId: getConfiguredFeedSections
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfConfiguredFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    post:
      summary: Создать рубрику ленты
      operationId: createConfiguredFeedSection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConfiguredFeedSectionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseConfiguredFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/configured-feed-sections/{configuredFeedSectionId}:
    parameters:
      - $ref: '#/components/parameters/ConfiguredFeedSectionId'
    get:
      summary: Вернуть рубрику по ИД
      operationId: getConfiguredFeedSection
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseConfiguredFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    put:
      summary: Обновить существующий рубрику ленты
      operationId: updateConfiguredFeedSection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConfiguredFeedSectionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseConfiguredFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    delete:
      summary: Удалить рубрику ленты по ИД
      operationId: deleteConfiguredFeedSection
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/configured-feed-sections/{configuredFeedSectionId}/move:
    parameters:
      - $ref: '#/components/parameters/ConfiguredFeedSectionId'
    patch:
      summary: Переместить рубрику
      operationId: moveConfiguredFeedSection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MoveConfiguredFeedSectionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseConfiguredFeedSection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/feed/objects-collections:
    get:
      summary: Вернуть коллекцию полок объектов
      operationId: getFeedObjectsCollections
      parameters:
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfAdminFeedObjectsCollection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    post:
      summary: Создать полку объектов
      operationId: createFeedObjectsCollection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFeedObjectsCollectionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminFeedObjectsCollection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/feed/objects-collections/{feedObjectsCollectionId}:
    parameters:
      - $ref: '#/components/parameters/FeedObjectsCollectionId'
    put:
      summary: Обновить существующую полку объектов
      operationId: updateFeedObjectsCollection
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFeedObjectsCollectionRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseAdminFeedObjectsCollection'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
    delete:
      summary: Удалить полку объектов по ИД
      operationId: deleteFeedObjectsCollection
      responses:
        '200':
          description: OK
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/tags:
    post:
      summary: Создать тег
      operationId: createTag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTagRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTagAdminExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/tags/extended:
    get:
      summary: Вернуть страницу тегов для админки
      operationId: getAdminTagsExtended
      parameters:
        - in: query
          name: name
          schema:
            type: string
        - in: query
          name: sort
          required: false
          schema:
            $ref: '#/components/schemas/TagSortingOption'
        - $ref: '#/components/parameters/PageNumber'
        - $ref: '#/components/parameters/PageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageOfTagAdminExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/tags/extended/list:
    get:
      summary: Вернуть коллекцию тегов для админки по идентификаторам
      operationId: getAdminTagsExtendedList
      parameters:
        - in: query
          name: id
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfTagAdminExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/tags/{tagId}/name:
    parameters:
      - $ref: '#/components/parameters/TagId'
    patch:
      summary: Обновить существующий тег
      operationId: updateTagName
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTagNameRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTagAdminExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/tags/{tagId}/pinned/order:
    parameters:
      - $ref: '#/components/parameters/TagId'
    patch:
      summary: Изменить позицию закрепленного тега
      operationId: updatePinnedTagOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePinnedTagOrderRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTagAdminExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/tags/{tagId}/status:
    parameters:
      - $ref: '#/components/parameters/TagId'
    patch:
      summary: Изменить статус тега
      operationId: updateTagStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTagStatusRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseTagAdminExtendedView'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found

  /api/v2/admin/social/config/global:
    get:
      summary: Вернуть конфиг
      operationId: getGlobalConfig
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseGlobalConfig'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    patch:
      summary: Обновить конфиг
      operationId: patchGlobalConfig
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchGlobalConfigRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseGlobalConfig'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden

  /api/v2/admin/social/users/{userId}/moderation/rules:
    parameters:
      - $ref: '#/components/parameters/UserId'
    get:
      operationId: getUserModerationRules
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseListOfUserModerationRule'
          description: OK
    post:
      operationId: createUserModerationRules
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserModerationRuleRequest'
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseUserModerationRule'
          description: OK
    delete:
      operationId: deleteUserModerationRule
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseUserModerationRule'
          description: OK

components:

  parameters:

    PostId:
      in: path
      name: postId
      schema:
        type: integer
        format: int64
      required: true

    CommentId:
      in: path
      name: commentId
      schema:
        type: integer
        format: int64
      required: true

    StoryItemId:
      in: path
      name: storyItemId
      schema:
        type: integer
        format: int64
      required: true

    UserId:
      in: path
      name: userId
      schema:
        type: integer
        format: int64
      required: true

    TagId:
      in: path
      name: tagId
      schema:
        type: integer
        format: int64
      required: true

    FeedSectionCode:
      in: path
      name: feedSectionCode
      schema:
        type: string
      required: true

    NextAnchor:
      in: query
      name: nextAnchor
      schema:
        type: string
      required: false

    PageNumber:
      in: query
      name: pageNumber
      schema:
        type: integer
        default: 1
      required: false

    PageSize:
      in: query
      name: pageSize
      schema:
        type: integer
        default: 20
      required: false

    CurrencyCode:
      in: query
      name: currencyCode
      schema:
        type: string
      required: false

    CountryId:
      in: query
      name: countryId
      schema:
        type: integer
        format: int64
      required: false

    Feature:
      in: query
      name: feature
      required: false
      schema:
        type: array
        items:
          $ref: "#/components/schemas/Feature"

    ConfiguredFeedSectionId:
      in: path
      name: configuredFeedSectionId
      schema:
        type: integer
        format: int64
      required: true

    FeedObjectsCollectionId:
      in: path
      name: feedObjectsCollectionId
      schema:
        type: integer
        format: int64
      required: true

  schemas:

    Api2Response:
      type: object
      properties:
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    MediaUploadMetaInfo:
      type: object
      properties:
        objectType:
          $ref: '#/components/schemas/ObjectType'
        items:
          type: array
          items:
            $ref: '#/components/schemas/MediaUploadItem'

    MediaUploadItem:
      type: object
      properties:
        variants:
          type: array
          items:
            $ref: '#/components/schemas/MediaUploadItemVariant'

    MediaUploadItemVariant:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/MediaVariantType'
        width:
          type: integer
        height:
          type: integer
        fileName:
          type: string

    Media:
      type: object
      properties:
        fileId:
          type: integer
          format: int64
          description: ИД файла
        type:
          $ref: '#/components/schemas/MediaType'
        status:
          $ref: '#/components/schemas/MediaStatus'
        orderNumber:
          type: integer
          description: порядковый номер
        variants:
          type: array
          items:
            $ref: '#/components/schemas/MediaVariant'
      discriminator:
        propertyName: type
        mapping:
          IMAGE: '#/components/schemas/Image'
          VIDEO: '#/components/schemas/Video'

    MediaType:
      type: string
      enum:
        - IMAGE
        - VIDEO

    MediaStatus:
      type: string
      enum:
        - PROCESSING
        - PROCESSED

    MediaVariant:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД файла
        type:
          $ref: '#/components/schemas/MediaVariantType'
        filePath:
          type: string
        fileUrl:
          type: string
        fileType:
          type: string
        width:
          type: integer
        height:
          type: integer

    MediaVariantType:
      type: string
      enum:
        - ORIGINAL
        - ZOOMED
        - THUMBNAIL
        - FEED

    Image:
      allOf:
        - $ref: '#/components/schemas/Media'
        - properties:
            similarProductsGetToken:
              description: Токен для запроса похожих продуктов
              type: string

    Api2ResponseListOfImage:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Image'

    Video:
      allOf:
        - $ref: '#/components/schemas/Media'
        - properties:
            duration:
              description: Длительность видео в мс
              type: integer
              format: int64

    Api2ResponseListOfVideo:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Video'

    Api2ResponseListOfMedia:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                oneOf:
                  - $ref: '#/components/schemas/Image'
                  - $ref: '#/components/schemas/Video'

    CreateMediaUploadUrlsRequest:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              originalFileName:
                type: string
              originalFileSize:
                description: в байтах
                type: integer
                format: int64
            required:
              - originalFileName
              - originalFileSize
      required:
        - items

    Api2ResponseMediaUploadUrls:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/MediaUploadUrls'

    MediaUploadUrls:
      type: object
      properties:
        items:
          type: array
          items:
            type: object
            properties:
              originalFileName:
                type: string
              uploadUrl:
                type: string
              filePath:
                type: string
            required:
              - originalFileName
              - uploadUrl
              - filePath
      required:
        - items

    ProcessMediaRequest:
      type: object
      properties:
        objectType:
          $ref: '#/components/schemas/ObjectType'
        items:
          type: array
          items:
            $ref: '#/components/schemas/MediaProcessItem'
      required:
        - objectType
        - items

    MediaProcessItem:
      type: object
      properties:
        variants:
          type: array
          items:
            $ref: '#/components/schemas/MediaProcessItemVariant'
      required:
        - variants

    MediaProcessItemVariant:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/MediaVariantType'
        fileType:
          type: string
        width:
          type: integer
        height:
          type: integer
        filePath:
          type: string
      required:
        - type
        - fileType
        - filePath

    SavePostRequest:
      type: object
      properties:
        text:
          description: текст
          type: string

    CreatePostRequest:
      allOf:
        - $ref: '#/components/schemas/SavePostRequest'
        - properties:
            mediaFileIds:
              description: коллекция ИД файлов медиа
              type: array
              items:
                type: integer
                format: int64
                minItems: 1
            tagIds:
              description: коллекция ИД тегов
              type: array
              items:
                type: integer
                format: int64
            mentionedUserIds:
              description: коллекция ИД упомянутых пользователей
              type: array
              items:
                type: integer
                format: int64
            productIds:
              description: коллекция ИД продуктов
              type: array
              items:
                type: integer
                format: int64
            link:
              $ref: '#/components/schemas/SaveLinkData'
      required:
        - mediaFileIds

    SaveLinkData:
      type: object
      properties:
        url:
          type: string
        text:
          type: string
        color:
          type: string
      required:
        - url
        - text
        - color

    UpdatePostRequest:
      allOf:
        - $ref: '#/components/schemas/SavePostRequest'

    UpdatePostFullRequest:
      allOf:
        - $ref: '#/components/schemas/SavePostRequest'
        - properties:
            mediaFileIds:
              description: коллекция ИД файлов медиа
              type: array
              items:
                type: integer
                format: int64
                minItems: 1
            tagIds:
              description: коллекция ИД тегов
              type: array
              items:
                type: integer
                format: int64
            mentionedUserIds:
              description: коллекция ИД упомянутых пользователей
              type: array
              items:
                type: integer
                format: int64
            productIds:
              description: коллекция ИД продуктов
              type: array
              items:
                type: integer
                format: int64
            link:
              $ref: '#/components/schemas/SaveLinkData'
      required:
        - mediaFileIds

    PostFeedView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        author:
          $ref: '#/components/schemas/User'
        media:
          description: коллекция медиа (иллюстрация | видео)
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/Image'
              - $ref: '#/components/schemas/Video'
        text:
          description: текст поста
          type: string
        createdAt:
          type: string
          format: date-time
        publishedAt:
          type: string
          format: date-time
        mentionedUsersExists:
          type: boolean
        likedByMe:
          type: boolean
        likesCount:
          type: integer
        lastLikedUsers:
          description: коллекция последних лайкнувших пользователей
          type: array
          items:
            $ref: '#/components/schemas/UserShortView'
        addedToFavoritesByMe:
          type: boolean
        link:
          $ref: '#/components/schemas/Link'
        products:
          description: коллекция продуктов
          type: array
          items:
            $ref: '#/components/schemas/ProductShortView'
        tags:
          description: коллекция тегов
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
        status:
          $ref: '#/components/schemas/PostStatus'
        commentsCount:
          type: integer
        lastComments:
          type: array
          items:
            $ref: '#/components/schemas/Comment'
        sharesCount:
          type: integer
      required:
        - id
        - author
        - media
        - createdAt
        - publishedAt
        - mentionedUsersExists
        - likedByMe
        - likesCount
        - addedToFavoritesByMe
        - products
        - tags
        - status
        - commentsCount
        - lastComments

    PostDiscoveryView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        author:
          $ref: '#/components/schemas/UserShortView'
        previewMedia:
          description: медиа, используемое для отображения
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/Image'
              - $ref: '#/components/schemas/Video'
        multipleMedia:
          description: флаг наличия других медиа в галерее поста
          type: boolean
      required:
        - id
        - author
        - previewMedia
        - multipleMedia

    PostSearchView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        author:
          $ref: '#/components/schemas/User'
        media:
          description: коллекция медиа (иллюстрация | видео)
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/Image'
              - $ref: '#/components/schemas/Video'
        text:
          description: текст поста
          type: string
        createdAt:
          type: string
          format: date-time
        publishedAt:
          type: string
          format: date-time
        mentionedUsersExists:
          type: boolean
        likedByMe:
          type: boolean
        likesCount:
          type: integer
        addedToFavoritesByMe:
          type: boolean
        link:
          $ref: '#/components/schemas/Link'
        products:
          description: коллекция продуктов
          type: array
          items:
            $ref: '#/components/schemas/ProductShortView'
        tags:
          description: коллекция тегов
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
        status:
          $ref: '#/components/schemas/PostStatus'
        commentsCount:
          type: integer
        sharesCount:
          type: integer
      required:
        - id
        - author
        - media
        - createdAt
        - publishedAt
        - mentionedUsersExists
        - likedByMe
        - likesCount
        - addedToFavoritesByMe
        - products
        - tags
        - status
        - commentsCount
        - sharesCount

    PostExtendedView:
      allOf:
        - $ref: '#/components/schemas/PostFeedView'
        - properties:
            mentionedUsers:
              type: array
              items:
                $ref: '#/components/schemas/User'
      required:
        - id
        - author
        - media
        - mentionedUsersExists
        - likedByMe
        - likesCount
        - addedToFavoritesByMe
        - status
        - commentsCount
        - lastComments
        - mentionedUsers

    PostFeedCollectionView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        author:
          $ref: '#/components/schemas/UserShortView'
        media:
          description: коллекция медиа (иллюстрация | видео)
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/Image'
              - $ref: '#/components/schemas/Video'
      required:
        - id
        - author
        - media

    PostStatus:
      type: string
      enum:
        - CREATING
        - NEED_MODERATION
        - PUBLISHED
        - REJECTED

    Api2ResponsePostExtendedView:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/PostExtendedView'

    Api2ResponsePageOfFavoritePost:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/FavoritePost'

    CreateCommentRequest:
      type: object
      properties:
        text:
          description: текст поста
          type: string
        imageFileIds:
          description: коллекция ИД файлов картинок
          type: array
          items:
            type: integer
            format: int64
            minItems: 1
        parentId:
          type: integer
          format: int64
          description: ИД родительского коммента

    CreateCommentByAdminRequest:
      type: object
      properties:
        authorId:
          description: автор коммента
          type: integer
          format: int64
        text:
          description: текст поста
          type: string
        imageFileIds:
          description: коллекция ИД файлов картинок
          type: array
          items:
            type: integer
            format: int64
            minItems: 1
        parentId:
          type: integer
          format: int64
          description: ИД родительского коммента
      required:
        - authorId

    AdminCommentMediaUploadInfo:
      type: object
      properties:
        contentsBase64:
          description: |
            Коллекция содержимого медиа, каждый элемент коллекции имеет следующий формат: 
            data:<mime-type>;base64,<content>
          type: array
          items:
            type: string
      required:
        - contentsBase64

    Comment:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД коммента
        author:
          $ref: '#/components/schemas/UserShortView'
        text:
          description: текст поста
          type: string
        createdAt:
          description: дата создания
          type: string
          format: date-time
        images:
          description: коллекция картинок
          type: array
          items:
            $ref: '#/components/schemas/Image'
        parentId:
          type: integer
          format: int64
          description: ИД родительского коммента
        children:
          type: array
          items:
            $ref: '#/components/schemas/Comment'
      required:
        - id
        - author
        - createdAt

    Api2ResponseComment:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/Comment'

    CreateStoryItemRequest:
      type: object
      properties:
        text:
          description: текст элемента стори
          type: string
        mediaFileId:
          type: integer
          format: int64
          description: ИД файла медиа
        productStickers:
          type: array
          items:
            $ref: '#/components/schemas/CreateProductStickerRequest'

    CreateProductStickerRequest:
      type: object
      properties:
        productId:
          type: integer
          format: int64
        left:
          type: integer
          format: int64
        top:
          type: integer
          format: int64
        width:
          type: integer
          format: int64
        height:
          type: integer
          format: int64

    StoryShortView:
      type: object
      properties:
        author:
          $ref: '#/components/schemas/UserShortView'
        viewedByMe:
          description: просмотрены ли все элементы стори текущим пользователем
          type: boolean

    Story:
      allOf:
        - $ref: '#/components/schemas/StoryShortView'
        - properties:
            items:
              type: array
              items:
                $ref: '#/components/schemas/StoryItem'

    Api2ResponseStory:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/Story'

    StoryItem:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД элемента стори
        createdAt:
          description: дата создания
          type: string
          format: date-time
        media:
          oneOf:
            - $ref: '#/components/schemas/Image'
            - $ref: '#/components/schemas/Video'
        productStickers:
          type: array
          items:
            $ref: '#/components/schemas/ProductSticker'
        viewedByMe:
          description: просмотрено ли текущим пользователем
          type: boolean
        likedByMe:
          type: boolean
        likesCount:
          type: integer
        lastViewedUsers:
          description: коллекция последних просмотревших пользователей
          type: array
          items:
            $ref: '#/components/schemas/UserShortView'

    Api2ResponseStoryItem:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/StoryItem'

    ProductSticker:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductShortView'
        left:
          type: integer
          format: int64
        top:
          type: integer
          format: int64
        width:
          type: integer
          format: int64
        height:
          type: integer
          format: int64

    UserShortView:
      type: object
      properties:
        id:
          type: integer
          format: int64
        nickname:
          type: string
          description: nickname в oskelly
        avatarPath:
          type: string
        pro:
          type: boolean
        sex:
          $ref: '#/components/schemas/UserSex'
        email:
          type: string
        isTrusted:
          type: boolean
      required:
        - id
        - nickname

    User:
      allOf:
        - $ref: '#/components/schemas/UserShortView'
        - properties:
            communityBadge:
              $ref: './partial.yaml#/components/schemas/CommunityBadge'
            isFollowed:
              type: boolean
            productsCount:
              type: integer
            followersCount:
              type: integer
      required:
        - id
        - nickname
        - followedByMe
        - productsCount
        - followersCount

    Celebrity:
      allOf:
        - $ref: '#/components/schemas/User'
        - properties:
            followersCountStr:
              type: string
      required:
        - followersCountStr

    Api2ResponseUser:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/User'

    Api2ResponseListOfUser:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/User'

    Api2ResponseListOfShortUser:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/UserShortView'

    Api2ResponseListOfOperationResultContainerAdminPost:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/OperationResultContainerAdminPost'

    Api2ResponseListOfOperationResultContainerAdminComment:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/OperationResultContainerAdminComment'

    OperationResult:
      type: string
      enum:
        - OK
        - ERROR

    OperationResultContainerAdminPost:
      type: object
      properties:
        message:
          type: string
        objectId:
          type: integer
          format: int64
        result:
          $ref: '#/components/schemas/AdminPost'
        resultCode:
          $ref: '#/components/schemas/OperationResult'
      required:
        - objectId
        - resultCode

    OperationResultContainerAdminComment:
      type: object
      properties:
        message:
          type: string
        objectId:
          type: integer
          format: int64
        result:
          $ref: '#/components/schemas/AdminComment'
        resultCode:
          $ref: '#/components/schemas/OperationResult'
      required:
        - objectId
        - resultCode

    DeletePostsByAdminRequest:
      type: object
      properties:
        postIds:
          type: array
          items:
            type: integer
            format: int64
        deletionReason:
          type: string
      required:
        - postIds
        - deletionReason

    DeleteCommentsByAdminRequest:
      type: object
      properties:
        commentIds:
          type: array
          items:
            type: integer
            format: int64
      required:
        - postIds

    RejectPostsByAdminRequest:
      type: object
      properties:
        postIds:
          type: array
          items:
            type: integer
            format: int64
        rejectionReason:
          type: string
      required:
        - postIds
        - rejectionReason

    Api2ResponsePageOfUser:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/User'

    Link:
      type: object
      properties:
        url:
          type: string
        text:
          type: string
        color:
          type: string
      required:
        - url
        - text
        - color

    ProductShortView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД продукта
        category:
          $ref: '#/components/schemas/Category'
        brand:
          $ref: '#/components/schemas/Brand'
        image:
          $ref: '#/components/schemas/Image'
        displayName:
          type: string
        stateDisplayName:
          type: string
        currentPrice:
          type: number
        currencyCode:
          type: string
        addedToWishlistByMe:
          type: boolean
        available:
          type: boolean
        hasSimilar:
          type: boolean
        url:
          type: string
      required:
        - id
        - category
        - brand
        - displayName
        - stateDisplayName
        - currentPrice
        - currencyCode
        - addedToWishlistByMe
        - available
        - hasSimilar
        - url

    Brand:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД бренда
        name:
          type: string

    Category:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД категории
        name:
          type: string

    LikeState:
      type: object
      properties:
        likesCount:
          type: integer
          format: int64
        likedByMe:
          type: boolean
        lastLikedUsers:
          description: коллекция последних лайкнувших пользователей
          type: array
          items:
            $ref: '#/components/schemas/UserShortView'
      required:
        - likesCount
        - likedByMe
        - lastLikedUsers

    Api2ResponseLikeState:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/LikeState'

    CreateTagRequest:
      type: object
      properties:
        name:
          description: название тега
          type: string

    UpdateTagNameRequest:
      type: object
      properties:
        name:
          description: название тега
          type: string

    UpdatePinnedTagOrderRequest:
      type: object
      properties:
        beforePinnedTagId:
          description: перед каким тегов закрепить тег, null - закрепить в конце
          type: integer
          format: int64

    UpdateTagStatusRequest:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/TagStatus'

    TagShortView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД тега
        name:
          type: string
          description: Название тега
        deleted:
          type: boolean

    TagExtendedView:
      allOf:
        - $ref: '#/components/schemas/TagShortView'
        - properties:
            postsCount:
              description: количество постов с данным тегом
              type: integer
              format: int64
            isBound:
              description: Признак того, что тэг есть у текущего пользователя
              type: boolean

    Api2ResponseTagExtendedView:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/TagExtendedView'

    Api2ResponseTagAdminExtendedView:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/TagAdminExtendedView'

    Api2ResponsePageOfTagShortView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/TagShortView'

    Api2ResponsePageOfTagExtendedView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/TagExtendedView'

    StoryItemViewedUser:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        liked:
          type: boolean

    Api2ResponsePageOfStoryItemViewedUser:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/StoryItemViewedUser'

    Api2ResponseTokenizedPage:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              properties:
                nextAnchor:
                  type: string

    Api2ResponsePage:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              properties:
                itemsCount:
                  type: integer
                totalPages:
                  type: integer
                totalAmount:
                  type: integer
                  format: int64

    Api2ResponsePageOfPostFeedView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/PostFeedView'

    Api2ResponsePageOfComment:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/Comment'

    Api2ResponsePageOfStory:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/Story'

    Api2ResponseStoryShortView:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/StoryShortView'

    Api2ResponsePageOfStoryShortView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/StoryShortView'

    # TODO: not implemented yet
    Live:
      type: object
      properties:
        title:
          type: string

    FeedItem:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/FeedItemType'
        content:
          oneOf:
            - $ref: '#/components/schemas/PostFeedView'
            - $ref: '#/components/schemas/PostsCollection'
            - $ref: '#/components/schemas/CelebritiesCollection'
            - $ref: '#/components/schemas/Live'

    DiscoveryItem:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/DiscoveryItemType'
        content:
          oneOf:
            - $ref: '#/components/schemas/PostDiscoveryView'
            - $ref: '#/components/schemas/Live'

    FeedItemType:
      type: string
      description: |
        Тип элемента ленты:
        * POST - пост
        * POSTS_COLLECTION - подборка постов
        * LIVE - прямая трансляция
        * USERS_COLLECTION - подборка селебрити (топ-блоггеры)
      enum:
        - POST
        - POSTS_COLLECTION
        - LIVE
        - USERS_COLLECTION

    DiscoveryItemType:
      type: string
      description: |
        Тип элемента дискавери:
        * POST - пост
        * LIVE - прямая трансляция
      enum:
        - POST
        - LIVE

    PostsCollection:
      type: object
      properties:
        title:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/PostFeedCollectionView'
        filters:
          type: array
          items:
            $ref: '#/components/schemas/PostsCollectionFilter'
        token:
          type: string

    PostsCollectionFilter:
      type: object
      properties:
        name:
          type: string
        value:
          type: string

    # TODO: метод get /api/v2/social/accounts выдает то, что требуется, надо понять, как его использовать в полке
    # su.reddot.presentation.api.v2.SocialControllerApiV2.getSocialAccounts
    CelebritiesCollection:
      type: object
      properties:
        title:
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/Celebrity'

    Api2ResponseTokenizedPageOfPostFeedView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponseTokenizedPage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/PostFeedView'

    Api2ResponseTokenizedPageOfPostSearchView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponseTokenizedPage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/PostSearchView'

    Api2ResponseTokenizedPageOfFeedItem:
      allOf:
        - $ref: '#/components/schemas/Api2ResponseTokenizedPage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/FeedItem'

    Api2ResponseTokenizedPageOfDiscoveryItem:
      allOf:
        - $ref: '#/components/schemas/Api2ResponseTokenizedPage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/DiscoveryItem'

    ObjectType:
      type: string
      enum:
        - POST
        - STORY
        - COMMENT

    FeedSection:
      type: object
      properties:
        code:
          type: string
          description: Код рубрики
        name:
          type: string
          description: Название рубрики
      required:
        - code
        - name

    Api2ResponseFeedSection:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/FeedSection'

    Api2ResponsePageOfFeedSection:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/FeedSection'

    FavoritePost:
      type: object
      properties:
        post:
          $ref: '#/components/schemas/PostFeedView'
        addedAt:
          type: string
          format: date-time

    RankedPostReturningContext:
      type: string
      enum:
        - FEED_POST
        - PRODUCT_CARD
        - FEED_POSTS_COLLECTION
        - HOME_POSTS_COLLECTION

    Api2ResponseCommentCreationExtendedResult:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/CommentCreationExtendedResult'

    CommentCreationExtendedResult:
      type: object
      properties:
        comment:
          $ref: '#/components/schemas/Comment'
        commentsCount:
          type: integer
        lastComments:
          type: array
          items:
            $ref: '#/components/schemas/Comment'
      required:
        - comment
        - commentsCount
        - lastComments

    Api2ResponseCommentDeletionExtendedResult:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/CommentDeletionExtendedResult'

    CommentDeletionExtendedResult:
      type: object
      properties:
        commentsCount:
          type: integer
        lastComments:
          type: array
          items:
            $ref: '#/components/schemas/Comment'
      required:
        - commentsCount
        - lastComments

    # Exclusively admin schemas

    AdminPost:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        createdAt:
          type: string
          format: date-time
        publishedAt:
          type: string
          format: date-time
        author:
          $ref: '#/components/schemas/AdminUser'
        rating:
          type: number
        boostingRate:
          type: number
        boostingExpiresAt:
          type: string
          format: date-time
        status:
          $ref: '#/components/schemas/PostStatus'
        deleted:
          type: boolean
        deletionReason:
          type: string
        rejectionReason:
          type: string
        hidden:
          type: boolean
        text:
          description: текст поста
          type: string
        media:
          description: коллекция медиа (иллюстрация | видео)
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/Image'
              - $ref: '#/components/schemas/Video'
        mentionedUsers:
          type: array
          items:
            $ref: '#/components/schemas/AdminUserShortView'
        products:
          description: коллекция продуктов
          type: array
          items:
            $ref: '#/components/schemas/AdminProductShortView'
        tags:
          description: коллекция тегов
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
        link:
          $ref: '#/components/schemas/Link'
        viewsCount:
          type: integer
        likesCount:
          type: integer
        sharesCount:
          type: integer
        favoritesCount:
          type: integer
        commentsCount:
          type: integer
      required:
        - id
        - createdAt
        - author
        - rating
        - boostingRate
        - status
        - deleted
        - hidden
        - media
        - viewsCount
        - likesCount
        - sharesCount
        - favoritesCount
        - commentsCount

    PostHistory:
      type: object
      properties:
        actionType:
          $ref: '#/components/schemas/ActionType'
        changedProperty:
          $ref: '#/components/schemas/PostPropertyChanged'
        createdAt:
          type: string
          format: date-time
        id:
          type: integer
          format: int64
        postId:
          type: integer
          format: int64
        user:
          $ref: '#/components/schemas/AdminUserShortView'
      required:
        - actionType
        - changedProperty
        - createdAt
        - id
        - postId

    ActionType:
      type: string
      enum:
        - CREATED
        - NEED_MODERATION
        - PUBLISHED
        - REJECTED
        - DELETED
        - RESTORED
        - HIDDEN
        - SHOWN
        - PROPERTY_CHANGED
        - PROPERTY_DELETED
        - OTHER

    PostPropertyChanged:
      type: object
      properties:
        boosting:
          $ref: '#/components/schemas/SimplePropertyChangedBoosting'
        deletedAt:
          $ref: '#/components/schemas/SimplePropertyChangedZonedDateTime'
        deletionReason:
          $ref: '#/components/schemas/SimplePropertyChangedString'
        hiddenAt:
          $ref: '#/components/schemas/SimplePropertyChangedZonedDateTime'
        link:
          $ref: '#/components/schemas/SimplePropertyChangedLink'
        media:
          $ref: '#/components/schemas/ArrayPropertyChangedMedia'
        mentionedUsers:
          $ref: '#/components/schemas/ArrayPropertyChangedUserShortView'
        products:
          $ref: '#/components/schemas/ArrayPropertyChangedAdminProductShortView'
        publishedAt:
          $ref: '#/components/schemas/SimplePropertyChangedZonedDateTime'
        rejectionReason:
          $ref: '#/components/schemas/SimplePropertyChangedString'
        status:
          $ref: '#/components/schemas/SimplePropertyChangedPostStatus'
        tags:
          $ref: '#/components/schemas/ArrayPropertyChangedTagShortView'
        text:
          $ref: '#/components/schemas/SimplePropertyChangedString'

    SimplePropertyChangedBoosting:
      type: object
      properties:
        newValue:
          $ref: '#/components/schemas/Boosting'
        oldValue:
          $ref: '#/components/schemas/Boosting'

    SimplePropertyChangedLink:
      type: object
      properties:
        newValue:
          $ref: '#/components/schemas/Link'
        oldValue:
          $ref: '#/components/schemas/Link'

    SimplePropertyChangedPostStatus:
      type: object
      properties:
        newValue:
          $ref: '#/components/schemas/PostStatus'
        oldValue:
          $ref: '#/components/schemas/PostStatus'

    SimplePropertyChangedString:
      type: object
      properties:
        newValue:
          type: string
        oldValue:
          type: string

    SimplePropertyChangedZonedDateTime:
      type: object
      properties:
        newValue:
          type: string
          format: date-time
        oldValue:
          type: string
          format: date-time

    ArrayPropertyChangedMedia:
      type: object
      properties:
        added:
          type: array
          items:
            $ref: '#/components/schemas/Media'
        deleted:
          type: array
          items:
            $ref: '#/components/schemas/Media'
      required:
        - added
        - deleted

    ArrayPropertyChangedTagShortView:
      type: object
      properties:
        added:
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
        deleted:
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
      required:
        - added
        - deleted

    ArrayPropertyChangedUserShortView:
      type: object
      properties:
        added:
          type: array
          items:
            $ref: '#/components/schemas/UserShortView'
        deleted:
          type: array
          items:
            $ref: '#/components/schemas/UserShortView'
      required:
        - added
        - deleted

    ArrayPropertyChangedAdminProductShortView:
      type: object
      properties:
        added:
          type: array
          items:
            $ref: '#/components/schemas/AdminProductShortView'
        deleted:
          type: array
          items:
            $ref: '#/components/schemas/AdminProductShortView'
      required:
        - added
        - deleted

    Boosting:
      type: object
      properties:
        expiresAt:
          type: string
          format: date-time
        rate:
          type: number
          format: double
      required:
        - rate

    AdminShortPost:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        createdAt:
          type: string
          format: date-time
        publishedAt:
          type: string
          format: date-time
        author:
          $ref: '#/components/schemas/UserShortView'
        boostingRate:
          type: number
        boostingExpiresAt:
          type: string
          format: date-time
        status:
          $ref: '#/components/schemas/PostStatus'
        deleted:
          type: boolean
        deletionReason:
          type: string
        rejectionReason:
          type: string
        hidden:
          type: boolean
        text:
          description: текст поста
          type: string
        link:
          $ref: '#/components/schemas/Link'
      required:
        - id
        - createdAt
        - author
        - boostingRate
        - status
        - deleted
        - hidden

    AdminShortPostWithMedia:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД поста
        createdAt:
          type: string
          format: date-time
        publishedAt:
          type: string
          format: date-time
        author:
          $ref: '#/components/schemas/UserShortView'
        boostingRate:
          type: number
        boostingExpiresAt:
          type: string
          format: date-time
        status:
          $ref: '#/components/schemas/PostStatus'
        deleted:
          type: boolean
        deletionReason:
          type: string
        rejectionReason:
          type: string
        hidden:
          type: boolean
        text:
          description: текст поста
          type: string
        link:
          $ref: '#/components/schemas/Link'
        media:
          description: коллекция медиа
          type: array
          items:
            $ref: '#/components/schemas/Media'
      required:
        - id
        - createdAt
        - author
        - boostingRate
        - status
        - deleted
        - hidden
        - media

    Api2ResponsePageOfAdminCommentView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/CommentAdminView'

    Api2ResponsePageOfAdminPost:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/AdminPost'

    AdminComment:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД коммента
        author:
          $ref: '#/components/schemas/AdminUserShortView'
        publishedBy:
          $ref: '#/components/schemas/AdminUserShortView'
        text:
          description: текст поста
          type: string
        createdAt:
          description: дата создания
          type: string
          format: date-time
        images:
          description: коллекция картинок
          type: array
          items:
            $ref: '#/components/schemas/Image'
        parentId:
          type: integer
          format: int64
          description: ИД родительского коммента
        children:
          type: array
          items:
            $ref: '#/components/schemas/AdminComment'
        deleted:
          description: был ли комментарий удален
          type: boolean
      required:
        - id
        - author
        - createdAt
        - deleted

    CommentAdminView:
      type: object
      properties:
        author:
          $ref: '#/components/schemas/AdminUserShortView'
        publishedBy:
          $ref: '#/components/schemas/AdminUserShortView'
        createdAt:
          type: string
          format: date-time
        createdAtTs:
          type: integer
          format: int64
        deleted:
          type: boolean
        deletedAt:
          type: string
          format: date-time
        deletedAtTs:
          type: integer
          format: int64
        id:
          type: integer
          format: int64
        images:
          description: коллекция картинок
          type: array
          items:
            $ref: '#/components/schemas/Image'
        parent:
          $ref: '#/components/schemas/CommentAdminView'
        post:
          $ref: '#/components/schemas/AdminShortPostWithMedia'
        text:
          type: string
      required:
        - authorId
        - createdAt
        - createdAtTs
        - deleted
        - id
        - images
        - post

    Api2ResponseAdminComment:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/AdminComment'

    Api2ResponsePageOfAdminComment:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/AdminComment'

    Api2ResponsePageOfAdminViewComment:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CommentAdminView'

    CommentSortingOption:
      type: string
      enum:
        - CREATION_DATE_DESC
        - CREATION_DATE_ASC

    SaveConfiguredFeedSectionRequest:
      type: object
      properties:
        name:
          type: string
          description: Название рубрики
        tagIds:
          type: array
          items:
            type: integer
            format: int64
        authorIds:
          type: array
          items:
            type: integer
            format: int64
        publishedAtFrom:
          type: string
          format: date
        publishedAtTo:
          type: string
          format: date
      required:
        - name

    CreateConfiguredFeedSectionRequest:
      allOf:
        - $ref: '#/components/schemas/SaveConfiguredFeedSectionRequest'

    UpdateConfiguredFeedSectionRequest:
      allOf:
        - $ref: '#/components/schemas/SaveConfiguredFeedSectionRequest'

    MoveConfiguredFeedSectionRequest:
      type: object
      properties:
        beforeId:
          description: ИД рубрики, перед которой нужно вставить данную рубрику или null, если нужно вставить рубрику в конец
          type: integer

    ConfiguredFeedSection:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД рубрики
        name:
          type: string
          description: Название рубрики
        tags:
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
        authors:
          type: array
          items:
            $ref: '#/components/schemas/AdminUserShortView'
        publishedAtFrom:
          type: string
          format: date
        publishedAtTo:
          type: string
          format: date
      required:
        - id
        - name
        - tags
        - authors

    Api2ResponseConfiguredFeedSection:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/ConfiguredFeedSection'

    Api2ResponsePageOfConfiguredFeedSection:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/ConfiguredFeedSection'

    SaveFeedPostsCollectionFiltersData:
      type: object
      properties:
        tagIds:
          type: array
          items:
            type: integer
            format: int64
        authorIds:
          type: array
          items:
            type: integer
            format: int64
        publishedAtFrom:
          type: string
          format: date
        publishedAtTo:
          type: string
          format: date
        ids:
          type: array
          items:
            type: integer
            format: int64

    SaveFeedObjectsCollectionRequest:
      type: object
      properties:
        title:
          type: string
          description: заголовок
        linkedPostNumber:
          type: number
          format: int64
          description: позиция поста
        type:
          $ref: '#/components/schemas/FeedObjectsCollectionType'
        filters:
          $ref: '#/components/schemas/SaveFeedPostsCollectionFiltersData'
      required:
        - title
        - linkedPostNumber
        - type

    CreateFeedObjectsCollectionRequest:
      allOf:
        - $ref: '#/components/schemas/SaveFeedObjectsCollectionRequest'

    UpdateFeedObjectsCollectionRequest:
      allOf:
        - $ref: '#/components/schemas/SaveFeedObjectsCollectionRequest'

    AdminFeedPostsCollectionFilters:
      type: object
      properties:
        tags:
          type: array
          items:
            $ref: '#/components/schemas/TagShortView'
        authors:
          type: array
          items:
            $ref: '#/components/schemas/AdminUserShortView'
        publishedAtFrom:
          type: string
          format: date
        publishedAtTo:
          type: string
          format: date
        posts:
          type: array
          items:
            $ref: '#/components/schemas/AdminShortPost'
      required:
        - tags
        - authors
        - posts

    AdminFeedObjectsCollection:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД рубрики
        title:
          type: string
          description: заголовок
        linkedPostNumber:
          type: number
          format: int64
          description: позиция поста
        type:
          $ref: '#/components/schemas/FeedObjectsCollectionType'
        filters:
          $ref: '#/components/schemas/AdminFeedPostsCollectionFilters'
        fillingType:
          $ref: '#/components/schemas/FillingType'
      required:
        - id
        - title
        - linkedPostNumber
        - type

    Api2ResponseAdminFeedObjectsCollection:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/AdminFeedObjectsCollection'

    Api2ResponsePageOfAdminFeedObjectsCollection:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/AdminFeedObjectsCollection'

    AdminUserShortView:
      type: object
      properties:
        id:
          type: integer
          format: int64
        nickname:
          type: string
          description: nickname в oskelly
        avatarPath:
          type: string
        pro:
          type: boolean
        sex:
          $ref: '#/components/schemas/UserSex'
        email:
          type: string
        moderator:
          type: boolean
        admin:
          type: boolean
        banned:
          type: boolean
      required:
        - id
        - nickname
        - pro

    UserSex:
      type: string
      enum:
        - MALE
        - FEMALE
        - BOY
        - GIRL
        - ADULT
        - CHILD

    AdminUser:
      allOf:
        - $ref: '#/components/schemas/AdminUserShortView'
        - properties:
            communityBadge:
              $ref: './partial.yaml#/components/schemas/CommunityBadge'

    AdminProductShortView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД продукта
        category:
          $ref: '#/components/schemas/Category'
        brand:
          $ref: '#/components/schemas/Brand'
        image:
          $ref: '#/components/schemas/Image'
        displayName:
          type: string
        currentPrice:
          type: number
        currencyCode:
          type: string
      required:
        - id
        - category
        - brand
        - displayName
        - currentPrice
        - currencyCode

    Api2ResponseAdminPost:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/AdminPost'

    Api2ResponseListOfPostHistory:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/PostHistory'

    Api2ResponseAdminShortPost:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/AdminShortPost'

    Api2ResponseListOfAdminShortPost:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/AdminShortPost'

    Api2ResponseLong:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: integer
              format: int64

    DeletePostByAdminRequest:
      type: object
      properties:
        deletionReason:
          description: текст
          type: string
      required:
        - deletionReason

    RejectPostByAdminRequest:
      type: object
      properties:
        rejectionReason:
          description: текст
          type: string
      required:
        - rejectionReason

    PatchPostByAdminRequest:
      type: object
      properties:
        text:
          description: текст, для очистки указывается пустая строка
          type: string
        boosting:
          $ref: '#/components/schemas/UpdateBoostingData'
        mediaFileIds:
          description: |
            Коллекция ИД файлов медиа, должна иметь как минимум один элемент.
            Для удаления медиа из коллекции указывается коллекция ИД медиа без удаленных элементов.
          type: array
          items:
            type: integer
            format: int64
        tagIds:
          description: |
            Коллекция ИД тегов, для полной очистки указывается пустая коллекция.
            Для удаления тега из коллекции указывается коллекция ИД тегов без удаленных элементов.
          type: array
          items:
            type: integer
            format: int64
        mentionedUserIds:
          description: |
            Коллекция ИД упомянутых пользователей, для полной очистки указывается пустая коллекция.
            Для удаления пользователя из коллекции указывается коллекция ИД пользователей без удаленных элементов.
          type: array
          items:
            type: integer
            format: int64
        productIds:
          description: |
            Коллекция ИД продуктов, для полной очистки указывается пустая коллекция
            Для удаления продукта из коллекции указывается коллекция ИД продуктов без удаленных элементов.
          type: array
          items:
            type: integer
            format: int64
        link:
          $ref: '#/components/schemas/LinkUpdateByAdminData'
        clearableProperties:
          $ref: '#/components/schemas/ClearablePostProperties'

    UpdateBoostingData:
      type: object
      properties:
        rate:
          type: number
          description: коэффициент бустинга
        expiresAt:
          type: string
          format: date-time
          description: срок действия
      required:
        - rate

    LinkUpdateByAdminData:
      type: object
      description: ссылка
      properties:
        url:
          type: string
        text:
          type: string
      required:
        - url
        - text

    ClearablePostProperties:
      type: object
      description: поля для очистки
      properties:
        boosting:
          type: boolean
        link:
          type: boolean
        text:
          type: boolean

    GlobalConfig:
      type: object
      description: конфиг
      properties:
        moderationBeforePublishingEnabled:
          type: boolean
        moderationBeforePublishingOCommunityUsersExcluded:
          type: boolean
      required:
        - moderationBeforePublishingEnabled
        - moderationBeforePublishingOCommunityUsersExcluded

    PatchGlobalConfigRequest:
      type: object
      description: конфиг
      properties:
        moderationBeforePublishingEnabled:
          type: boolean
        moderationBeforePublishingOCommunityUsersExcluded:
          type: boolean

    Api2ResponseGlobalConfig:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/GlobalConfig'

    FillingType:
      type: string
      description: |
        Тип наполнения полки постами:
        * WITHOUT_FILTERING - без фильтрации постов ("по популярности")
        * WITH_FILTERING_BY_POSTS - указанные посты ("по ИД")
        * WITH_FILTERING_BY_PARAMS - с фильтрацией постов ("отфильтровать")
      enum:
        - WITHOUT_FILTERING
        - WITH_FILTERING_BY_POSTS
        - WITH_FILTERING_BY_PARAMS

    FeedObjectsCollectionType:
      type: string
      description: |
        Тип полки
        * POSTS_COLLECTION - полка постов
        * USERS_COLLECTION - полка селебрити
      enum:
        - POSTS_COLLECTION
        - USERS_COLLECTION

    TagAdminView:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: ИД тега
        name:
          type: string
          description: Название тега
        status:
          $ref: '#/components/schemas/TagStatus'

    TagAdminExtendedView:
      allOf:
        - $ref: '#/components/schemas/TagAdminView'
        - properties:
            postsCount:
              description: количество постов с данным тегом
              type: integer
              format: int64

    Api2ResponsePageOfTagAdminExtendedView:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/TagAdminExtendedView'

    Api2ResponseListOfTagAdminExtendedView:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/TagAdminExtendedView'

    Api2ResponseListOfUserModerationRule:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/UserModerationRule'

    Api2ResponseUserModerationRule:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/UserModerationRule'

    UserModerationRule:
      type: object
      properties:
        active:
          type: boolean
        createdAt:
          type: string
          format: date-time
        createdBy:
          $ref: '#/components/schemas/UserShortView'
        deletedAt:
          type: string
          format: date-time
        deletedBy:
          $ref: '#/components/schemas/UserShortView'
        finishedAt:
          type: string
          format: date-time
        id:
          type: integer
          format: int64
        startedAt:
          type: string
          format: date-time
        reason:
          type: string
      required:
        - active
        - createdAt
        - createdBy
        - reason
        - id
        - startedAt

    CreateUserModerationRuleRequest:
      type: object
      properties:
        finishedAt:
          type: string
          format: date-time
        startedAt:
          type: string
          format: date-time
        reason:
          type: string
      required:
        - startedAt
        - reason

    TagStatus:
      type: string
      enum:
        - ACTIVE
        - PINNED
        - HIDDEN
        - DELETED

    TagSortingOption:
      type: string
      enum:
        - POSTS_COUNT_DESC
        - POSTS_COUNT_ASC

    PostWithExtraDataSortingOption:
      type: string
      enum:
        - PUBLICATION_OR_CREATION_DATE_ASC
        - PUBLICATION_OR_CREATION_DATE_DESC
        - RATING_ASC
        - RATING_DESC
        - LIKES_COUNT_ASC
        - LIKES_COUNT_DESC
        - COMMENTS_COUNT_ASC
        - COMMENTS_COUNT_DESC
        - VIEWS_COUNT_ASC
        - VIEWS_COUNT_DESC
        - SHARES_COUNT_ASC
        - SHARES_COUNT_DESC
        - FAVORITES_COUNT_ASC
        - FAVORITES_COUNT_DESC

    ActivityType:
      type: string
      enum:
        - VIEW
        - LIKE
        - SHARE
        - FAVORITE

    Api2ResponsePageOfAdminPostActivity:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/AdminPostActivity'

    AdminPostActivity:
      type: object
      description: |
        Элемент активности пользователя. 
        Если user != null, активность зарегистрирована от имени авторизованного пользователя, 
        если user == null - от имени неавторизованного пользователя
      properties:
        user:
          $ref: '#/components/schemas/AdminUserShortView'
        registeredAt:
          type: string
          format: date-time
        type:
          $ref: '#/components/schemas/ActivityType'
      required:
        - registeredAt
        - type

    Api2ResponsePageOfAdminUserPostsActivity:
      allOf:
        - $ref: '#/components/schemas/Api2ResponsePage'
        - properties:
            data:
              properties:
                items:
                  type: array
                  items:
                    $ref: '#/components/schemas/AdminUserPostsActivity'

    AdminUserPostsActivity:
      type: object
      description: |
        Элемент активности пользователя по постам
      properties:
        post:
          $ref: '#/components/schemas/AdminShortPostWithMedia'
        registeredAt:
          type: string
          format: date-time
        type:
          $ref: '#/components/schemas/ActivityType'
      required:
        - post
        - registeredAt
        - type

    PostHistorySortingOption:
      type: string
      enum:
        - CREATION_DATE_DESC
        - CREATION_DATE_ASC

    SearchProductPhotoResults:
      type: object
      description: Результаты подбора похожих продуктов
      properties:
        productIds:
          description: ИД всех похожих продуктов на фото
          type: array
          items:
            type: integer
            format: int64
        objects:
          description: Коллекция сегментов фото, в которых обнаружены похожие продукты
          type: array
          items:
            $ref: '#/components/schemas/OneSearchProductPhotoResult'
      required:
        - productIds
        - objects

    OneSearchProductPhotoResult:
      type: object
      description: Сегмент фото
      properties:
        count:
          description: количество продуктов
          type: integer
        productIds:
          description: ИД продуктов в этом сегменте
          type: array
          items:
            type: integer
            format: int64
        location:
          description: Координаты центра, заданные в долях от соответствущей размерности
          type: object
          properties:
            x:
              type: number
            y:
              type: number
      required:
        - count
        - productIds
        - location

    Feature:
      type: string
      enum:
        - TAGGED_USERS
        - POST_NO_LAST_COMMENTS

    Api2ResponseAdminPostRatingExplanation:
      allOf:
        - $ref: '#/components/schemas/Api2Response'
        - properties:
            data:
              $ref: '#/components/schemas/AdminPostRatingExplanation'

    AdminPostRatingExplanation:
      type: object
      description: Описание расчета рейтинга в виде упорядоченной коллекции частей
      properties:
        parts:
          type: array
          items:
            $ref: '#/components/schemas/AdminPostRatingExplanationPart'
      required:
        - components

    AdminPostRatingExplanationPart:
      type: object
      description: Часть описания
      properties:
        value:
          description: значение для вывода в строке описания расчета
          type: string
        description:
          description: описание части / метка части
          type: string
        color:
          description: цвет группы
          type: string
      required:
        - value