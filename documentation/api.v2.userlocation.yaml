{"openapi": "3.0.1", "info": {"title": "Oskelly Main Service", "description": "API Documentation", "version": "2.2.1"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "security": [{"cookieAuth": []}], "tags": [{"name": "Местоположение пользователя", "description": "Задание и получение местоположения пользователя"}], "paths": {"/api/v2/userlocation": {"get": {"tags": ["Местоположение пользователя"], "summary": "Получение местоположения пользователя", "description": "Поддерживается для авторизованного пользователя и для пользователя с guest token", "operationId": "getLocation", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseAddressDTO"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}, "put": {"tags": ["Местоположение пользователя"], "summary": "Задание местоположение пользователя", "description": "Поддерживается для авторизованного пользователя и для пользователя с guest token", "operationId": "setLocation", "requestBody": {"description": "Местоположение пользователя", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseString"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}}, "components": {"schemas": {"Api2Response": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "object"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AddressBreakdownDTO": {"type": "object", "properties": {"street": {"type": "string"}, "house": {"type": "string"}, "flat": {"type": "string"}}}, "AddressDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "zipCode": {"type": "string"}, "country": {"type": "string"}, "countryData": {"$ref": "#/components/schemas/CountryDTO"}, "cityData": {"$ref": "#/components/schemas/CityDTO"}, "region": {"type": "string"}, "city": {"type": "string"}, "address": {"type": "string"}, "addressBreakdown": {"$ref": "#/components/schemas/AddressBreakdownDTO"}, "address2": {"type": "string"}, "address3": {"type": "string"}, "fiasId": {"type": "string"}, "regionFiasId": {"type": "string"}, "cityFiasId": {"type": "string"}, "settlementFiasId": {"type": "string"}, "dadataFullAddress": {"type": "string"}, "fullCityName": {"type": "string"}, "fullAddress": {"type": "string"}, "cityValidated": {"type": "boolean"}, "addressValidated": {"type": "boolean"}, "checked": {"type": "boolean"}, "createTime": {"type": "integer", "format": "int64"}, "changeTime": {"type": "integer", "format": "int64"}, "deleteTime": {"type": "integer", "format": "int64"}, "cis": {"type": "boolean"}}}, "CityDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "region": {"type": "string"}}}, "CountryDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "isoCodeAlpha2": {"type": "string"}, "imageUrl": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyDTO"}, "uiCurrencyCode": {"type": "string"}, "requireZipcode": {"type": "boolean"}, "environment": {"type": "string", "enum": ["RU", "INT"]}, "countryCounterpartyType": {"type": "string", "enum": ["UAE_COUNTERPARTY", "DEFAULT_COUNTERPARTY"]}}}, "CurrencyDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sign": {"type": "string"}, "isoCode": {"type": "string"}, "isoNumber": {"type": "integer", "format": "int32"}, "selectedByDefault": {"type": "boolean"}, "active": {"type": "boolean"}, "base": {"type": "boolean"}}}, "Api2ResponseString": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "string"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Api2ResponseAddressDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/AddressDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Введите куки в формате: JSESSIONID=your_session_id", "name": "<PERSON><PERSON>", "in": "header"}}}}