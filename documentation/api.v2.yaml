openapi: 3.0.3

info:
  title: Oskelly API v2
  description: Oskelly API v2
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

tags:
  - name: api-v-2
    description: Api V 2

paths:
  /api/v2/logout:
    get:
      summary: Logout
      tags:
        - logout
      responses:
        '200':
          description: OK
      operationId: get-api-v2-logout
      parameters:
        - schema:
            type: string
          in: header
          name: referer
    post:
      summary: Logout
      tags:
        - logout
      operationId: post-api-v2-logout
      responses:
        '200':
          description: OK
      parameters:
        - schema:
            type: string
          in: header
          name: referer
  /api/v2/appversion/force-update-info:
    get:
      tags:
        - app-update-controller
      summary: app update
      operationId: checkAppUpdate
      parameters:
        - name: platform
          required: true
          in: query
          schema:
            type: string
            enum:
              - IOS
              - ANDROID
        - name: version
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfForceUpdateInfo'


components:
  schemas:
    Api2ResponseOfForceUpdateInfo:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ForceUpdateInfo'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    ForceUpdateInfo:
      title: ForceUpdateInfo
      type: object
      properties:
        updateRequired:
          type: boolean
        message:
          type: string

