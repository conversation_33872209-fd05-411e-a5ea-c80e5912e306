openapi: 3.0.0
info:
  title: Oskelly Bonuses API
  description: API для работы с Бонусной системой.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: localhost
tags:
  - name: bonuses-controller-api
    description: Bonuses Controller API
paths:
  /api/v2/bonuses/balance-brief:
    get:
      summary: Ручка для отображения текущего баланса + инфа о сгорающих баллах
      operationId: getBalanceBrief
      tags:
        - bonuses-controller-api
      responses:
        '200':
          description: Текущей баланс + инфа о сгорающих баллах
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfBonusesBalanceDTO'

  /api/v2/bonuses/balance-burning-schedule:
    get:
      summary: Ручка для отображения плана сгорания бонусов
      operationId: getBalanceBurningSchedule
      tags:
        - bonuses-controller-api
      responses:
        '200':
          description: План сгорания бонусов
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBonusesBurningScheduleItemDTO'

  /api/v2/bonuses/history:
    get:
      summary: Ручка для получения истории начислений и списаний
      operationId: getHistory
      parameters:
        - name: pageNumber
          in: query
          description: Номер страницы
          required: false
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: Размер страницы
          required: false
          schema:
            type: integer
            format: int32
      tags:
        - bonuses-controller-api
      responses:
        '200':
          description: История начислений и списаний
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfBonusesTransactionDTO'

  /api/v2/bonuses/loyalty-card/get-or-create:
    post:
      summary: Ручка для получения (или создания) инфы о бонусной карте (баркода)
      operationId: getOrCreateLoyaltyCard
      tags:
        - bonuses-controller-api
      responses:
        '200':
          description: Инфа о бонусной карте (баркод)
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfLoyaltyCardCardInfoDTO'

  /api/v2/bonuses/loyalty-card/wallet-url:
    get:
      summary: Ручка для получения инфы о бонусной карте (ссылка для добавления в Apple Wallet)
      operationId: getLoyaltyCardWallet
      parameters:
        - name: type
          in: query
          description: Размер страницы
          required: false
          schema:
            type: string
            enum:
              - URL
              - QR
              - PKPASS
      tags:
        - bonuses-controller-api
      responses:
        '200':
          description: Инфа о бонусной карте (ссылка для добавления в Apple Wallet)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfLoyaltyCardWalletDTO'

components:
  schemas:
    Api2ResponseOfLoyaltyCardWalletDTO:
      title: Api2ResponseOfBonusesBonusCardWalletDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/LoyaltyCardWalletDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    LoyaltyCardWalletDTO:
      description: Информация о ссылке wallet
      type: object
      properties:
        link:
          nullable: false
          type: string
          description: Линк
          example: http://example.comm
