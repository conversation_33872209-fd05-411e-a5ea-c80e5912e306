openapi: 3.0.3
info:
  title: Oskelly Main Service
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
paths:
  /api/v3/admin/productComments:
    post:
      tags:
        - Product comments API
      summary: publishComment
      operationId: publishCommentUsingPOST
      parameters:
        - name: images
          in: query
          required: false
          style: pipeDelimited
          schema:
            type: array
            items:
              type: string
              format: binary
        - name: parentCommentId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: productId
          in: query
          required: false
          style: form
          schema:
            type: integer
            format: int64
        - name: text
          in: query
          required: false
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/CommentDTO"
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v3/admin/productComments/user:
    post:
      tags:
        - Product comments API
      summary: getUserComments
      operationId: getUserCommentsUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCommentsRequestV3'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/PageOfCommentsDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v3/admin/productComments/base64-images:
    post:
      tags:
        - Product comments API
      summary: publishComment
      operationId: publishCommentWithBase64ImagesUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductCommentBase64ImagesDataDTO'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/CommentDTO"
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v3/admin/productComments/product/{productId}:
    get:
      tags:
        - Product comments API
      summary: getProductComments
      operationId: getProductCommentsUsingGET
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: withDeleted
          in: query
          description: withDeleted
          required: false
          style: simple
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CommentDTO"
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v3/admin/productComments/product/{productId}/tree:
    get:
      tags:
        - Product comments API
      summary: getProductCommentsTree
      operationId: getProductCommentsTreeUsingGET
      parameters:
        - name: productId
          in: path
          description: productId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: withDeleted
          in: query
          description: withDeleted
          required: false
          style: simple
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CommentDTO"
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v3/admin/productComments/{commentId}:
    get:
      tags:
        - Product comments API
      summary: getComment
      operationId: getCommentUsingGET
      parameters:
        - name: commentId
          in: path
          description: commentId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/CommentDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    delete:
      tags:
        - Product comments API
      summary: deleteComment
      operationId: deleteCommentUsingDELETE
      parameters:
        - name: commentId
          in: path
          description: commentId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: integer
                format: int64
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v3/admin/productComments/restore/{commentId}:
    post:
      tags:
        - Product comments API
      summary: restoreComment
      operationId: restoreCommentUsingPOST
      parameters:
        - name: commentId
          in: path
          description: commentId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: "#/components/schemas/CommentDTO"
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
components:
  schemas:
    UserCommentsRequestV3:
      title: UserCommentsRequestV3
      type: object
      properties:
        userId:
          type: integer
          format: int64
        page:
          type: integer
          format: int32
        rowsPerPage:
          type: integer
          format: int32
        sortBy:
          type: string
        descending:
          type: boolean
        commentText:
          type: string
        withDeleted:
          type: boolean

    PageOfCommentsDTO:
      title: PageOfCommentsDTO
      type: object
      properties:
        totalPages:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int32
        itemsCount:
          type: integer
          format: int32
        items:
          type: array
          items:
            $ref: '#/components/schemas/CommentDTO'

    CommentDTO:
      title: CommentDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        images:
          type: array
          items:
            type: string
        parentCommentId:
          type: integer
          format: int64
        productId:
          type: integer
          format: int64
        productRequestId:
          type: integer
          format: int64
        publishedAtTime:
          type: integer
          format: int64
        publisher:
          $ref: '#/components/schemas/UserDTO'
        replyTo:
          type: string
        needsTranslate:
          type: boolean
        subComments:
          type: array
          items:
            $ref: '#/components/schemas/CommentDTO'
        text:
          type: string
        subCommentsCount:
          type: integer
          format: int32
        productImage:
          type: string
        editedAtTime:
          type: string
        deletedAtTime:
          type: string

    UserDTO:
      title: UserDTO
      type: object
      properties:
        acceptsReturns:
          type: boolean
        avatarPath:
          type: string
        birthDate:
          type: integer
          format: int64
        brandLikesCount:
          type: integer
          format: int32
        email:
          type: string
        firstChar:
          type: string
        fullName:
          type: string
        id:
          type: integer
          format: int64
        isFollowed:
          type: boolean
        isPro:
          type: boolean
        isTrusted:
          type: boolean
        likesCount:
          type: integer
          format: int32
        name:
          type: string
        nickname:
          type: string
        productLikesCount:
          type: integer
          format: int32
        productsCount:
          type: integer
          format: int32
        registrationTime:
          type: integer
          format: int64
        sex:
          type: string
          enum:
            - ADULT
            - BOY
            - CHILD
            - FEMALE
            - GIRL
            - MALE
        adminProfileUrl:
          type: string
          description: Link to user profile in old admin view
        commonTags:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/UserCommonTagDTO'
        sellerType:
            $ref: './partial.yaml#/components/schemas/SellerType'

    ProductCommentBase64ImagesDataDTO:
      title: ProductCommentBase64ImagesDataDTO
      type: object
      properties:
        imagesBase64:
          type: array
          items:
            type: string
        parentCommentId:
          type: integer
          format: int64
        productId:
          type: integer
          format: int64
        productRequestId:
          type: integer
          format: int64
        text:
          type: string
