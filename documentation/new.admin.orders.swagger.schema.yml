openapi: 3.0.3
info:
  title: Oskelly Admin Order Service
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
paths:
  /api/v2/admin/orders:
    get:
      tags:
        - Get All Orders
      summary: Получить список заказов
      operationId: getOrdersUsingGET
      parameters:
        - name: tab
          in: query
          description: Выбор категории заказ. Категория заказов 'ВСЕ' установлена по умолчанию
          required: false
          style: form
          schema:
            type: string
            enum:
              - ALL
              - COMPLETED
              - CREATED
              - EXPERTISE
              - FROM_SELLER
              - PAYMENT
              - PAYMENT_TO_SELLER
              - REFUND
              - RETURN
              - TO_BUYER
              - UNCOMPLETED
              - UNDEFINED
        - name: subTab
          in: query
          description: Выбор подкатегории заказа. Подкатегория заказов 'ВСЕ' установлена по умолчанию
          required: false
          style: form
          schema:
            type: string
            enum:
              - BUYER_IN_MOSCOW
              - EXPECTING_CONFIRM_AGENT_REPORT
              - EXPECTING_COURIER_TO_BUYER
              - EXPECTING_COURIER_TO_SELLER
              - EXPERTISE_START
              - FROM_SELLER_TO_OFFICE
              - HOLD_COMPLETE_REJECTED
              - LOGIST_ON_WAY_TO_BUYER
              - LOGIST_ON_WAY_TO_SELLER
              - ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS
              - ORDER_COMPLETED
              - ORDER_COMPLETED_RETURN
              - ORDER_CONFIRMED
              - ORDER_CONFIRMING
              - ORDER_DELIVERED
              - ORDER_REFUND
              - OURSELVES_DELIVERY_TO_BUYER
              - OURSELVES_FROM_OFFICE_TO_BUYER
              - OURSELVES_FROM_SELLER_TO_OFFICE
              - OURSELVES_PICKING_UP_FROM_SELLER
              - RETURN_COMPLETED
              - RETURN_CREATED
              - RETURN_EXPERTISE
              - RETURN_ON_WAY_TO_OFFICE
              - SELLER_IN_MOSCOW
              - UNCOMPLETED
              - UNDEFINED
              - WAIT_PAYMENT_MONEY_TO_SELLER
        - name: sellerType
          in: query
          description: Тип продавца
          required: false
          style: form
          schema:
            type: string
            enum:
              - ALL
              - BOUTIQUE
              - SIMPLE_SELLER
        - name: from
          in: query
          description: Дата начала поиска
          required: false
          style: form
          schema:
            type: string
            format: date
        - name: to
          in: query
          description: Дата окончание поиска
          required: false
          style: form
          schema:
            type: string
            format: date
        - name: orderIds
          in: query
          description: Список ID заказов для поиска
          required: false
          style: form
          explode: true
          schema:
            type: string
        - name: sort
          in: query
          description: Выбор поля для сортировки
          required: false
          style: form
          schema:
            type: string
            enum:
              - HOLD_TIME
              - HOLD_TIME_DESC
              - UPDATE_TIME
              - UPDATE_TIME_DESC
        - name: page
          in: query
          description: Номер страницы
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: Колличество элементов на странице
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2Response'

components:
  schemas:
    Api2Response:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Page'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Page:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AdminPanelOrder'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    AdminPanelOrder:
      required:
        - amount
        - amountWithoutDelivery
        - amountWithoutPromoAndWithoutDelivery
        - buyerInfo
        - companyProfit
        - id
        - lastUpdateDateTime
        - promoCode
        - sellerInfo
        - sellerProfit
        - status
        - typeSeller
      type: object
      properties:
        amount:
          type: number
          description: Сумма дохода
          format: double
        amountWithoutDelivery:
          type: number
          description: Сумма без учета доставки
          format: double
        amountWithoutPromoAndWithoutDelivery:
          type: number
          description: Сумма без учета промокода и доставки
          format: double
        buyerInfo:
          $ref: '#/components/schemas/AdminPanelOrderUser'
        companyProfit:
          type: number
          description: Доход компании
          format: double
        holdPeriod:
          type: integer
          description: Количество дней после оплаты
          format: int64
        id:
          type: integer
          description: ID Заказа
          format: int64
        lastUpdateDateTime:
          type: string
          description: Дата обновления
          format: date-time
        promoCode:
          type: string
          description: Промокод
        promoPercent:
          type: number
          description: Процент скидки по промокод
          format: double
        promoValue:
          type: number
          description: Скидка по промокоду
          format: double
        sellerInfo:
          $ref: '#/components/schemas/AdminPanelOrderUser'
        sellerProfit:
          type: number
          description: Доход продавца
          format: double
        status:
          type: string
          description: Статус заказа
          enum:
            - BUYER_IN_MOSCOW
            - EXPECTING_CONFIRM_AGENT_REPORT
            - EXPECTING_COURIER_TO_BUYER
            - EXPECTING_COURIER_TO_SELLER
            - EXPERTISE_START
            - FROM_SELLER_TO_OFFICE
            - HOLD_COMPLETE_REJECTED
            - LOGIST_ON_WAY_TO_BUYER
            - LOGIST_ON_WAY_TO_SELLER
            - ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS
            - ORDER_COMPLETED
            - ORDER_COMPLETED_RETURN
            - ORDER_CONFIRMED
            - ORDER_CONFIRMING
            - ORDER_DELIVERED
            - ORDER_REFUND
            - OURSELVES_DELIVERY_TO_BUYER
            - OURSELVES_FROM_OFFICE_TO_BUYER
            - OURSELVES_FROM_SELLER_TO_OFFICE
            - OURSELVES_PICKING_UP_FROM_SELLER
            - RETURN_COMPLETED
            - RETURN_CREATED
            - RETURN_EXPERTISE
            - RETURN_ON_WAY_TO_OFFICE
            - SELLER_IN_MOSCOW
            - UNCOMPLETED
            - UNDEFINED
            - WAIT_PAYMENT_MONEY_TO_SELLER
        typeSeller:
          type: string
          description: Тип продавца
          enum:
            - ALL
            - BOUTIQUE
            - SIMPLE_SELLER
      description: Информация о заказе для панели администратора
    AdminPanelOrderUser:
      required:
        - fullName
        - mailAddr
        - nickName
      type: object
      properties:
        fullName:
          type: string
          description: ФИО
        mailAddr:
          type: string
          description: Адрес электроной почты
        nickName:
          type: string
          description: Псевдоним
      description: Информация о пользователе в список заказов (покупатель / продавец)