openapi: 3.0.3
info:
  title: Oskelly Admin - Common tags
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
paths:
  /api/v3/admin/userCommonTags/allGroups:
    get:
      tags:
        - User common tags API
      summary: Получить список групп
      operationId: getAllTagGroupsUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfTagGroups'

  /api/v3/admin/userCommonTags/allGroupsCached:
    get:
      tags:
        - User common tags API
      summary: Получить список групп (кеш)
      operationId: getAllTagGroupsCachedUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfTagGroups'

  /api/v3/admin/userCommonTags//byGroup/{groupId}/usersCount:
    get:
      parameters:
        - name: groupId
          in: path
          required: true
          schema:
            type: integer
            format: int64
      tags:
        - User common tags API
      summary: Получить счетчики пользователей
      operationId: getUsersCountByGroupTagsUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfUserCommonTagUsersCountDTO'

components:
  schemas:
    Api2ResponseOfListOfTagGroups:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/UserCommonTagGroupDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfUserCommonTagUsersCountDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/UserCommonTagUsersCountDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    UserCommonTagGroupCode:
      type: string
      enum:
        - COMMON_TAG_GROUP_USER_STATUS
        - COMMON_TAG_GROUP_COMMERCIAL_TAGS_OLD
        - COMMON_TAG_GROUP_COMMERCIAL_TAGS_NEW
        - COMMON_TAG_GROUP_VIP_STATUS
        - COMMON_TAG_GROUP_PAYMENT_METHOD
        - COMMON_TAG_GROUP_LOGISTIC
        - COMMON_TAG_GROUP_COMMUNITY_STATUS
        - COMMON_TAG_GROUP_CONTRACT_TYPE
        - COMMON_TAG_GROUP_SELLER_LOCATION
        - COMMON_TAG_GROUP_PAYMENT_CURRENCY
        - COMMON_TAG_GROUP_SOURCING
        - USER_TYPE
        - SELLER_TYPE
        - ACCESS
        - CONCIERGE_BUYER
        - SOCIAL_NETWORKS
        - USER_BAN
        - SEGMENTS
        - CROSS_BORDER
        - AUTHORITIES