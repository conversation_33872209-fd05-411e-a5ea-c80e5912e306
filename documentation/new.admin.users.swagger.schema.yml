openapi: 3.0.3
info:
  title: Oskelly Admin - Users
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
paths:
  /api/v3/admin/users:
    post:
      tags:
        - Users API
      summary: Создать пользователя
      operationId: createUserUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminPanelUserDTO'

  /api/v3/admin/users/list:
    post:
      tags:
        - Users API
      summary: Получить список пользователей
      operationId: getUsersUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminV2UsersRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfPageOfAdminPanelUserDTO'

  /api/v3/admin/users/count:
    post:
      tags:
        - Users API
      summary: Получить количество пользователей
      operationId: getCountUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminV2UsersRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfUsersCount'

  /api/v3/admin/users/restore/{userId}:
    post:
      tags:
        - Users API
      summary: Восстановить пользователя по его ИД
      operationId: restoreUserByIdUsingPOST
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'

  /api/v3/admin/users/{userId}:
    get:
      tags:
        - Users API
      summary: Получить пользователя по его ИД
      operationId: getUserByIdUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminPanelUserDTO'
    put:
      tags:
        - Users API
      summary: Обновить пользователя
      operationId: updateUser
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminPanelUserDTO'
    patch:
      tags:
        - Users API
      summary: Обновить заданные поля пользователя
      operationId: patchUser
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfAdminPanelUserDTO'
    delete:
      tags:
        - Users API
      summary: Удалить пользователя по его ИД
      operationId: deleteUserByIdUsingDELETE
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Api2ResponseOfString"

  /api/v3/admin/users/commonDebt:
    get:
      tags:
        - Users API
      summary: Получить долги
      operationId: getCommonDebtUsingPOST
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfDebtDTO'

  /api/v3/admin/users/userDebt/{userId}:
    get:
      tags:
        - Users API
      summary: Долг пользователя
      operationId: getUserDebtByIdUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfDebtDTO'

  /api/v3/admin/users/activity/{userId}:
    get:
      tags:
        - Users API
      summary: Активности пользователя
      operationId: getUserActivityByIdUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfMapOfActivityDTO'

  /api/v3/admin/users/rejectHistory:
    post:
      tags:
        - Users API
      summary: История отклонений
      operationId: getRejectHistoryUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RejectHistoryRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfRejectHistoryContainer'

  /api/v3/admin/users/community/userStatusHistory/{userId}:
    get:
      tags:
        - Users API
      summary: Получение информации об истории статусов пользователя
      operationId: getUserStatusHistoryByIdUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfCommunityUserStatusHistoryItem'

  /api/v3/admin/users/community/forceUpdateUserStatus/{userId}:
    post:
      tags:
        - Users API
      summary: Принудительное обновление статуса пользователя из админки
      operationId: forceUpdateUserStatusPOST
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StatusForceUpdateRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCommunityStatusExtended'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/community/privileges-requirements:
    get:
      summary: Получить информацию о каждом статусе, его привилегиях, и требованиях, общее для всех пользователей
      operationId: getAdminPrivilegesRequirements
      tags:
        - Users API
      responses:
        '200':
          description: Получение списка требований для получения статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCommunityInfos'

  /api/v3/admin/users/bonuses/balance-brief/{userId}:
    get:
      summary: Ручка для отображения текущего баланса + инфа о сгорающих баллах
      operationId: getBalanceBrief
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      tags:
        - Users API
      responses:
        '200':
          description: Текущей баланс + инфа о сгорающих баллах
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfBonusesBalanceDTO'

  /api/v3/admin/users/bonuses/history/{userId}:
    get:
      summary: Ручка для получения истории начислений и списаний
      operationId: getHistory
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
        - name: bonusesType
          in: query
          description: Отображать только сгораемые бонусы
          required: false
          schema:
            $ref: './partial.yaml#/components/schemas/BonusesType'
        - name: pageNumber
          in: query
          description: Номер страницы
          required: false
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: Размер страницы
          required: false
          schema:
            type: integer
            format: int32
      tags:
        - Users API
      responses:
        '200':
          description: История начислений и списаний бонусов
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfPageOfBonusesTransactionDTO'

  /api/v3/admin/users/bonuses/balance-burning-schedule/{userId}:
    get:
      summary: Ручка для отображения плана сгорания бонусов
      operationId: getBalanceBurningSchedule
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      tags:
        - Users API
      responses:
        '200':
          description: План сгорания бонусов
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfListOfBonusesBurningScheduleItemDTO'

  /api/v3/admin/users/bonuses/templates/{code}:
    get:
      summary: Ручка для получения одного шаблона списания/начисления
      operationId: getTemplate
      parameters:
        - name: code
          in: path
          description: Код шаблона
          required: true
          schema:
            type: string
      tags:
        - Users API
      responses:
        '200':
          description: Шаблон списания/начисления бонусов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBonusesTransferTemplateDTO'
        '404':
          description: Шаблон с заданным кодом не найден
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/bonuses/templates/list:
    get:
      summary: Ручка для получения всех шаблонов списания/начисления
      operationId: getTemplateList
      tags:
        - Users API
      responses:
        '200':
          description: Шаблоны списания/начисления бонусов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfBonusesTransferTemplateDTO'

  /api/v3/admin/users/bonuses/manual-withdraw-bonuses:
    post:
      tags:
        - Users API
      summary: Ручное списание бонусов пользователя из админки
      operationId: manualWithdrawBonuses
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BonusesManualWithdrawRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/bonuses/manual-transfer-bonuses:
    post:
      tags:
        - Users API
      summary: Ручное начисление бонусов пользователя из админки
      operationId: manualTransferBonuses
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BonusesManualTransferRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/bonuses/transfer-bonuses-welcome:
    post:
      tags:
        - Users API
      summary: Начисление приветственных бонусов пользователя
      operationId: transferBonusesWelcome
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferBonusesWelcomeRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/bonuses/transfer-bonuses-birthday:
    post:
      tags:
        - Users API
      summary: Начисление бонусов на день рождения пользователя
      operationId: transferBonusesBirthday
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferBonusesBirthdayRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/bonuses/force-update-loyalty-card-info:
    post:
      tags:
        - Users API
      summary: Запуск ручного обновления на карте лояльности покупателя
      operationId: forceUpdateUserInfoOnLoyaltyCard
      parameters:
        - name: userId
          in: query
          description: Идентификатор пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/Api2ResponseOfLoyaltyCardCardInfoDTO'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/loyalty/accounts/{userId}:
    get:
      summary: Ручка для получения информации о пользователе программы лояльности
      operationId: getLoyaltyAccount
      parameters:
        - name: userId
          in: path
          description: Идентификатор пользователя
          required: true
          schema:
            type: integer
            format: int64
      tags:
        - Users API
      responses:
        '200':
          description: Информация по статусу пользователя в программе лояльности
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfLoyaltyAccountAdminDTO'
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/loyalty/accounts/{userId}/available-statuses:
    get:
      summary: Ручка для получения доступных пользователю статусов в программе лояльности
      operationId: getLoyaltyAccountAvailableStatuses
      parameters:
        - name: userId
          in: path
          description: Идентификатор пользователя
          required: true
          schema:
            type: integer
            format: int64
      tags:
        - Users API
      responses:
        '200':
          description: Информация по доступным пользователю статусам в программе лояльности
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfLoyaltyStatusDTO'
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/loyalty/accounts/{userId}/status:
    post:
      summary: Ручка для принудительного обновления статуса пользователя в программе лояльности
      operationId: setLoyaltyStatusToUser
      parameters:
        - name: userId
          in: path
          description: Идентификатор пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoyaltyStatusSetRequest'
      tags:
        - Users API
      responses:
        '200':
          description: Информация по статусу пользователя в программе лояльности
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfLoyaltyAccountAdminDTO'
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/loyalty/accounts/{userId}/history:
    get:
      summary: Ручка для получения истории изменений статуса пользователя в программе лояльности
      operationId: getLoyaltyStatusHistory
      parameters:
        - name: userId
          in: path
          description: Идентификатор пользователя
          required: true
          schema:
            type: integer
            format: int64
      tags:
        - Users API
      responses:
        '200':
          description: Информация по истории изменений статуса пользователя в программе лояльности
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfLoyaltyStatusHistoryItemAdminDTO'
        '404':
          description: Пользователь не найден
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/loyalty/statuses/description:
    get:
      summary: Ручка для получения требований и привилегий по статусам в программе лояльности
      operationId: getLoyaltyStatusesDescription
      tags:
        - Users API
      responses:
        '200':
          description: Общий набор требований и привилегий для каждого статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfLoyaltyStatusesDescriptionAdminDTO'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/users/loyalty/statuses/sync:
    post:
      summary: Ручка для синхронизации статусов из сервиса лояльности в коммон теги
      operationId: syncLoyaltyStatuses
      tags:
        - Users API
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
        '500':
          description: Внутренняя ошибка сервера

  /api/v3/admin/user/security/passwordReset:
    post:
      tags:
        - Users API
      summary: Сброс пароля
      operationId: passwordResetUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetIdRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'

  /api/v3/admin/users/counters/{userId}:
    get:
      tags:
        - Users API
      summary: Каунтеры пользователя
      operationId: getUserCounterUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfMapOfCounters'

  /api/v3/admin/segments/user/{userId}:
    get:
      tags:
        - Users API
      summary: Сегменты пользователя
      operationId: getUserSegmentsUsingGET
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfNewAdminSegmentDTO'

  /api/v3/admin/segments/all:
    get:
      tags:
        - Users API
      summary: Сегменты
      operationId: getAllSegmentsUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfNewAdminSegmentDTO'

  /api/v3/admin/segments/{segmentId}/users:
    get:
      tags:
        - Users API
      summary: Пользователи сегмента
      operationId: getAllUsersForSegmentUsingGET
      parameters:
        - name: segmentId
          in: path
          description: ИД сегмента
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfSegmentUserDTO'

  /api/v3/admin/segments/{segmentId}:
    delete:
      tags:
        - Users API
      summary: Удаление сегмена
      operationId: deleteSegmentUsingDELETE
      parameters:
        - name: segmentId
          in: path
          description: ИД сегмента
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK

  /api/v3/admin/segments:
    post:
      tags:
        - Users API
      summary: Создание сегмента
      operationId: createSegmentUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSegmentRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfNewAdminSegmentDTO'

  /api/v3/admin/segments/user:
    post:
      tags:
        - Users API
      summary: Редактирование сегментов пользователя
      operationId: editUserSegmentsUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSegmentsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfNewAdminSegmentDTO'

  /api/v3/admin/commissionGrid:
    post:
      tags:
        - CommissionGrid API
      summary: Edit commission grid
      operationId: editCommissionGridUsingPUST
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommissionGridDTOV3'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCommissionGridDTOV3'
  /api/v3/admin/commissionGrid/filter:
    post:
      tags:
        - CommissionGrid API
      summary: Get grids
      operationId: filterCommissionGridsUsingPOST
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommissionGridFilter'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePageCommissionGridDTOV3'

  /api/v3/admin/commissionGrid/listForUser/{userId}:
    get:
      tags:
        - CommissionGrid API
      summary: List grids for user
      operationId: listCommissionGridsForUserUsingGET
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfCommissionGridDTOV3'

  /api/v3/admin/commissionGrid/{id}:
    get:
      tags:
        - CommissionGrid API
      summary: Get grid by id
      operationId: getCommissionGridByIdUsingGET
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCommissionGridDTOV3'
    delete:
      tags:
        - CommissionGrid API
      summary: Delete grid by id
      operationId: deleteCommissionGridUsingDELETE
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseCommissionGridDTOV3'

  /api/v3/admin/commissionGrid/{id}/rules:
    post:
      tags:
        - CommissionGrid API
      summary: Create commission grid rule
      operationId: editCommissionGridRuleUsingPOST
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCommissionGridRuleRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCommissionGridRuleResponse'

  /api/v3/admin/dictionary/saleShipmentRoutes:
    get:
      tags:
        - Dictionary API
      summary: Get all shipment routes
      description: Get a list of all shipment routes
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfSaleShipmentRouteDTO'

  /api/v3/admin/personalManagers/allOperators:
    get:
      tags:
        - Users API
      summary: Get all operators
      description: Get a list of all operators
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfPersonalManagerDTO'

  /api/v3/admin/personalManagers/dict:
    get:
      tags:
        - Users API
      summary: Get dictionary
      description: Get a list of personal managers in dictionary
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfPersonalManagerDTO'

  /api/v3/admin/personalManagers/addToDict:
    post:
      tags:
        - Users API
      summary: Add manager to dictionary
      description: Add a manager to the dictionary
      parameters:
        - name: managerId
          in: query
          description: ID of the manager to add
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfPersonalManagerDTO'

  /api/v3/admin/personalManagers/removeFromDict:
    post:
      tags:
        - Users API
      summary: Remove manager from dictionary
      description: Remove a manager from the dictionary
      parameters:
        - name: managerId
          in: query
          description: ID of the manager to remove
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfPersonalManagerDTO'

  /api/v3/admin/users/{userId}/contracts/upload:
    post:
      tags:
        - Users API
      summary: Загрузить контракт
      operationId: uploadContract
      parameters:
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: Файл контракта для загрузки
      responses:
        '200':
          description: OK

  /api/v3/admin/users/{userId}/contracts/{contractId}:
    get:
      tags:
        - Users API
      summary: Получить контракт
      operationId: getContract
      parameters:
        - name: contractId
          in: path
          description: ИД контракта
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: 'Хэдер Attachment с именем файла'
              schema:
                type: string

    delete:
      tags:
        - Users API
      summary: Удалить контракт
      operationId: deleteContract
      parameters:
        - name: contractId
          in: path
          description: ИД контракта
          required: true
          schema:
            type: integer
            format: int64
        - name: userId
          in: path
          description: ИД пользователя
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK

  /api/v3/admin/users/confirmations/confirmed:
    post:
      tags:
        - Users API
      summary: Подтверждение получено
      operationId: confirmationConfirmed
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmationConfirmedRequest'
      responses:
        '200':
          description: OK

  /api/v3/admin/users/confirmations/transferred-to-user-moderation:
    post:
      tags:
        - Users API
      summary: Требуется модерация пользователя
      operationId: confirmationTransferredToUserModeration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmationTransferredToUserModerationRequest'
      responses:
        '200':
          description: OK

  /api/v3/admin/users/confirmations/edited:
    post:
      tags:
        - Users API
      summary: Изменено
      operationId: confirmationEdited
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConfirmationEditedRequest'
      responses:
        '200':
          description: OK

components:
  schemas:
    Api2ResponseOfString:
      type: "object"
      properties:
        data:
          type: "string"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfString"

    Api2ResponseOfBoolean:
      type: "object"
      properties:
        data:
          type: "boolean"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfBoolean"

    PasswordResetIdRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64

    BonusesTransferTemplateDTO:
      type: object
      properties:
        code:
          type: string
          description: Код шаблона
        name:
          type: string
          description: Наименование шаблона
        reason:
          type: string
          description: Причина начисления/списания
        bonusesType:
          $ref: './partial.yaml#/components/schemas/BonusesType'
        amount:
          nullable: true
          type: number
          description: Количество баллов к списанию/начислению
          example: 10
        dateEnd:
          type: string
          format: date-time
          description: Дата окончания действия бонусов

    BonusesManualWithdrawRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          nullable: false
          description: Идентификатор пользователя, которому изменяют бонусный баланс
        type:
          nullable: false
          $ref: './partial.yaml#/components/schemas/BonusesType'
        reason:
          type: string
          description: Причина списания бонусов
        description:
          nullable: false
          type: string
          description: Комментарий к списанию бонусов
        amount:
          nullable: false
          type: number
          description: Количество баллов к списанию
          example: 10

    BonusesManualTransferRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          nullable: false
          description: Идентификатор пользователя, которому изменяют бонусный баланс
        type:
          nullable: false
          $ref: './partial.yaml#/components/schemas/BonusesType'
        reason:
          type: string
          description: Причина зачисления бонусов
        description:
          nullable: false
          type: string
          description: Комментарий к списанию бонусов
        expirationDate:
          type: string
          format: date-time
          description: Дата окончания действия, зачисленных бонусов
        amount:
          nullable: false
          type: number
          description: Количество баллов к списанию
          example: 10

    TransferBonusesWelcomeRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          nullable: false
          description: Идентификатор пользователя, которому изменяют бонусный баланс

    TransferBonusesBirthdayRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          nullable: false
          description: Идентификатор пользователя, которому изменяют бонусный баланс

    LoyaltyStatusSetRequest:
      type: object
      description: Запрос на ручную простановку статуса в программе лояльности
      properties:
        auto:
          type: boolean
          nullable: false
          description: Признак того, что текущий статус расчитан автоматически
        status:
          $ref: '#/components/schemas/LoyaltyStatus'
          description: Текущий статус в программе лояльности
        expiresAt:
          type: string
          format: date
          description: Дата окончания действия статуса
        reasonText:
          type: string
          description: Причина ручной установки статуса

    LoyaltyAccountAdminDTO:
      type: object
      description: Информация по статусу пользователя в программе лояльности
      properties:
        userId:
          type: integer
          format: int64
          nullable: false
          description: Идентификатор пользователя
        auto:
          type: boolean
          nullable: false
          description: Признак того, что текущий статус расчитан автоматически
        status:
          description: Текущий статус в программе лояльности
          $ref: '#/components/schemas/LoyaltyStatusDTO'
        statusExpiresAt:
          type: string
          format: date
          description: Дата окончания действия статуса
        autoStatus:
          description: Статус, расчитанный по заказам пользователя
          $ref: '#/components/schemas/LoyaltyStatusDTO'
        nextStatus:
          description: Следующий, более высокий, статус
          $ref: '#/components/schemas/LoyaltyStatusDTO'
        nextStatusRemainingAmount:
          type: number
          description: Сколько осталось до следующего статуса
        purchaseAmount:
          type: number
          description: Сумма заказов пользователя
        statusSetByAdminName:
          type: string
          description: Имя администратора, установившего статус

    LoyaltyStatusesDescriptionAdminDTO:
      type: object
      description: Общий набор требований и привилегий для каждого статуса
      properties:
        whiteStatus:
          $ref: '#/components/schemas/LoyaltyStatusDescriptionAdminDTO'
          description: Требование для статуса WHITE
          nullable: false
        silverStatus:
          $ref: '#/components/schemas/LoyaltyStatusDescriptionAdminDTO'
          description: Требование для статуса SILVER
          nullable: false
        blackStatus:
          $ref: '#/components/schemas/LoyaltyStatusDescriptionAdminDTO'
          description: Требование для статуса BLACK
          nullable: false
        privileges:
          type: array
          description: Набор привилегий
          nullable: false
          items:
            $ref: '#/components/schemas/LoyaltyStatusPrivilegeDescriptionDTO'

    LoyaltyStatusDescriptionAdminDTO:
      type: object
      description: Требование для присвоения статуса
      properties:
        requiredPurchaseAmount:
          $ref: '#/components/schemas/LoyaltyStatusValueDataDTO'
          description: Требование для присвоения статуса
          nullable: false

    LoyaltyStatusHistoryItemAdminDTO:
      type: object
      description: Одна строка из истории изменения статуса лояльности пользователя
      properties:
        status:
          $ref: '#/components/schemas/LoyaltyStatusDTO'
          description: Статус
          nullable: false
        statusExpiredAt:
          type: string
          format: date
          description: Дата окончания действия статуса
        statusSetAt:
          type: string
          format: date-time
          nullable: false
          description: Дата получения или присвоения статуса
        statusSetByAdminId:
          type: integer
          format: int64
          description: Идентификатор администратора, установившего статус
        statusSetByAdminName:
          type: string
          description: Имя администратора, установившего статус
        statusSetReasonText:
          type: string
          description: Причина ручной установки статуса

    LoyaltyStatusPrivilegeDescriptionDTO:
      type: object
      description: Описание привилегии для всех статусов
      properties:
        code:
          type: string
          description: Код привилегии
          nullable: false
        name:
          type: string
          description: Наименование привилегии
          nullable: false
        whiteValueData:
          $ref: '#/components/schemas/LoyaltyStatusValueDataDTO'
          description: Значение данной привилении для статуса WHITE
        silverValueData:
          $ref: '#/components/schemas/LoyaltyStatusValueDataDTO'
          description: Значение данной привилении для статуса SILVER
        blackValueData:
          $ref: '#/components/schemas/LoyaltyStatusValueDataDTO'
          description: Значение данной привилении для статуса BLACK

    LoyaltyStatusValueDataDTO:
      type: object
      description: Значение привилегии или требования для какого-либо конкретного статуса
      properties:
        displayText:
          type: string
          description: При необходимости, текст, который нужно отобразить
        paid:
          type: boolean
          description: Информационный признак платно/не платно
        type:
          $ref: '#/components/schemas/ValueType'
          nullable: false
        value:
          type: string
          description: Одиночное значение
        valueFrom:
          type: string
          description: Значение на нижней границе диапазона
        valueTo:
          type: string
          description: Значение на верхней границе диапазона

    LoyaltyStatusDTO:
      type: object
      description: ДТО для статуса программы лояльности
      properties:
        code:
          $ref: '#/components/schemas/LoyaltyStatus'
          description: Код статуса лояльности
          nullable: false
        name:
          type: string
          description: Описание статуса лояльности
          nullable: false

    LoyaltyStatus:
      type: string
      description: Статус лояльности
      enum:
        - WHITE
        - SILVER
        - BLACK
        -
    ValueType:
      type: string
      description: Тип значения
      enum:
        - RUBLE
        - PERCENT
        - BONUS
        - LEVEL

    StatusForceUpdateRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
          nullable: false
          description: Идентификатор пользователя, которому меняют статус
        code:
          type: string
          description: Код нужного статуса. Может быть null, если нужно просто сбросить статус.
        changeReason:
          type: string
          description: Причина установления статуса вручную. Может быть null (обычно если просто продлевается действия ручного статуса).
        expirationDate:
          type: string
          format: date-time
          description: Дата окончания действия, выставленного вручную статуса

    UserCreateRequestDTO:
      type: object
      properties:
        nickName:
          type: string
        email:
          type: string
        phone:
          type: string

    Api2ResponseOfMapOfCounters:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/UserCounterDTO"
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfUsersCount:
      required:
        - data
      type: object
      properties:
        data:
          type: integer
          format: int64
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfPageOfAdminPanelUserDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfAdminPanelUserDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfNewAdminSegmentDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/NewAdminSegmentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfNewAdminSegmentDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/NewAdminSegmentDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfSegmentUserDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SegmentUserDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    NewAdminSegmentDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        order:
          type: integer
          format: int32
        enabled:
          type: boolean

    CreateSegmentRequest:
      type: object
      properties:
        order:
          type: integer
          format: int32
        name:
          type: string

    SegmentUserDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        avatarPath:
          type: string
        nickname:
          type: string

    UpdateSegmentsRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
        segmentIds:
          type: array
          items:
            type: integer
            format: int64

    Api2ResponseOfCommunityStatusExtended:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CommunityStatusExtended'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfCommunityInfos:
      required:
        - data
      type: object
      properties:
        data:
          $ref: './partial.yaml#/components/schemas/CommunityInfos'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfBonusesTransferTemplateDTO:
      title: Api2ResponseOfListOfBonusesTransferTemplateDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BonusesTransferTemplateDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfBonusesTransferTemplateDTO:
      title: Api2ResponseOfBonusesTransferTemplateDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/BonusesTransferTemplateDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfLoyaltyAccountAdminDTO:
      title: Api2ResponseOfLoyaltyAccountAdminDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/LoyaltyAccountAdminDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfLoyaltyStatusesDescriptionAdminDTO:
      title: Api2ResponseOfLoyaltyStatusesDescriptionAdminDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/LoyaltyStatusesDescriptionAdminDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfLoyaltyStatusDTO:
      title: Api2ResponseOfListOfLoyaltyStatusDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyStatusDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfLoyaltyStatusHistoryItemAdminDTO:
      title: Api2ResponseOfListOfLoyaltyStatusHistoryItemAdminDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LoyaltyStatusHistoryItemAdminDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfAdminPanelUserDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AdminPanelUserDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PageOfAdminPanelUserDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AdminPanelUserDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    AdminPanelUserDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        info:
          $ref: '#/components/schemas/AdminPanelUserInfoDTO'
        extendedInfo:
          $ref: '#/components/schemas/AdminPanelUserExtendedInfoDTO'


    AdminPanelUserInfoDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        avatarPath:
          type: string
        nickname:
          type: string
        description:
          type: string
        email:
          type: string
        sellerType:
          $ref: './partial.yaml#/components/schemas/SellerType'
        registrationDate:
          type: string
          format: date-time
        deleteTime:
          type: string
          format: date-time
        hasActiveBans:
          type: boolean
        badges:
          type: array
          items:
            $ref: '#/components/schemas/BadgeDTO'
        tradeStat:
          $ref: '#/components/schemas/UserTradeStatDTO'
        userRole:
          $ref: '#/components/schemas/Role'
        authorities:
          type: array
          items:
            $ref: '#/components/schemas/AuthorityDTO'
        authorityType:
          $ref: '#/components/schemas/AuthorityType'

    BadgeDTO:
      type: object
      properties:
        group:
          type: string
        name:
          type: string

    UserTradeStatDTO:
      type: object
      properties:
        userId:
          type: integer
          format: int64
        purchaseCount:
          type: integer
          format: int32
        sellCount:
          type: integer
          format: int32
        productCount:
          type: integer
          format: int32
        productCountOskelly:
          type: integer
          format: int32
        productCountSeller:
          type: integer
          format: int32

    BadgeGroup:
      type: string
      enum:
        - USER_ROLE
        - SELLER_TYPE
        - RETURNS


    AdminPanelUserExtendedInfoDTO:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        name:
          type: string
        phone:
          type: string
        isPhoneConfirmed:
          type: boolean
        isLoyaltyProgramAccepted:
          type: boolean
        isLoyaltyProgramV2Accepted:
          type: boolean
        sex:
          $ref: '#/components/schemas/Sex'
        dateOfBirth:
          type: string
          format: date
        likedBrands:
          type: array
          items:
            $ref: '#/components/schemas/BrandDTO'
        canCreateStream:
          type: boolean
        canPublishMultiSizes:
          type: boolean
        canPublishToDisabledCategories:
          type: boolean
        syncAgree:
          type: boolean
        syncSuccess:
          type: boolean
        canAcceptsReturns:
          type: boolean
        userType:
          $ref: '#/components/schemas/UserType'
        socialAccounts:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/SocialAccountDTO"
        tagGroups:
          type: object
          additionalProperties:
            $ref: './partial.yaml#/components/schemas/UserCommonTagGroupDTO'
        conciergeBuyer:
          type: boolean
        crossBorder:
          type: boolean
        autoPickupDisabled:
          type: boolean
        userCurrentOCommunityStatusExtended:
          $ref: "#/components/schemas/CommunityStatusExtended"
        segments:
          type: array
          items:
            $ref: "#/components/schemas/NewAdminSegmentDTO"
        commissionGrid:
          $ref: "#/components/schemas/CommissionGridDTOV3"
        deviceCountry:
          $ref: './partial.yaml#/components/schemas/CountryDTO'
        pickupCountry:
          $ref: './partial.yaml#/components/schemas/CountryDTO'
        personalManagers:
          type: array
          items:
            $ref: "#/components/schemas/PersonalManagerDTO"
        saleShipmentRoute:
          $ref: '#/components/schemas/SaleShipmentRouteDTO'
        userContract:
          $ref: '#/components/schemas/UserContractDTO'
        userVipStatus:
          $ref: '#/components/schemas/UserVipStatusDTO'
        userTrustInfo:
          $ref: '#/components/schemas/UserTrustInfoDTO'
        deliveryDays:
          type: integer
          format: int32
        trusted:
          type: boolean
        instanceId:
          type: integer
          format: int32
          example: 123456

    UserCounterDTO:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
        userId:
          type: integer
          format: int64
        value:
          type: integer
          format: int32

    CommunityUserStatusHistoryItem:
      type: object
      properties:
        userId:
          type: integer
          format: int64
        modUserId:
          type: integer
          format: int64
        modUserName:
          type: string
        status:
          $ref: './partial.yaml#/components/schemas/CommunityStatus'
        indicators:
          $ref: './partial.yaml#/components/schemas/CommunityIndicator'
        statusChangedAt:
          type: string
          format: date-time
        changeReason:
          type: string

    CommunityStatusExtendedItem:
      required:
        - status
        - isActive
      type: object
      properties:
        status:
          $ref: './partial.yaml#/components/schemas/CommunityStatus'
        statusExpiresOn:
          type: string
          format: date-time
        isActive:
          type: boolean
        privilegeGroups:
          $ref: './partial.yaml#/components/schemas/CommunityPrivilegeGroups'

    CommunityStatusExtended:
      required:
        - status
        - statusExtendedItems
      type: object
      properties:
        status:
          $ref: './partial.yaml#/components/schemas/CommunityStatus'
        statusExtendedItems:
          type: array
          items:
            $ref: '#/components/schemas/CommunityStatusExtendedItem'

    BrandDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string


    AuthorityDTO:
      type: object
      properties:
        authorityName:
          $ref: '#/components/schemas/AuthorityName'
        displayName:
          type: string
        turnOn:
          type: boolean

    AuthorityType:
      type: string
      enum:
        - MODERATOR
        - ADMIN
        - RETOUCHER
        - USER

    Sex:
      type: string
      enum:
        - MALE
        - FEMALE
        - BOY
        - GIRL
        - ADULT
        - CHILD


    Role:
      type: string
      enum:
        - SIMPLE_USER
        - BOUTIQUE


    SocialAccountDTO:
      type: object
      properties:
        nickname:
          type: string
        url:
          type: string
        subscribersCount:
          type: integer
          format: int32
        postsCount:
          type: integer
          format: int32
        description:
          type: string

    SocialNetwork:
      type: string
      enum:
        - INSTAGRAM
        - TIKTOK
        - YOUTUBE
        - SNAPCHAT

    AdminV2UsersRequest:
      type: object
      properties:
        idsInclude:
          type: array
          items:
            type: integer
            format: int64
        idsExclude:
          type: array
          items:
            type: integer
            format: int64
        nickname:
          type: string
        sellerTypes:
          type: array
          items:
            $ref: "./partial.yaml#/components/schemas/SellerType"
        canAcceptsReturns:
          type: boolean
        canCreateStream:
          type: boolean
        canPublishMultiSizes:
          type: boolean
        canPublishToDisabledCategories:
          type: boolean
        isActiveBan:
          type: boolean
        banTypes:
          type: array
          items:
            $ref: "#/components/schemas/BanType"
        searchString:
          type: string
        socialNetworks:
          type: array
          items:
            $ref: "#/components/schemas/SocialNetwork"
        authorityNames:
          type: array
          items:
            $ref: "#/components/schemas/AuthorityName"
        customFilter:
          type: string
        userCommonTags:
          type: array
          items:
            type: integer
            format: int64
        segments:
          type: array
          items:
            type: integer
            format: int64
        proStatus:
          type: boolean
        conciergeBuyer:
          type: boolean
        crossBorder:
          type: boolean
        autoPickupDisabled:
          type: boolean
        isTrusted:
          type: boolean
        singleAuthority:
          type: boolean
        anyAuthority:
          type: boolean
        notNullSellerTypeOR:
          type: boolean
        authorityNamesExclude:
          type: array
          items:
            $ref: "#/components/schemas/AuthorityName"

        selfPredicateType:
          $ref: "#/components/schemas/PredicateType"
        combinedPredicateType:
          $ref: "#/components/schemas/PredicateType"
        subPredicates:
          type: array
          items:
            $ref: "#/components/schemas/AdminV2UsersRequest"
        followersOfUserId:
          type: integer
          format: int64
        followingUserId:
          type: integer
          format: int64
        page:
          type: integer
          format: int32
        rowsPerPage:
          type: integer
          format: int32
        sortBy:
          type: string
        descending:
          type: boolean

    UserUpdateRequestDTO:
      type: object
      properties:
        deliveryDays:
          type: integer
          format: int32
        nickname:
          type: string
        description:
          type: string
        email:
          type: string
        sellerType:
          $ref: './partial.yaml#/components/schemas/SellerType'
        firstName:
          type: string
        lastName:
          type: string
        name:
          type: string
        phone:
          type: string
        isPhoneConfirmed:
          type: boolean
        isLoyaltyProgramAccepted:
          type: boolean
        isLoyaltyProgramV2Accepted:
          type: boolean
        sex:
          $ref: '#/components/schemas/Sex'
        dateOfBirth:
          type: string
          format: date
        likedBrandIds:
          type: array
          items:
            type: integer
            format: int64
        canCreateStream:
          type: boolean
        canPublishMultiSizes:
          type: boolean
        canPublishToDisabledCategories:
          type: boolean
        canAcceptsReturns:
          type: boolean
        userRole:
          $ref: '#/components/schemas/Role'
        userType:
          $ref: '#/components/schemas/UserType'
        socialAccounts:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/SocialAccountDTO"
        authorityNames:
          type: array
          items:
            $ref: '#/components/schemas/AuthorityName'
        _clear:
          $ref: '#/components/schemas/UserClearablePatchProps'
        userCommonTagIds:
          type: array
          items:
            type: integer
            format: int64
        conciergeBuyer:
          type: boolean
        crossBorder:
          type: boolean
        autoPickupDisabled:
          type: boolean
        segmentIds:
          type: array
          items:
            type: integer
            format: int64
        commissionGridId:
          type: integer
          format: int64
        pickupCountryId:
          type: integer
          format: int64
        personalManagerBindings:
          type: array
          items:
            $ref: '#/components/schemas/PersonalManagerBindingDTO'
        saleShipmentRouteId:
          type: integer
          format: int64
        trusted:
          type: boolean
        instanceId:
          type: integer
          format: int64

    UserClearablePatchProps:
      type: object
      properties:
        birthDate:
          type: boolean
        sex:
          type: boolean
        phone:
          type: boolean
        saleShipmentRoute:
          type: boolean

    PersonalManagerBindingDTO:
      type: object
      properties:
        managerId:
          type: integer
          format: int64
        order:
          type: integer
          format: int32

    RejectHistoryRequest:
      type: object
      properties:
        userId:
          type: integer
          format: int64
        page:
          type: integer
          format: int32
        rowsPerPage:
          type: integer
          format: int32
        sortBy:
          type: string
        descending:
          type: boolean

    PersonalManagerDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        email:
          type: string
        fullName:
          type: string
        nickname:
          type: string

    SaleShipmentRouteDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        channelId:
          type: string
        systemName:
          type: string
      required: [ id, name, channelId, systemName ]

    Api2ResponseOfRejectHistoryContainer:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/RejectHistoryContainer'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    RejectHistoryContainer:
      required:
        - totalPrice
        - totalOskellyAmount
      type: object
      properties:
        history:
          $ref: "#/components/schemas/PageOfRejectHistoryDTO"
        totalPrice:
          type: number
          format: bigdecimal
        totalOskellyAmount:
          type: number
          format: bigdecimal

    PageOfRejectHistoryDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/RejectHistoryDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    RejectHistoryDTO:
      required:
        - productId
        - photoUrl
        - categoryPath
        - brandName
        - price
        - oskellyAmount
        - date
        - orderId
        - status
      type: object
      properties:
        productId:
          type: integer
          format: int64
        photoUrl:
          type: string
        categoryPath:
          type: string
        brandName:
          type: string
        price:
          type: number
          format: bigdecimal
        oskellyAmount:
          type: number
          format: bigdecimal
        date:
          type: string
          format: date
        orderId:
          type: integer
          format: int64
        status:
          $ref: '#/components/schemas/RejectStatusDTO'

    RejectStatusDTO:
      required:
        - name
        - label
      type: object
      properties:
        name:
          type: string
        label:
          type: string
        extensions:
          type: array
          items:
            $ref: '#/components/schemas/RejectStatusExtensionDTO'

    RejectStatusExtensionDTO:
      required:
        - name
        - label
      type: object
      properties:
        name:
          type: string
        label:
          type: string

    Api2ResponseOfListOfCommunityUserStatusHistoryItem:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CommunityUserStatusHistoryItem'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfSaleShipmentRouteDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SaleShipmentRouteDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfPersonalManagerDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PersonalManagerDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfPersonalManagerDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PersonalManagerDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfMapOfActivityDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/UserActivityDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    UserActivityDTO:
      required:
        - label
      type: object
      properties:
        label:
          type: string
        amount:
          type: number
          format: bigdecimal
        count:
          type: integer
          format: int64

    Api2ResponseOfDebtDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/DebtDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    DebtDTO:
      required:
        - usersDebt
        - oskellyDebt
      type: object
      properties:
        usersDebt:
          type: number
          format: bigdecimal
        oskellyDebt:
          type: number
          format: bigdecimal

    CommissionGridDTOV3:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        type:
          $ref: '#/components/schemas/Type'
        commissions:
          type: array
          items:
            $ref: '#/components/schemas/CommissionDTOV3'
        deleteTime:
          type: string
          format: date-time
        fixedAmount:
          type: number
          format: double
    Type:
      type: string
      enum:
        - DEFAULT
        - PRO
        - CUSTOM
    CommissionDTOV3:
      type: object
      properties:
        id:
          type: integer
          format: int64
        commissionGridId:
          type: integer
          format: int64
        publicPrice:
          type: number
          format: double
        value:
          type: number
          format: double
        boutiqueValue:
          type: number
          format: double
        valueScaled:
          type: number
          format: double
        boutiqueValueScaled:
          type: number
          format: double

    CreateCommissionGridRuleRequestSSR:
      type: object
      properties:
        id:
          type: integer
          format: int64
      required: [ id ]

    CreateCommissionGridRuleRequest:
      type: object
      properties:
        saleShipmentRoute:
          $ref: '#/components/schemas/CreateCommissionGridRuleRequestSSR'

    CreateCommissionGridRuleResponse:
      type: object

    Api2ResponseCommissionGridDTOV3:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CommissionGridDTOV3'

    Api2ResponsePageCommissionGridDTOV3:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
          properties:
            content:
              type: array
              items:
                $ref: '#/components/schemas/CommissionGridDTOV3'
            pageable:
              type: object
            totalElements:
              type: integer
              format: int64
            totalPages:
              type: integer
              format: int32
            size:
              type: integer
              format: int32
            number:
              type: integer
              format: int32
            sort:
              type: object
            first:
              type: boolean
            numberOfElements:
              type: integer
              format: int32
            empty:
              type: boolean
    Api2ResponseOfListOfCommissionGridDTOV3:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CommissionGridDTOV3'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    CommissionGridFilter:
      type: object
      properties:
        name:
          type: string
        types:
          type: array
          items:
            $ref: '#/components/schemas/Type'
        withDeleted:
          type: boolean
        page:
          type: integer
          format: int32
        size:
          type: integer
          format: int32
        sort:
          type: string

    UserContractDTO:
      required:
        - id
        - filename
      type: object
      properties:
        id:
          type: integer
          format: int64
        filename:
          type: string

    UserVipStatusDTO:
      required:
        - managementMode
        - managementModeUpdateTime
        - purchaseVolumes
      type: object
      properties:
        managementMode:
          $ref: '#/components/schemas/VipStatusManagementModeDTO'
        managementModeUpdateTime:
          type: string
          format: date-time
        purchaseVolumes:
          type: array
          items:
            $ref: '#/components/schemas/UserPurchaseVolumeDTO'

    UserPurchaseVolumeDTO:
      required:
        - year
        - amount
      type: object
      properties:
        year:
          type: integer
          format: int32
        amount:
          type: number
          format: double

    UserTrustInfoDTO:
      required:
        - managementMode
        - managementModeUpdateTime
      type: object
      properties:
        managementMode:
          $ref: '#/components/schemas/TrustInfoManagementModeDTO'
        managementModeUpdateTime:
          type: string
          format: date-time

    PredicateType:
      type: string
      enum:
        - AND
        - OR

    UserSex:
      type: string
      enum:
        - MALE
        - FEMALE
        - BOY
        - GIRL
        - ADULT
        - CHILD

    UserType:
      type: string
      enum:
        - SIMPLE_USER
        - IP
        - OOO

    BanType:
      type: string
      enum:
        - USER_BAN
        - COMMENT_BAN
        - PUBLISH_BAN
        - STORIES_BAN
        - BARGAIN_BAN
        - STREAM_BAN
        - WARNING
        - OSOCIAL_POST_BAN
        - OSOCIAL_COMMENT_BAN
        - COMMENT_SHADOW_BAN


    AuthorityName:
      type: string
      enum:
        - ADMIN
        - PRODUCT_MODERATION
        - USER_MODERATION
        - USERFILE_MODERATION
        - AUTHORITY_MODERATION
        - CONTENT_CREATE
        - CONTENT_DELETE
        - ORDER_MODERATION
        - CAN_VIEW_ALL_PRODUCTS
        - OFFER_MODERATION
        - RETOUCHING_MODERATION
        - COMMENT_MODERATION
        - PROMOCODES_MODERATION
        - PROMOCODES_ADMINISTRATION
        - CURRENCY_RATE_MODERATION
        - CONTENT_MODERATION
        - MASTER_USER
        - STORY_MODERATION
        - ORDER_RETURNS
        - ORDER_PAYOUTS
        - ORDER_CONCIERGE_PAYOUTS
        - STREAM_MODERATION
        - EXPERTISE
        - ORDER_PREPAYMENTS
        - ORDER_RETURN_COMPLETED
        - ORDER_RESOLVE_DISPUTE
        - ORDER_EXPERTISE_FINISH_ANY_DEFECTS_NEGOTIATION
        - BOUTIQUE_SALES
        - ORDER_MARKING_CODES
        - USER_BALANCE_CHANGE
        - PAYOUT_BY_CASH
        - CUSTOM_COMMISSION
        - LOGISTIC
        - SALE_REQUEST_MODERATION
        - PRODUCTS_BULK_EDIT
        - LOGISTIC_SEND_DELIVERY_COMPANY_MAIL_REQUEST
        - ORDER_REFUND_AMOUNT
        - ORDER_MANUAL_TRANSFER
        - SPLIT_ORDER
        - MARK_ORDER_AS_PREPARATION_FOR_PUBLICATION_REQUIRED
        - ORDER_VIEW_ALL_ORDER_SOURCES
        - ORDER_BOUTIQUE_0ST_ACTION
        - ORDER_BOUTIQUE_1ST_ACTION
        - ORDER_BOUTIQUE_2ND_ACTION
        - ORDER_VIEW_LIST_BY_LOCATION
        - USER_DELETE
        - ORDER_MANUAL_CHANGE_DELIVERY_STATE
        - ORDER_MANUAL_CANCEL
        - FAST_AUTO_SHIPMENT_TO_BOUTIQUE
        - ORDER_BOUTIQUE_PACKING_STEP
        - TEST_AUTHORITY_00
        - ORDER_SELLER_CONCIERGE_MOVE_TO_BOUTIQUE
        - USER_CHANGE_MAIL
        - COMMISSION_MODERATION
        - GAZPROM_BONUS
        - CONCIERGE_SOURCERS_ADMIN
        - SOURCER
        - CONCIERGE_SALES_ADMIN
        - SALES
        - MERCAUX_ADMIN
        - STOLESHNIKOV_ADMIN
        - STOLESHNIKOV_BOUTIQUE_SALESMAN
        - KUZNETSKY_BRIDGE_ADMIN
        - KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN
        - ACCESS_TO_MERKO
        - CREATE_PAYOUT_REQUEST
        - REFUSE_PAYOUT_REQUEST
        - CART_CLEAN_ADMIN

    VipStatusManagementModeDTO:
      type: string
      enum:
        - AUTO
        - ADMIN

    TrustInfoManagementModeDTO:
      type: string
      enum:
        - ADMIN

    ConfirmationActionPerformedRequest:
      type: object
      properties:
        id:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        type:
          $ref: '#/components/schemas/ConfirmationType'
        details:
          $ref: '#/components/schemas/ConfirmationDetails'
      required:
        - id
        - userId
        - type
        - details

    ConfirmationConfirmedRequest:
      allOf:
        - $ref: '#/components/schemas/ConfirmationActionPerformedRequest'

    ConfirmationTransferredToUserModerationRequest:
      allOf:
        - $ref: '#/components/schemas/ConfirmationActionPerformedRequest'
        - properties:
            comment:
              type: string

    ConfirmationEditedRequest:
      allOf:
        - $ref: '#/components/schemas/ConfirmationActionPerformedRequest'
        - properties:
            confirmed:
              type: boolean

    ConfirmationType:
      type: string
      enum:
        - USER_DESCRIPTION
        - USER_PHOTO

    ConfirmationDetails:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/ConfirmationDetailsType'
      required:
        - type

    ConfirmationDetailsType:
      type: string
      enum:
        - USER_DESCRIPTION_DETAILS
        - USER_PHOTO_DETAILS

    UserDescriptionConfirmationDetails:
      type: object
      allOf:
        - $ref: '#/components/schemas/ConfirmationDetails'
        - properties:
            id:
              type: integer
              format: int64
            description:
              type: string
      required:
        - id
        - description
        - type

    UserPhotoConfirmationDetails:
      type: object
      allOf:
        - $ref: '#/components/schemas/ConfirmationDetails'
        - properties:
            id:
              type: integer
              format: int64
            url:
              type: string
            imageId:
              type: integer
              format: int64
      required:
        - id
        - type
        - url
        - imageId