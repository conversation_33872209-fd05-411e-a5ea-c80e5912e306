openapi: 3.0.0
info:
  title: Oskelly O!Community API
  description: API для O!Community.
  version: 1.0.0
servers:
  - url: http://localhost:8080
    description: localhost
tags:
  - name: o-community-controller-api
    description: O!Community Controller API
paths:
  /api/v2/community/badge-layout:
    get:
      summary: Получить информацию о статусе пользователя в O!Community для наполнения баннера текущего пользователя
      operationId: getBadgeLayout
      tags:
        - o-community-controller-api
      responses:
        '200':
          description: Получение статуса пользователя в O!Community для наполнения баннера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommunityBadgeLayout'

  /api/v2/community/privileges-requirements:
    get:
      summary: Получить информацию о каждом статусе, его привилегиях, и требованиях, общее для всех пользователей
      operationId: getPrivilegesRequirements
      tags:
        - o-community-controller-api
      responses:
        '200':
          description: Получение списка требований для получения статуса
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/CommunityInfos'

  /api/v2/community/privileges-requirements/{userId}:
    get:
      summary: Получить информацию о каждом статусе, его привилегиях, и относительные требования, для указанного пользователя
      operationId: getUserPrivilegesRequirements
      tags:
        - o-community-controller-api
      parameters:
        - name: userId
          in: path
          required: true
          description: Идентификатор пользователя
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Получение списка требований для получения статуса
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/CommunityInfos'
        '404':
          description: Пользователь не найден


  /api/v2/community/transaction:
    get:
      summary: Получить информацию о количестве покупок и продаж для текущего пользователя
      operationId: getStatus
      tags:
        - o-community-controller-api
      responses:
        '200':
          description: Успешное получение информации о количестве покупок и продаж для текущего пользователя
          content:
            application/json:
              schema:
                $ref: './partial.yaml#/components/schemas/CommunityIndicator'

components:
  schemas:

    CommunityBadgeLayout:
      description: Описание статуса пользователя в O!Community для наполнение для баннера
      type: object
      properties:
        title:
          nullable: false
          type: string
          description: Описание статуса
          example: "Станьте частью O!Community"
        status:
          $ref: './partial.yaml#/components/schemas/CommunityStatus'
        description:
          nullable: false
          type: string
          description: Описание статуса
          example: "Покупайте и продавайте на платформе и станьте членом O’Community с привилегиями. <a href=\"myPrivileges\">Мои привилегии</a>"
        link:
          nullable: false
          type: string
          description: Ссылка на страницу пользователя в O!Community
          example: "<a href=\"https://o-community.com/user/my-priveleges\">Мои привилегии</a>"
