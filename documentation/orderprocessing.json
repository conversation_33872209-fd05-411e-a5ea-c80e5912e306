{"openapi": "3.0.3", "info": {"title": "Oskelly Main Service", "version": "2.2.1"}, "servers": [{"url": "http://0.0.0.0:8080", "description": "Inferred Url"}], "tags": [{"name": "op-admin-alert-controller", "description": "OP Admin Alert Controller"}, {"name": "op-change-order-controller", "description": "OP Change Order Controller"}, {"name": "op-delivery-controller", "description": "OP Delivery Controller"}, {"name": "op-order-controller", "description": "OP Order Controller"}, {"name": "op-order-expertise-controller", "description": "OP Order Expertise Controller"}], "paths": {"/adminpanel/api/v1/integrations/orderprocessing/alerts": {"post": {"tags": ["op-admin-alert-controller"], "summary": "get<PERSON><PERSON><PERSON>", "operationId": "getAlertsUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AlertRequestListDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupOrderAlertListDTO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/integration/orderprocessing/orders/{orderId}/change-dto": {"get": {"tags": ["op-change-order-controller"], "summary": "getChangeOrder", "operationId": "getChangeOrderUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeOrderDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/delivery/alert": {"post": {"tags": ["op-delivery-controller"], "summary": "createDeliveryToBuyerAdminAlert", "operationId": "createDeliveryToBuyerAdminAlertUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderAlertDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OrderAlertDTO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/delivery/company/list": {"get": {"tags": ["op-delivery-controller"], "summary": "getDeliveryCompanyList", "operationId": "getDeliveryCompanyListUsingGET", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryCompanyListDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/delivery/orders/{orderId}/address/info": {"get": {"tags": ["op-delivery-controller"], "summary": "getUsersDeliveryAddressInfo", "operationId": "getUsersDeliveryAddressInfoUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OrderUsersDeliveryAddressInfoDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/delivery/orders/{orderId}/company/id": {"get": {"tags": ["op-delivery-controller"], "summary": "getDeliveryCompanyIdForO2B", "operationId": "getDeliveryCompanyIdForO2BUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "from", "in": "query", "description": "from", "required": true, "style": "form", "schema": {"type": "string", "enum": ["BUYER", "OFFICE", "SELLER"]}}, {"name": "to", "in": "query", "description": "to", "required": true, "style": "form", "schema": {"type": "string", "enum": ["BUYER", "OFFICE", "SELLER"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/data": {"post": {"tags": ["op-order-controller"], "summary": "getOrderData", "operationId": "getOrderDataUsingPOST", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IntegrationOrderNumbersListRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OrderDataListResponseDTO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/numbers": {"post": {"tags": ["op-order-controller"], "summary": "getOrderNumbers", "operationId": "getOrderNumbersUsingPOST_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IntegrationOrderNumbersListRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/IntegrationOrderNumbersListResponseDTO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/comments": {"get": {"tags": ["op-order-controller"], "summary": "getOrderComments", "operationId": "getOrderCommentsUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CommentDTO"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}, "post": {"tags": ["op-order-controller"], "summary": "createTicket", "operationId": "createTicketUsingPOST", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCommentDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CommentDTO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/comments/{commentId}": {"delete": {"tags": ["op-order-controller"], "summary": "deleteTicket", "operationId": "deleteTicketUsingDELETE", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "commentId", "in": "path", "description": "commentId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK"}, "204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/delivery/info": {"get": {"tags": ["op-order-controller"], "summary": "getOrderDelivery", "operationId": "getOrderDeliveryUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDeliveryInfoDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/numbers": {"post": {"tags": ["op-order-controller"], "summary": "getOrderNumbers", "operationId": "getOrderNumbersUsingPOST", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IntegrationOrderNumbersRequestDTO"}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/IntegrationOrderNumbersDTO"}}}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/product/oskelly-size": {"get": {"tags": ["op-order-controller"], "summary": "getOskellySize", "operationId": "getOskellySizeUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "categoryId", "in": "query", "description": "categoryId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}, {"name": "sizeId", "in": "query", "description": "sizeId", "required": true, "style": "form", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderItemSizeDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/expertise/orders/{orderId}/orderposition/{orderPositionId}/finish": {"post": {"tags": ["op-order-expertise-controller"], "summary": "finishOrderPositionExpertise", "operationId": "finishOrderPositionExpertiseUsingPOST", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "orderPositionId", "in": "path", "description": "orderPositionId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPositionExpertiseFinishDTO"}}}}, "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/adminpanel/api/v1/integrations/orderprocessing/expertise/orders/{orderId}/orderposition/{orderPositionId}/precomputation/numbers": {"get": {"tags": ["op-order-expertise-controller"], "summary": "getPreComputationOrderNumbers", "operationId": "getPreComputationOrderNumbersUsingGET", "parameters": [{"name": "orderId", "in": "path", "description": "orderId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "orderPositionId", "in": "path", "description": "orderPositionId", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int64"}}, {"name": "previousAmount", "in": "query", "description": "previousAmount", "required": false, "style": "form", "schema": {"type": "number", "format": "double"}}, {"name": "amount", "in": "query", "description": "amount", "required": true, "style": "form", "schema": {"type": "number", "format": "double"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OrderItemDefectMatchingReconciliationPreComputationDTO"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}}, "components": {"schemas": {"AlertRequestListDTO": {"title": "AlertRequestListDTO", "type": "object", "properties": {"objectIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ChangeOrderDTO": {"title": "ChangeOrderDTO", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "sellerCounterpartyId": {"type": "integer", "format": "int64"}}}, "CommentDTO": {"title": "CommentDTO", "required": ["author", "authorId", "commentId", "datetime", "isPinned", "message"], "type": "object", "properties": {"author": {"maxLength": 255, "minLength": 1, "type": "string"}, "authorId": {"type": "integer", "format": "int64"}, "commentId": {"type": "string"}, "datetime": {"type": "string", "format": "date-time"}, "isPinned": {"type": "boolean", "example": false}, "message": {"maxLength": 2147483647, "minLength": 1, "type": "string"}}}, "ConciergeClientChannelDTO": {"title": "ConciergeClientChannelDTO", "required": ["id", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "ConciergeDataDTO": {"title": "ConciergeDataDTO", "type": "object", "properties": {"adminComment": {"type": "string"}, "conciergeClientChannel": {"$ref": "#/components/schemas/ConciergeClientChannelDTO"}, "deliveryToBuyerDateHint": {"type": "string", "format": "date-time"}}}, "CreateCommentDTO": {"title": "CreateCommentDTO", "required": ["message", "userId"], "type": "object", "properties": {"message": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}}}, "DeliveryCompanyDTO": {"title": "DeliveryCompanyDTO", "required": ["id", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}}}, "DeliveryCompanyListDTO": {"title": "DeliveryCompanyListDTO", "required": ["list"], "type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryCompanyDTO"}}}}, "GroupOrderAlertDTO": {"title": "GroupOrderAlertDTO", "required": ["list", "objectId"], "type": "object", "properties": {"list": {"type": "array", "items": {"type": "string"}}, "objectId": {"type": "integer", "format": "int64"}}}, "GroupOrderAlertListDTO": {"title": "GroupOrderAlertListDTO", "type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/GroupOrderAlertDTO"}}}}, "IntegrationOrderItemNumbersDTO": {"title": "IntegrationOrderItemNumbersDTO", "type": "object", "properties": {"companyProfit": {"type": "number", "format": "bigdecimal"}, "companyProfitPercent": {"type": "number", "format": "double"}, "orderItemAmountWithoutDelivery": {"type": "number", "format": "bigdecimal"}, "orderItemRrpPrice": {"type": "number", "format": "bigdecimal"}, "orderPositionId": {"type": "integer", "format": "int64"}, "promocodeAmount": {"type": "number", "format": "bigdecimal"}, "sellerProfit": {"type": "number", "format": "bigdecimal"}, "webSitePrice": {"type": "number", "format": "bigdecimal"}}}, "IntegrationOrderItemNumbersRequestDTO": {"title": "IntegrationOrderItemNumbersRequestDTO", "required": ["amount", "orderPositionId"], "type": "object", "properties": {"amount": {"type": "number", "format": "bigdecimal"}, "orderPositionId": {"type": "integer", "format": "int64"}}}, "IntegrationOrderNumbersDTO": {"title": "IntegrationOrderNumbersDTO", "required": ["items"], "type": "object", "properties": {"buyerDeliveryAmount": {"type": "number", "format": "bigdecimal"}, "companyDeliveryAmount": {"type": "number", "format": "bigdecimal"}, "companyProfit": {"type": "number", "format": "bigdecimal"}, "companyProfitWithoutPromocode": {"type": "number", "format": "bigdecimal"}, "companyProfitWithoutPromocodePercent": {"type": "number", "format": "double"}, "conciergeData": {"$ref": "#/components/schemas/ConciergeDataDTO"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationOrderItemNumbersDTO"}}, "orderAmountWithPromocodeWithDelivery": {"type": "number", "format": "bigdecimal"}, "orderAmountWithPromocodeWithoutDelivery": {"type": "number", "format": "bigdecimal"}, "orderAmountWithoutPromocodeAndWithoutDelivery": {"type": "number", "format": "bigdecimal"}, "orderId": {"type": "integer", "format": "int64"}, "orderItemCount": {"type": "integer", "format": "int32"}, "promocode": {"maxLength": 255, "minLength": 0, "type": "string"}, "promocodeAmount": {"type": "number", "format": "bigdecimal"}, "promocodePercent": {"type": "number", "format": "double"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/OrderExtraPropDTO"}}, "sellerProfit": {"type": "number", "format": "bigdecimal"}, "tags": {"type": "array", "items": {"type": "string"}}}}, "IntegrationOrderNumbersListRequestDTO": {"title": "IntegrationOrderNumbersListRequestDTO", "required": ["list"], "type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationOrderNumbersRequestDTO"}}}}, "IntegrationOrderNumbersListResponseDTO": {"title": "IntegrationOrderNumbersListResponseDTO", "type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationOrderNumbersDTO"}}}}, "IntegrationOrderNumbersRequestDTO": {"title": "IntegrationOrderNumbersRequestDTO", "required": ["items", "orderId"], "type": "object", "properties": {"isCommentRequired": {"type": "boolean", "example": false}, "isConciergeDataRequired": {"type": "boolean", "example": false}, "isItemsNumbersRequired": {"type": "boolean", "example": false}, "isPropertiesRequired": {"type": "boolean", "example": false}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/IntegrationOrderItemNumbersRequestDTO"}}, "orderId": {"type": "integer", "format": "int64"}}}, "IntegrationOrderUserDTO": {"title": "IntegrationOrderUserDTO", "type": "object", "properties": {"email": {"maxLength": 255, "minLength": 1, "type": "string"}, "fullname": {"maxLength": 255, "minLength": 1, "type": "string"}, "id": {"type": "integer", "format": "int64"}, "nickname": {"maxLength": 255, "minLength": 1, "type": "string"}, "type": {"type": "string", "enum": ["PRO", "SIMPLE"]}}}, "OrderAlertDTO": {"title": "OrderAlertDTO", "type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "productId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["AdminOrderAlertBankRejectedOperation", "AdminOrderAlertWaybillCreationErrorWrongAddress", "AdminOrderAlertWaybillDeliveryFromSellerFailedRaiseComplaint", "AdminOrderAlertWaybillDeliveryProblemNoDeliveryAttempt", "AdminOrderAlertWaybillDeliveryProblemReceiptRenouncement", "AdminOrderAlertWaybillDeliveryProblemRecipientAbsence", "AdminOrderAlertWaybillDeliveryProblemTraceOpen", "AdminOrderAlertWaybillDeliveryProblemWrongAddress", "AdminOrderAlertWaybillDeliveryToBuyerFailedRaiseComplaint", "AdminOrderAlertWaybillPostponedAgreedDeliveryDate", "AdminOrderAlertWaybillPostponedCarrierProblem", "AdminOrderAlertWaybillPostponedRedirect", "OrderDeliveredToBuyerRequestConfirmationNotification", "OrderNotDeliveredToBuyerRequestConfirmationNotification"]}, "waybillId": {"type": "integer", "format": "int64"}}}, "OrderDataListResponseDTO": {"title": "OrderDataListResponseDTO", "required": ["list"], "type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/OrderDataResponseDTO"}}}}, "OrderDataResponseDTO": {"title": "OrderDataResponseDTO", "type": "object", "properties": {"alerts": {"$ref": "#/components/schemas/GroupOrderAlertDTO"}, "buyer": {"$ref": "#/components/schemas/IntegrationOrderUserDTO"}, "comments": {"type": "array", "items": {"$ref": "#/components/schemas/CommentDTO"}}, "daysAfterDelivery": {"type": "integer", "format": "int64"}, "numbers": {"$ref": "#/components/schemas/IntegrationOrderNumbersDTO"}, "orderId": {"type": "integer", "format": "int64"}, "seller": {"$ref": "#/components/schemas/IntegrationOrderUserDTO"}}}, "OrderDeliveryInfoDTO": {"title": "OrderDeliveryInfoDTO", "type": "object", "properties": {"buyer": {"$ref": "#/components/schemas/OrderUserDeliveryDTO"}, "buyerWaybill": {"$ref": "#/components/schemas/OrderDeliveryWaybillDTO"}, "pickupDate": {"type": "string", "format": "date"}, "seller": {"$ref": "#/components/schemas/OrderUserDeliveryDTO"}, "sellerWaybill": {"$ref": "#/components/schemas/OrderDeliveryWaybillDTO"}}}, "OrderDeliveryWaybillDTO": {"title": "OrderDeliveryWaybillDTO", "type": "object", "properties": {"deliveryAmount": {"type": "number", "format": "double"}, "deliveryCompanyName": {"maxLength": 255, "minLength": 1, "type": "string"}, "deliveryDateTime": {"type": "string", "format": "date-time"}, "deliveryMessage": {"type": "string"}, "interval": {"type": "string"}, "pickupDateTime": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["DELIVERED_FROM_BUYER_TO_OFFICE", "DELIVERED_FROM_SELLER_TO_OFFICE", "DELIVERED_TO_BUYER", "DELIVERED_TO_SELLER", "DELIVERY_IN_MOSCOW", "DELIVERY_TODAY_TO_BUYER", "FROM_BUYER_TO_OFFICE", "FROM_OFFICE_TO_BUYER", "FROM_OFFICE_TO_SELLER", "FROM_SELLER_TO_OFFICE", "JUST_CREATED", "JUST_CREATED_TO_BUYER", "OURSELVES_DELIVERY_TODAY_TO_BUYER", "OURSELVES_DELIVERY_TO_BUYER", "OURSELVES_FROM_BUYER_TO_OFFICE", "OURSELVES_FROM_OFFICE_TO_BUYER", "OURSELVES_FROM_OFFICE_TO_SELLER", "OURSELVES_FROM_SELLER_TO_OFFICE", "OURSELVES_PICKING_UP_FROM_BUYER", "OURSELVES_PICKING_UP_FROM_OFFICE", "OURSELVES_PICKING_UP_FROM_SELLER", "PICKING_UP_FROM_BUYER", "PICKING_UP_FROM_OFFICE", "PICKING_UP_FROM_SELLER", "PICKUP_DECLINED", "PICKUP_IN_MOSCOW"]}, "waybillId": {"type": "string"}}}, "OrderExtraPropDTO": {"title": "OrderExtraPropDTO", "required": ["name", "value"], "type": "object", "properties": {"groupName": {"type": "string"}, "name": {"type": "string"}, "value": {"type": "string"}}}, "OrderItemDefectMatchingReconciliationPreComputationDTO": {"title": "OrderItemDefectMatchingReconciliationPreComputationDTO", "required": ["companyProfit", "newCompanyProfit", "newSellerProfit", "newWebSitePrice", "sellerProfit", "webSitePrice"], "type": "object", "properties": {"companyProfit": {"type": "number", "format": "bigdecimal"}, "newCompanyProfit": {"type": "number", "format": "bigdecimal"}, "newSellerProfit": {"type": "number", "format": "bigdecimal"}, "newWebSitePrice": {"type": "number", "format": "bigdecimal"}, "sellerProfit": {"type": "number", "format": "bigdecimal"}, "webSitePrice": {"type": "number", "format": "bigdecimal"}}}, "OrderItemSizeDTO": {"title": "OrderItemSizeDTO", "required": ["type", "value"], "type": "object", "properties": {"type": {"$ref": "#/components/schemas/OrderItemSizeTypeDTO"}, "value": {"$ref": "#/components/schemas/OrderItemSizeValueDTO"}}}, "OrderItemSizeTypeDTO": {"title": "OrderItemSizeTypeDTO", "required": ["abbreviation", "description"], "type": "object", "properties": {"abbreviation": {"maxLength": 255, "minLength": 1, "type": "string"}, "description": {"maxLength": 255, "minLength": 1, "type": "string"}}}, "OrderItemSizeValueDTO": {"title": "OrderItemSizeValueDTO", "required": ["id", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"maxLength": 255, "minLength": 1, "type": "string"}}}, "OrderPositionExpertiseFinishDTO": {"title": "OrderPositionExpertiseFinishDTO", "required": ["isPassed"], "type": "object", "properties": {"cleaningPrice": {"type": "number", "format": "bigdecimal"}, "discountComment": {"type": "string"}, "discountPrice": {"type": "number", "format": "bigdecimal"}, "isPassed": {"type": "boolean", "example": false}, "orderPositionId": {"type": "integer", "format": "int64"}, "rejectedReason": {"type": "string"}}}, "OrderUserDeliveryAddressConfirmationDetailsDTO": {"title": "OrderUserDeliveryAddressConfirmationDetailsDTO", "type": "object", "properties": {"address": {"type": "string"}, "city": {"type": "string"}, "isAddressValidated": {"type": "boolean", "example": false}, "isCityValidated": {"type": "boolean", "example": false}, "isValidated": {"type": "boolean", "example": false}, "region": {"type": "string"}}}, "OrderUserDeliveryAddressDTO": {"title": "OrderUserDeliveryAddressDTO", "type": "object", "properties": {"addressLine": {"maxLength": 255, "minLength": 1, "type": "string"}, "city": {"maxLength": 255, "minLength": 1, "type": "string"}, "id": {"type": "integer", "format": "int64"}, "isFiasValidated": {"type": "boolean", "example": false}, "zipCode": {"maxLength": 255, "minLength": 1, "type": "string"}}}, "OrderUserDeliveryDTO": {"title": "OrderUserDeliveryDTO", "required": ["address", "fullname", "isBestFriend", "isCelebrity", "nickname", "ordersCount", "phone", "profileUrl", "salesCount", "userId"], "type": "object", "properties": {"address": {"$ref": "#/components/schemas/OrderUserDeliveryAddressDTO"}, "comment": {"type": "string"}, "companyName": {"maxLength": 255, "minLength": 1, "type": "string"}, "fullname": {"maxLength": 255, "minLength": 1, "type": "string"}, "isBanned": {"type": "boolean", "example": false}, "isBestFriend": {"type": "boolean", "example": false}, "isCelebrity": {"type": "boolean", "example": false}, "isVip": {"type": "boolean", "example": false}, "nickname": {"maxLength": 255, "minLength": 1, "type": "string"}, "ordersCount": {"type": "integer", "format": "int64"}, "phone": {"maxLength": 255, "minLength": 1, "type": "string"}, "profileUrl": {"type": "string"}, "salesCount": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["PRO", "SIMPLE"]}, "usedeskUrl": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}}}, "OrderUsersDeliveryAddressInfoDTO": {"title": "OrderUsersDeliveryAddressInfoDTO", "type": "object", "properties": {"buyerAddress": {"$ref": "#/components/schemas/OrderUserDeliveryAddressConfirmationDetailsDTO"}, "sellerAddress": {"$ref": "#/components/schemas/OrderUserDeliveryAddressConfirmationDetailsDTO"}, "sellerCounterpartyId": {"type": "integer", "format": "int64"}}}}}}