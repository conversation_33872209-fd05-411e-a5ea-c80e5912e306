# TODO REQUIRED ID
openapi: 3.0.3

info:
  title: Oskelly API v2, partial
  description: Oskelly API v2, partial
  version: "1.0"

components:
  schemas:
    PromoCodeResetRange:
      type: string
      enum:
        - WEEK
        - MONTH
        - QUARTER
        - HALF_YEAR
        - YEAR
    Api2ResponseOfAddressEndpointDTO:
      title: Api2ResponseOfAddressEndpointDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AddressEndpointDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfCategoryTree:
      title: Api2ResponseOfCategoryTree
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CategoryTree'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfCategoryDTO:
      title: Api2ResponseOfListOfCategoryDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDTORes'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfBrandDTO:
      title: Api2ResponseOfListOfBrandDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BrandDTORes'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfAttributeDTO:
      title: Api2ResponseOfListOfAttributeDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AttributeDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfOrderSourceInfoDTO:
      title: Api2ResponseOfListOfOrderSourceInfoDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/OrderSourceInfoDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfLegalEntitiesDTO:
      title: Api2ResponseOfListOfLegalEntitiesDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/LegalEntityDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfDictionaryDTO:
      title: Api2ResponseOfListOfLegalEntitiesDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/DictionaryDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfProductTagDTO:
      title: Api2ResponseOfListOfProductTagDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductTagDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOflong:
      title: Api2ResponseOfListOflong
      type: object
      properties:
        data:
          type: array
          items:
            type: integer
            format: int64
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfProductDTO:
      title: Api2ResponseOfProductDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductDTORes'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfProductIdDTO:
      title: Api2ResponseOfProductIdDTO
      type: object
      properties:
        data:
          type: integer
          format: int64
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfProductDTOLite:
      title: Api2ResponseOfProductDTOLite
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ProductDTOLite'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfProductDTO:
      title: Api2ResponseOfListOfProductDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductDTORes'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfPageOfProductDTO:
      title: Api2ResponseOfPageOfProductDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfProductDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfPageOfProductDTOIntegrationLite:
      title: Api2ResponseOfPageOfProductDTOIntegrationLite
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfProductDTOIntegrationLite'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfProductImageDTO:
      title: Api2ResponseOfListOfProductImageDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductImageDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfCounterpartyDTO:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/CounterpartyDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    AttributeDTO:
      title: AttributeDTO
      type: object
      properties:
        attributeValues:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValueDTO'
        id:
          type: integer
          format: int64
        isRequired:
          type: boolean
        kind:
          type: string
          enum:
            - GENERIC
            - MATERIAL
            - COLOR
        name:
          type: string
        showFilter:
          type: boolean
    AttributeWithValueDTO:
      title: AttributeWithValueDTO
      type: object
      properties:
        attribute:
          $ref: '#/components/schemas/AttributeDTO'
        attributeValue:
          $ref: '#/components/schemas/AttributeValueDTO'
    AttributeValueDTO:
      title: AttributeValueDTO
      type: object
      required:
        - id
      properties:
        icon:
          type: string
        id:
          type: integer
          format: int64
        ofValue:
          type: string
        pluralGenitiveValue:
          type: string
        singularGenitiveValue:
          type: string
        transliterateValue:
          type: string
        value:
          type: string
    AdditionalSizeDTO:
      title: AdditionalSizeDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        image:
          type: string
        isRequired:
          type: boolean
        name:
          type: string
        transliterateName:
          type: string
        value:
          type: integer
          format: int32
    PayoutInfoDTO:
      title: OfferDTO
      type: object
      properties:
        value:
          type: integer
          format: bigdecimal
        currencyCode:
          type: string
    SizeValueDTO:
      title: SizeValueDTO
      type: object
      properties:
        additionalSizeValues:
          type: object
          additionalProperties:
            type: integer
            format: int32
        payoutInfo:
          $ref: '#/components/schemas/PayoutInfoDTO'
        sku:
          type: string
        categorySizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        categorySizeValue:
          type: string
        count:
          type: integer
          format: int32
        id:
          type: integer
          format: int64
        interestingSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        interestingSizeValue:
          type: string
        offer:
          $ref: '#/components/schemas/OfferDTO'
        productSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        productSizeValue:
          type: string
    SizeValueDTORes:
      title: SizeValueDTORes
      type: object
      properties:
        payoutInfo:
          $ref: '#/components/schemas/PayoutInfoDTO'
        sku:
          type: string
        additionalSizeValues:
          type: object
          additionalProperties:
            type: integer
            format: int32
        categorySizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        categorySizeValue:
          type: string
        count:
          type: integer
          format: int32
        id:
          type: integer
          format: int64
        interestingSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        interestingSizeValue:
          type: string
        offer:
          $ref: '#/components/schemas/OfferDTORes'
        productSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        productSizeValue:
          type: string
        productCustomSizeType:
          type: string
        productCustomSizeValue:
          type: string

    SizeValueDTOReq:
      title: SizeValueDTOReq
      type: object
      properties:
        payoutInfo:
          $ref: '#/components/schemas/PayoutInfoDTO'
        sku:
          type: string
        additionalSizeValues:
          type: object
          additionalProperties:
            type: integer
            format: int32
        categorySizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        categorySizeValue:
          type: string
        count:
          type: integer
          format: int32
        id:
          type: integer
          format: int64
        interestingSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        interestingSizeValue:
          type: string
        offer:
          "$ref": "#/components/schemas/OfferDTOReq"
        productSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        productSizeValue:
          type: string
        productCustomSizeType:
          type: string
        productCustomSizeValue:
          type: string

    OfferDTO:
      title: OfferDTO
      type: object
      properties:
        brandId:
          type: integer
          format: int64
        categoryId:
          type: integer
          format: int64
        consumed:
          type: boolean
        id:
          type: integer
          format: int64
        isSizeAvailable:
          type: boolean
        negotiatedPrice:
          type: number
          format: bigdecimal
        offerStatus:
          type: string
          enum:
            - ACCEPTED
            - PENDING
            - REJECTED
        offerorId:
          type: integer
          format: int64
        price:
          type: number
          format: bigdecimal
        product:
          $ref: '#/components/schemas/ProductDTO'
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        sizeId:
          type: integer
          format: int64
    OfferDTORes:
      title: OfferDTORes
      type: object
      properties:
        brandId:
          type: integer
          format: int64
        categoryId:
          type: integer
          format: int64
        consumed:
          type: boolean
        id:
          type: integer
          format: int64
        isSizeAvailable:
          type: boolean
        negotiatedPrice:
          type: number
          format: bigdecimal
        offerStatus:
          type: string
          enum:
            - ACCEPTED
            - PENDING
            - REJECTED
        offerorId:
          type: integer
          format: int64
        price:
          type: number
          format: bigdecimal
        product:
          $ref: '#/components/schemas/ProductDTORes'
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        sizeId:
          type: integer
          format: int64
    OfferDTOReq:
      title: OfferDTOReq
      type: object
      properties:
        brandId:
          type: integer
          format: int64
        categoryId:
          type: integer
          format: int64
        consumed:
          type: boolean
        id:
          type: integer
          format: int64
        isSizeAvailable:
          type: boolean
        negotiatedPrice:
          type: number
          format: bigdecimal
        offerStatus:
          type: string
          enum:
            - ACCEPTED
            - PENDING
            - REJECTED
        offerorId:
          type: integer
          format: int64
        price:
          type: number
          format: bigdecimal
        product:
          "$ref": "#/components/schemas/ProductDTOReq"
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        sizeId:
          type: integer
          format: int64

    DescriptionAttributeView:
      title: DescriptionAttributeView
      type: object
      properties:
        attributeValueId:
          type: integer
          format: int64
        title:
          type: string
        value:
          type: string

    CommentDTO:
      title: CommentDTO
      type: object
      properties:
        id:
          type: integer
          format: int64
        images:
          type: array
          items:
            type: string
        parentCommentId:
          type: integer
          format: int64
        productId:
          type: integer
          format: int64
        productRequestId:
          type: integer
          format: int64
        publishedAtTime:
          type: integer
          format: int64
        publisher:
          $ref: '#/components/schemas/UserDTO'
        replyTo:
          type: string
        needsTranslate:
          type: boolean
        subComments:
          type: array
          items:
            $ref: '#/components/schemas/CommentDTO'
        text:
          type: string
        deletedAtTime:
          type: string
        editedAtTime:
          type: string

    CommentView:
      title: CommentView
      type: object
      properties:
        avatar:
          type: string
        id:
          type: integer
          format: int64
        images:
          type: array
          items:
            type: string
        isAnswer:
          type: boolean
        publishTime:
          type: string
        publishZonedDateTime:
          type: integer
          format: int64
        text:
          type: string
        user:
          type: string
        userId:
          type: integer
          format: int64
    CountryDTO:
      title: CountryDTO
      type: object
      properties:
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        name:
          type: string
        uiCurrencyCode:
          type: string
        environment:
          type: string
          enum:
            - RU
            - INT
        isoCodeAlpha2:
          type: string
        imageUrl:
          type: string
        requireZipcode:
          type: boolean
        id:
          type: integer
          format: int64
    CityDTO:
      title: CountryDTO
      type: object
      properties:
        name:
          type: string
        region:
          type: string
        id:
          type: integer
          format: int64
    BlankId:
      title: BlankId
      type: object
      properties:
        id:
          type: integer
          format: int64
    CurrencyDTO:
      title: CurrencyDTO
      type: object
      properties:
        name:
          type: string
        sign:
          type: string
        isoCode:
          type: string
        isoNumber:
          type: integer
          format: int64
        id:
          type: integer
          format: int64
        base:
          type: boolean
        active:
          type: boolean
        selectedByDefault:
          type: boolean
    AddressEndpointAggregationRequestDTO:
      title: AddressEndpointAggregationDTO
      type: object
      properties:
        physicalAddress:
          $ref: '#/components/schemas/AddressEndpointAggRequestDTO'
        billingAddress:
          $ref: '#/components/schemas/AddressEndpointAggRequestDTO'
        usePhysicalAddressForBilling:
          type: boolean

    AddressEndpointAggregationRequestUpdateDTO:
      title: AddressEndpointAggregationDTO
      type: object
      properties:
        physicalAddress:
          $ref: '#/components/schemas/AddressEndpointAggRequestUpdateDTO'
        billingAddress:
          $ref: '#/components/schemas/AddressEndpointAggRequestUpdateDTO'
        usePhysicalAddressForBilling:
          type: boolean
        id:
          type: integer
          format: int64

    AddressEndpointAggregationResponseDTO:
      title: AddressEndpointAggregationDTO
      type: object
      properties:
        physicalAddress:
          $ref: '#/components/schemas/AddressEndpointAggResponseDTO'
        billingAddress:
          $ref: '#/components/schemas/AddressEndpointAggResponseDTO'
        id:
          type: integer
          format: int64
        usePhysicalAddressForBilling:
          type: boolean
    AddressDTO:
      title: AddressDTO
      type: object
      properties:
        address:
          type: string
        addressBreakdown:
          $ref: '#/components/schemas/AddressBreakdownDTO'
        address2:
          type: string
        address3:
          type: string
        city:
          type: string
        cityData:
          $ref: '#/components/schemas/CityDTO'
        country:
          type: string
        countryData:
          $ref: '#/components/schemas/CountryDTO'
        id:
          type: integer
          format: int64
        region:
          type: string
        zipCode:
          type: string
        fiasId:
          type: string
        regionFiasId:
          type: string
        cityFiasId:
          type: string
        settlementFiasId:
          type: string
        dadataFullAddress:
          type: string
        fullAddress:
          type: string
        checked:
          type: boolean
        createTime:
          type: integer
          format: int64
        deleteTime:
          type: integer
          format: int64
        changeTime:
          type: integer
          format: int64
        fullCityName:
          type: string
        cis:
          type: boolean
    AddressBreakdownDTO:
      properties:
        street:
          type: string
        house:
          type: string
        flat:
          type: string
    AddressAggregationRequestDTO:
      title: AddressAggregationRequestDTO
      type: object
      properties:
        address:
          type: string
        address2:
          type: string
        address3:
          type: string
        city:
          type: integer
          format: int64
        country:
          type: integer
          format: int64
        zipCode:
          type: string
    AddressAggregationRequestUpdateDTO:
      title: AddressAggregationRequestUpdateDTO
      type: object
      properties:
        address:
          type: string
        address2:
          type: string
        address3:
          type: string
        city:
          type: integer
          format: int64
        country:
          type: integer
          format: int64
        zipCode:
          type: string
        id:
          type: integer
          format: int64
    AddressAggregationResponseDTO:
      title: AddressAggregationResponseDTO
      type: object
      properties:
        address:
          type: string
        address2:
          type: string
        address3:
          type: string
        cityData:
          $ref: '#/components/schemas/CityDTO'
        countryData:
          $ref: '#/components/schemas/CountryDTO'
        id:
          type: integer
          format: int64
        zipCode:
          type: string
    AddressEndpointDTO:
      title: AddressEndpointDTO
      type: object
      properties:
        address:
          $ref: '#/components/schemas/AddressDTO'
        firstName:
          type: string
        id:
          type: integer
          format: int64
        lastName:
          type: string
        patronymicName:
          type: string
        phone:
          type: string
        deliveryCost:
          type: number
          format: bigdecimal
        deleteTime:
          type: integer
          format: int64
        changeTime:
          type: integer
          format: int64

    AddressEndpointAggRequestDTO:
      title: AddressEndpointDTO
      type: object
      properties:
        address:
          $ref: '#/components/schemas/AddressAggregationRequestDTO'
        firstName:
          type: string
        lastName:
          type: string
        patronymicName:
          type: string
        phone:
          type: string

    AddressEndpointAggRequestUpdateDTO:
      title: AddressEndpointDTO
      type: object
      properties:
        address:
          $ref: '#/components/schemas/AddressAggregationRequestUpdateDTO'
        firstName:
          type: string
        lastName:
          type: string
        patronymicName:
          type: string
        phone:
          type: string
        id:
          type: integer
          format: int64

    AddressEndpointAggResponseDTO:
      title: AddressEndpointDTO
      type: object
      properties:
        address:
          $ref: '#/components/schemas/AddressAggregationResponseDTO'
        firstName:
          type: string
        id:
          type: integer
          format: int6
        lastName:
          type: string
        patronymicName:
          type: string
        phone:
          type: string

    AddressAggregationDTO:
      properties:
        id:
          type: integer
          format: int64
        zipCode:
          type: string
        countryData:
          $ref: '#/components/schemas/CountryDTO'
        cityData:
          $ref: '#/components/schemas/CityDTO'
        address:
          type: string
        address2:
          type: string
        address3:
          type: string
        createTime:
          type: string
          format: date-time
        changeTime:
          type: string
          format: date-time

    BreadcrumbDTO:
      title: BreadcrumbDTO
      type: object
      properties:
        name:
          type: string
        url:
          type: string

    UserDTO:
      title: UserDTO
      type: object
      properties:
        acceptsReturns:
          type: boolean
        avatarPath:
          type: string
        birthDate:
          type: integer
          format: int64
        brandLikesCount:
          type: integer
          format: int32
        email:
          type: string
        firstChar:
          type: string
        fullName:
          type: string
        id:
          type: integer
          format: int64
        isFollowed:
          type: boolean
        isPro:
          type: boolean
        isTrusted:
          type: boolean
        likesCount:
          type: integer
          format: int32
        name:
          type: string
        nickname:
          type: string
        productLikesCount:
          type: integer
          format: int32
        productsCount:
          type: integer
          format: int32
        registrationTime:
          type: integer
          format: int64
        syncAgree:
          type: boolean
        syncSuccess:
          type: boolean
        sex:
          type: string
          enum:
            - ADULT
            - BOY
            - CHILD
            - FEMALE
            - GIRL
            - MALE
        adminProfileUrl:
          type: string
          description: Link to user profile in old admin view
        communityBadge:
          $ref: '#/components/schemas/CommunityBadge'
        commonTags:
          type: array
          items:
            $ref: '#/components/schemas/UserCommonTagDTO'
        sellerType:
          $ref: '#/components/schemas/SellerType'

    CommunityBadge:
      description: Бейджики пользователя в O!Community
      type: object
      properties:
        tags:
          type: array
          items:
            $ref: '#/components/schemas/CommunityTag'
        status:
          $ref: '#/components/schemas/CommunityStatus'

    CommunityStatus:
      description: Статус пользователя в O!Community
      type: object
      nullable: true
      properties:
        code:
          type: string
          description: Код
        name:
          type: string
          description: Название
          example: "o!beginner"

    CommunityPrivilegeGroups:
      description: Список привилегий для уровня статуса
      type: array
      items:
        $ref: '#/components/schemas/CommunityPrivilegeGroup'

    CommunityPrivilegeGroup:
      type: object
      description: Объект привилегий для уровня статуса
      properties:
        title:
          nullable: false
          description: Заголовок группы привилегий
          example: "Pop-up sale"
          type: string
        description:
          nullable: false
          description: Описание группы привилегий
          example: "Персональный поп-ап сейл ваших лотов в бутиках OSKELLY"
          type: string
        privileges:
          description: Список привилегий для уровня статуса
          nullable: false
          type: array
          items:
            $ref: '#/components/schemas/CommunityPrivilege'
        comingSoon:
          nullable: true
          description: true - означает что группа привилегий пока недоступна
          type: boolean
          example: true

    CommunityPrivilege:
      description: Наименование привилегии
      type: object
      properties:
        title:
          nullable: false
          description: Заголовок класса привилегий
          example: "Подарок на день рождения"
          type: string
        unlocked:
          nullable: true
          description: Доступна ли привилегия относительно предыдущего уровня
          type: boolean
          example: false
        comingSoon:
          nullable: true
          description: true - означает что привилегия пока недоступна
          type: boolean
          example: true

    CommunityInfos:
      description: Информация о статусах, привилегиях и требованиях для получения статуса
      type: array
      items:
        $ref: '#/components/schemas/CommunityInfo'

    CommunityInfo:
      type: object
      description: Информация о статусе, привилегиях и требованиях для получения статуса
      properties:
        status:
          $ref: '#/components/schemas/CommunityStatus'
        privilegeGroups:
          $ref: '#/components/schemas/CommunityPrivilegeGroups'
        requirements:
          $ref: '#/components/schemas/CommunityStatusRequirement'

    CommunityIndicator:
      type: object
      description: Количество покупок и продаж у текущего пользователя
      properties:
        purchaseCount:
          nullable: false
          type: integer
          description: Количество покупок у текущего пользователя
          example: 2
        saleCount:
          nullable: false
          type: integer
          description: Количество продаж у текущего пользователя
          example: 1

    CommunityStatusRequirement:
      description: Требования для получения статуса пользователя в O!Community
      type: object
      properties:
        purchaseRequired:
          nullable: true
          type: integer
          description: Сумма покупок для получения статуса
          example: 0
        saleRequired:
          nullable: true
          type: integer
          description: Сумма продаж для получения статуса
          example: 0
        type:
          nullable: false
          type: string
          description: Тип требования и\или
          enum:
            - AND
            - OR

    CommunityTag:
      description: Бейджик пользователя в O!Community
      type: object
      nullable: true
      properties:
        code:
          type: string
        name:
          type: string
          description: Значок пользователя
          example: "stylist"

    CounterpartyDTO:
      required: [ type ]
      properties:
        id:
          type: integer
          format: int64
        type:
          $ref: '#/components/schemas/CounterpartyType'
        isActive:
          type: boolean
        createTiestamp:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        jurAddress:
          $ref: '#/components/schemas/AddressDTO'
        physAddress:
          $ref: '#/components/schemas/AddressDTO'
        passport:
          type: string
        inn:
          type: string
        orgn:
          type: string
          description: Используется как для юр. лиц, так и для ИП (вместо ОГРНИП)
        kpp:
          type: string
        companyForm:
          type: string
          description: ООО, ЗАО. ПАО
        companyName:
          type: string
          description: Название компании без правовой формы и без кавычек (если только часть названия не выделена кавычками)
        bik:
          type: string
        correspondentAccount:
          type: string
        paymentAccount:
          type: string
        iban:
          type: string
        swiftCode:
          type: string
        routingNumber:
          type: string
        trn:
          type: string
        billingAddress:
          $ref: '#/components/schemas/AddressAggregationDTO'
        legalAddress:
          $ref: '#/components/schemas/AddressAggregationDTO'
        accountType:
          $ref: '#/components/schemas/BankAccountType'
        countryCounterpartyType:
          $ref: '#/components/schemas/CountryCounterpartyType'
        country:
          $ref: '#/components/schemas/CountryDTO'
        firstName:
          type: string
        patronymicName:
          type: string
        lastName:
          type: string
        phone:
          type: string
        isCard:
          type: boolean
        cardRefId:
          type: string
        cardNumber:
          type: string
        cardBindTime:
          type: string
          format: date-time
        cardUnbindTime:
          type: string
          format: date-time
        cardHolder:
          type: string
        cardBrand:
          type: string
        cardBindBank:
          type: string
        vatRateIndex:
          type: integer
          description: Ставка НДС
        cardExpireTime:
          type: string
          format: date-time
        isCardActive:
          type: boolean
        deleteTime:
          type: string
          format: date-time
        counterpartyImage:
          type: string
        isActiveInCurrentOrder:
          type: boolean
        contractNumber:
          type: string
        contractDate:
          type: string
          format: date-time
        directorName:
          type: string
        bankName:
          type: string
        unifiedSocialCreditCode:
          type: string
        businessRegistrationNumber:
          type: string

    Api2ResponseOfLoyaltyCardCardInfoDTO:
      title: Api2ResponseOfBonusesBonusCardInfoDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/LoyaltyCardCardInfoDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfBonusesBalanceDTO:
      title: Api2ResponseOfBonusesBalanceDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/BonusesBalanceDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfListOfBonusesBurningScheduleItemDTO:
      title: Api2ResponseOfListOfBonusesBurningScheduleItemDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BonusesBurningScheduleItemDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    Api2ResponseOfPageOfBonusesTransactionDTO:
      title: PageOfBonusesTransactionDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfBonusesTransactionDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string

    PageOfBonusesTransactionDTO:
      title: PageOfBonusesTransactionDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/BonusesTransactionDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32

    LoyaltyCardCardInfoDTO:
      description: Информация о бонусной карте пользователя
      type: object
      properties:
        barcode:
          nullable: false
          type: string
          description: Баркод
          example: 79991111111

    BonusesInfoWithBurningDTO:
      description: Структура позволяющая хранить информацию о начислении/списании бонусов и сумму ближайших к сгоранию бонусов
      type: object
      properties:
        bonusesInfo:
          nullable: false
          description: Информацию о начислении/списании бонусов
          $ref: '#/components/schemas/BonusesInfoDTO'
        burningAmount:
          nullable: false
          type: number
          description: Количество скоро сгоряющих баллов
          example: 20

    BonusesInfoDTO:
      description: Структура позволяющая хранить информацию о начислении/списании бонусов
      type: object
      properties:
        withdrawBonusesAmount:
          nullable: true
          description: Бонусы которые могут быть списаны
          $ref: '#/components/schemas/BonusesAmountDTO'
        transferBonusesAmount:
          nullable: true
          description: Бонусы которые могут быть начислены
          $ref: '#/components/schemas/BonusesAmountDTO'

    BonusesAmountDTO:
      description: Сгораемые и несгораемые баллы объединенные одним объектом
      type: object
      properties:
        bonuses:
          nullable: true
          type: number
          description: Количество сгорамых баллов
          example: 10
        money:
          nullable: true
          type: number
          description: Количество несгораемых баллов
          example: 10
        total:
          nullable: true
          type: number
          description: Общее количество баллов
          example: 10

    BonusesBalanceDTO:
      description: Бонусный баланс
      type: object
      properties:
        amount:
          nullable: false
          $ref: '#/components/schemas/BonusesAmountDTO'
        burningAmount:
          nullable: false
          type: number
          description: Количество скоро сгоряющих баллов
          example: 20

    BonusesBurningScheduleItemDTO:
      description: Элемент графика сгорания бонусов
      type: object
      properties:
        amount:
          nullable: false
          type: number
          description: Количество сгорающих баллов
          example: 10
        burningDate:
          nullable: false
          type: string
          format: date-time
        transactions:
          nullable: false
          type: array
          items:
            $ref: '#/components/schemas/BonusesTransactionDTO'

    BonusesTransactionDTO:
      description: Транзакции пользователя по бонусному счету
      type: object
      properties:
        amount:
          nullable: false
          description: Информация о сгораемых и несгораемых баллах
          $ref: '#/components/schemas/BonusesAmountDTO'
        trnDate:
          nullable: false
          description: Дата транзакции
          type: string
          format: date-time
        type:
          nullable: false
          $ref: '#/components/schemas/BonusesTransactionType'
        typeStr:
          nullable: false
          type: string
          description: Тип транзакции в локализоанном виде
        reason:
          type: string
          description: Причина транзакции. Псевдосправочник.
        description:
          type: string
          description: Комментарий к транзакции
        orderId:
          description: Идентификатор заказа
          type: integer
          format: int64
          example: 1234567
        orderDate:
          description: Дата заказа
          type: string
          format: date-time

    BonusesTransactionType:
      type: string
      enum:
        - TRANSFER
        - WITHDRAW
        - RETURN
        - EXPIRE

    BonusesType:
      type: string
      enum:
        - MONEY
        - BONUS

    UserCommonTagGroupDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        order:
          type: integer
          format: int32
        multiSelect:
          type: boolean
        tags:
          type: array
          items:
            $ref: '#/components/schemas/UserCommonTagDTO'

    UserCommonTagDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        code:
          type: string
        name:
          type: string
        extraData:
          type: string
        enabled:
          type: boolean

    UserCommonTagUsersCountDTO:
      type: object
      properties:
        tagId:
          type: integer
          format: int64
        usersCount:
          type: integer
          format: int64

    BrandDTO:
      title: BrandDTO
      type: object
      required:
        - id
      properties:
        description:
          type: string
        id:
          type: integer
          format: int64
        isHidden:
          type: boolean
        isLiked:
          type: boolean
        name:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductDTO'
        productsCount:
          type: integer
          format: int32
        transliterateName:
          type: string
        urlName:
          type: string
    BrandDTORes:
      title: BrandDTORes
      type: object
      properties:
        description:
          type: string
        id:
          type: integer
          format: int64
        isHidden:
          type: boolean
        isLiked:
          type: boolean
        name:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductDTORes'
        productsCount:
          type: integer
          format: int32
        transliterateName:
          type: string
        urlName:
          type: string
    BrandDTOReq:
      title: BrandDTOReq
      type: object
      properties:
        description:
          type: string
        id:
          type: integer
          format: int64
        isHidden:
          type: boolean
        isLiked:
          type: boolean
        name:
          type: string
        products:
          type: array
          items:
            "$ref": "#/components/schemas/ProductDTOReq"
        productsCount:
          type: integer
          format: int32
        transliterateName:
          type: string
        urlName:
          type: string
    BrandShortDTO:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
    CategoryDisplayNameDTO:
      type: object
      required:
        - id
        - displayName
      properties:
        id:
          type: integer
          format: int64
        displayName:
          type: string
    ProductModelDTORes:
      title: ProductModelDTORes
      type: object
      required:
        - id
        - name
        - brandId
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        brandId:
          type: integer
          format: int64
    CategoryDTO:
      title: CategoryDTO
      type: object
      properties:
        additionalSizes:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalSizeDTO'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeDTO'
        children:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDTO'
        defaultSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        displayName:
          type: string
        fullName:
          type: string
        hasChildren:
          type: boolean
        icon:
          type: string
        id:
          type: integer
          format: int64
        minPrice:
          type: integer
          format: int32
        pluralName:
          type: string
        productsCount:
          type: integer
          format: int32
        singularFullName:
          type: string
        singularName:
          type: string
        sizeValues:
          type: array
          items:
            $ref: '#/components/schemas/SizeValueDTO'
        url:
          type: string
    CategoryDTORes:
      title: CategoryDTORes
      type: object
      properties:
        additionalSizes:
          type: array
          items:
            $ref: '#/components/schemas/AdditionalSizeDTO'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeDTO'
        children:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDTORes'
        defaultSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        displayName:
          type: string
        fullName:
          type: string
        hasChildren:
          type: boolean
        icon:
          type: string
        id:
          type: integer
          format: int64
        minPrice:
          type: integer
          format: int32
        pluralName:
          type: string
        productsCount:
          type: integer
          format: int32
        singularFullName:
          type: string
        singularName:
          type: string
        sizeValues:
          type: array
          items:
            $ref: '#/components/schemas/SizeValueDTORes'
        url:
          type: string
    CategoryDTOReq:
      title: CategoryDTOReq
      type: object
      properties:
        additionalSizes:
          type: array
          items:
            "$ref": "#/components/schemas/AdditionalSizeDTO"
        attributes:
          type: array
          items:
            "$ref": "#/components/schemas/AttributeDTO"
        children:
          type: array
          items:
            "$ref": "#/components/schemas/CategoryDTOReq"
        defaultSizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        displayName:
          type: string
        fullName:
          type: string
        hasChildren:
          type: boolean
        icon:
          type: string
        id:
          type: integer
          format: int64
        minPrice:
          type: integer
          format: int32
        pluralName:
          type: string
        productsCount:
          type: integer
          format: int32
        singularFullName:
          type: string
        singularName:
          type: string
        sizeValues:
          type: array
          items:
            "$ref": "#/components/schemas/SizeValueDTOReq"
        url:
          type: string
    CategoryTree:
      title: CategoryTree
      type: object
      properties:
        rootCategory:
          $ref: '#/components/schemas/CategoryDTORes'

    ProductDTOLite:
      title: ProductDTOLite
      type: object
      properties:
        brand:
          $ref: '#/components/schemas/BrandDTO'
        category:
          $ref: '#/components/schemas/CategoryDTO'
        conditionId:
          type: integer
          format: int64
        conditionName:
          type: string
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        url:
          type: string

    ProductDTO:
      title: ProductDTO
      type: object
      properties:
        attributeValueIds:
          type: array
          items:
            type: integer
            format: int64
        attributeWithValues:
          type: array
          items:
            $ref: '#/components/schemas/AttributeWithValueDTO'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/DescriptionAttributeView'
        availabilityForBargainDate:
          type: string
          format: date-time
        brand:
          $ref: '#/components/schemas/BrandDTO'
        brandId:
          type: integer
          format: int64
        productModelId:
          type: integer
          format: int64
        productModel:
          $ref: '#/components/schemas/ProductModelDTORes'
        category:
          $ref: '#/components/schemas/CategoryDTO'
        categoryId:
          type: integer
          format: int64
        changeTime:
          type: string
          format: date-time
        changeTimestamp:
          type: integer
          format: int64
        comments:
          type: array
          items:
            $ref: '#/components/schemas/CommentView'
        commentsCount:
          type: integer
          format: int32
        commissionProc:
          type: number
          format: bigdecimal
        bonusesInfoWithBurning:
          $ref: '#/components/schemas/BonusesInfoWithBurningDTO'
        conditionId:
          type: integer
          format: int64
        createTime:
          type: string
          format: date-time
        createTimestamp:
          type: integer
          format: int64
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        defectImages:
          type: array
          items:
            $ref: '#/components/schemas/ProductImageDTO'
        description:
          type: string
        discount:
          type: integer
          format: int32
        fieldsLackingForModeration:
          type: array
          items:
            type: string
        sectionsLackingForModeration:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        higherPrice:
          type: number
          format: bigdecimal
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductImageDTO'
        isAtOffice:
          type: boolean
        isLiked:
          type: boolean
        isNewCollection:
          type: boolean
        isOurChoice:
          type: boolean
        isReadyForModeration:
          type: boolean
        isReadyForBargain:
          type: boolean
        isVintage:
          type: boolean
        isUsedInSaleRequest:
          type: boolean
        likesCount:
          type: integer
          format: int32
        model:
          type: string
        moderationHoursRemains:
          type: integer
          format: int64
        name:
          type: string
        origin:
          type: string
        ourChoiceStatusTime:
          type: string
          format: date-time
        parentCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDTO'
        pickupAddressEndpoint:
          $ref: '#/components/schemas/AddressEndpointDTO'
        pickupAddressEndpointId:
          type: integer
          format: int64
        pickupAddressEndpointAggregation:
          $ref: '#/components/schemas/AddressEndpointAggregationResponseDTO'
        pickupAddressEndpointAggregationId:
          type: integer
          format: int64
        prettyDiscount:
          type: integer
          format: int32
        prettyPrice:
          type: number
          format: bigdecimal
        price:
          type: number
          format: bigdecimal
        priceUpdateSubscribersCount:
          type: integer
          format: int32
        priceWithoutCommission:
          type: number
          format: bigdecimal
        primaryImageUrl:
          type: string
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        productStateTime:
          type: string
          format: date-time
        productStateTimestamp:
          type: integer
          format: int64
        productWasPublishedByNewPublisher:
          type: boolean
        publishTime:
          type: string
          format: date-time
        publishTimestamp:
          type: integer
          format: int64
        purchasePrice:
          type: number
          format: bigdecimal
        purchaseYear:
          type: integer
          format: int32
        rejectReason:
          $ref: '#/components/schemas/ProductRejectReasonDTO'
        rrpPrice:
          type: number
          format: bigdecimal
        rrpPriceCurrencyId:
          type: integer
          format: int64
        rrpPriceInCurrency:
          type: number
        seller:
          $ref: '#/components/schemas/UserDTO'
        sellerRecievesSum:
          type: number
          format: bigdecimal
        exclusiveSelectionTime:
          type: string
          format: date-time
        exclusiveSelectionTimeForLowStatuses:
          type: string
          format: date-time
        sendToModeratorTime:
          type: string
          format: date-time
        sentToModeratorTimestamp:
          type: integer
          format: int64
        serialNumber:
          type: string
        sex:
          type: string
          enum:
            - ADULT
            - BOY
            - CHILD
            - FEMALE
            - GIRL
            - MALE
        sizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        sizes:
          type: array
          items:
            $ref: '#/components/schemas/SizeValueDTO'
        startPrice:
          type: number
          format: bigdecimal
        storeCode:
          type: string
        subscribedOnPriceUpdates:
          type: boolean
        url:
          type: string
        vendorCode:
          type: string
        split:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        tabbySplit:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        yandexPlus:
          $ref: './partial.yaml#/components/schemas/YandexPlusInfo'
        countryOfOrigin:
          $ref: '#/components/schemas/CountryDTO'
    ProductDTORes:
      title: ProductDTORes
      type: object
      properties:
        attributeValueIds:
          type: array
          items:
            type: integer
            format: int64
        attributeWithValues:
          type: array
          items:
            $ref: '#/components/schemas/AttributeWithValueDTO'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/DescriptionAttributeView'
        availabilityForBargainDate:
          type: string
          format: date-time
        brand:
          $ref: '#/components/schemas/BrandDTORes'
        brandId:
          type: integer
          format: int64
        breadcrumbs:
          type: array
          items:
            $ref: '#/components/schemas/BreadcrumbDTO'
        productModelId:
          type: integer
          format: int64
        productModel:
          $ref: '#/components/schemas/ProductModelDTORes'
        category:
          $ref: '#/components/schemas/CategoryDTORes'
        categoryId:
          type: integer
          format: int64
        exclusiveSelectionTime:
          type: string
          format: date-time
        exclusiveSelectionTimeForLowStatuses:
          type: string
          format: date-time
        changeTime:
          type: string
          format: date-time
        changeTimestamp:
          type: integer
          format: int64
        comments:
          type: array
          items:
            $ref: '#/components/schemas/CommentView'
        lastCommentsTree:
          type: array
          items:
            "$ref": "#/components/schemas/CommentDTO"
        commentsCount:
          type: integer
          format: int32
        commentPostMode:
          type: string
          enum:
            - TEXT_AND_PHOTOS
            - TEXT
            - NONE
        commissionProc:
          type: number
          format: bigdecimal
        bonusesInfoWithBurning:
          $ref: '#/components/schemas/BonusesInfoWithBurningDTO'
        conditionId:
          type: integer
          format: int64
        conditionName:
          type: string
        createTime:
          type: string
          format: date-time
        createTimestamp:
          type: integer
          format: int64
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        defectImages:
          type: array
          items:
            $ref: '#/components/schemas/ProductImageDTO'
        description:
          type: string
        discount:
          type: integer
          format: int32
        fieldsLackingForModeration:
          type: array
          items:
            type: string
        sectionsLackingForModeration:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        higherPrice:
          type: number
          format: bigdecimal
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductImageDTO'
        inBoutique:
          type: boolean
        isCarryOver:
          type: boolean
        isInvestment:
          type: boolean
        isAtOffice:
          type: boolean
        isLiked:
          type: boolean
        needsTranslateDescription:
          type: boolean
        isNewCollection:
          type: boolean
        isOurChoice:
          type: boolean
        isReadyForModeration:
          type: boolean
        isReadyForBargain:
          type: boolean
        isVintage:
          type: boolean
        isSold:
          type: boolean
        isConcierge:
          type: boolean
        likesCount:
          type: integer
          format: int32
        model:
          type: string
        moderationHoursRemains:
          type: integer
          format: int64
        name:
          type: string
        origin:
          type: string
        ourChoiceStatusTime:
          type: string
          format: date-time
        parentCategories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDTORes'
        pickupAddressEndpoint:
          $ref: '#/components/schemas/AddressEndpointDTO'
        pickupAddressEndpointId:
          type: integer
          format: int64
        prettyDiscount:
          type: integer
          format: int32
        prettyPrice:
          type: number
          format: bigdecimal
        price:
          type: number
          format: bigdecimal
        priceUpdateSubscribersCount:
          type: integer
          format: int32
        priceWithoutCommission:
          type: number
          format: bigdecimal
        currentPriceInCurrency:
          type: number
          format: bigdecimal
        currentPriceCurrencyId:
          type: integer
          format: int64
        primaryImageUrl:
          type: string
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        productStateTime:
          type: string
          format: date-time
        productStateTimestamp:
          type: integer
          format: int64
        productWasPublishedByNewPublisher:
          type: boolean
        publishTime:
          type: string
          format: date-time
        publishTimestamp:
          type: integer
          format: int64
        purchasePrice:
          type: number
          format: bigdecimal
        purchaseYear:
          type: integer
          format: int32
        rejectReason:
          $ref: '#/components/schemas/ProductRejectReasonDTO'
        rrpPrice:
          type: number
          format: bigdecimal
        salesChannel:
          type: string
          enum:
            - WEBSITE
            - BOUTIQUE_AND_WEBSITE
            - STOCK_AND_BOUTIQUE_AND_WEBSITE
            - BOUTIQUE
        seasonId:
          type: integer
          format: int64
        season:
          $ref: '#/components/schemas/SeasonDTO'
        seller:
          $ref: '#/components/schemas/UserDTO'
        sellerRecievesSum:
          type: number
          format: bigdecimal
        sendToModeratorTime:
          type: string
          format: date-time
        sentToModeratorTimestamp:
          type: integer
          format: int64
        serialNumber:
          type: string
        sex:
          type: string
          enum:
            - ADULT
            - BOY
            - CHILD
            - FEMALE
            - GIRL
            - MALE
        sizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        sizes:
          type: array
          items:
            $ref: '#/components/schemas/SizeValueDTORes'
        startPrice:
          type: number
          format: bigdecimal
        storeCode:
          type: string
        subscribedOnPriceUpdates:
          type: boolean
        url:
          type: string
        vendorCode:
          type: string
        split:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        tabbySplit:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        yandexPlus:
          $ref: './partial.yaml#/components/schemas/YandexPlusInfo'
        sourceLink:
          type: string
        tags:
          type: array
          items:
            $ref: '#/components/schemas/ProductTagDTO'
        shortSeoDescription:
          type: string
        fullSeoDescription:
          type: string
        crossBorder:
          $ref: '#/components/schemas/ProductCrossBorderDTO'
        countryOfOrigin:
          $ref: '#/components/schemas/CountryDTO'
        postsExists:
          type: boolean
    ProductDTOIntegrationLiteRes:
      title: ProductDTOIntegrationLiteRes
      type: object
      properties:
        brandId:
          type: integer
          format: int64
        categoryId:
          type: integer
          format: int64
        conditionId:
          type: integer
          format: int64
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        storeCode:
          type: string
    ProductDTOReq:
      title: ProductDTOReq
      type: object
      properties:
        countryOfOrigin:
          $ref: '#/components/schemas/CountryDTO'
        attributeValueIds:
          type: array
          items:
            type: integer
            format: int64
        attributeWithValues:
          type: array
          items:
            "$ref": "#/components/schemas/AttributeWithValueDTO"
        attributes:
          type: array
          items:
            "$ref": "#/components/schemas/DescriptionAttributeView"
        availabilityForBargainDate:
          type: string
          format: date-time
        brand:
          "$ref": "#/components/schemas/BrandDTOReq"
        brandId:
          type: integer
          format: int64
        productModelId:
          type: integer
          format: int64
        productModel:
          $ref: '#/components/schemas/ProductModelDTORes'
        category:
          "$ref": "#/components/schemas/CategoryDTOReq"
        categoryId:
          type: integer
          format: int64
        changeTime:
          type: string
          format: date-time
        changeTimestamp:
          type: integer
          format: int64
        comments:
          type: array
          items:
            "$ref": "#/components/schemas/CommentView"
        commentsCount:
          type: integer
          format: int32
        commissionProc:
          type: number
          format: bigdecimal
        conditionId:
          type: integer
          format: int64
        createTime:
          type: string
          format: date-time
        createTimestamp:
          type: integer
          format: int64
        currency:
          $ref: '#/components/schemas/CurrencyDTO'
        defectImages:
          type: array
          items:
            "$ref": "#/components/schemas/ProductImageDTO"
        description:
          type: string
        discount:
          type: integer
          format: int32
        fieldsLackingForModeration:
          type: array
          items:
            type: string
        sectionsLackingForModeration:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        higherPrice:
          type: number
          format: bigdecimal
        images:
          type: array
          items:
            "$ref": "#/components/schemas/ProductImageDTO"
        isCarryOver:
          type: boolean
        isInvestment:
          type: boolean
        isAtOffice:
          type: boolean
        isLiked:
          type: boolean
        isNewCollection:
          type: boolean
        isOurChoice:
          type: boolean
        isReadyForModeration:
          type: boolean
        isReadyForBargain:
          type: boolean
        isVintage:
          type: boolean
        isConcierge:
          type: boolean
        likesCount:
          type: integer
          format: int32
        model:
          type: string
        moderationHoursRemains:
          type: integer
          format: int64
        name:
          type: string
        origin:
          type: string
        ourChoiceStatusTime:
          type: string
          format: date-time
        parentCategories:
          type: array
          items:
            "$ref": "#/components/schemas/CategoryDTOReq"
        pickupAddressEndpoint:
          "$ref": "#/components/schemas/AddressEndpointDTO"
        pickupAddressEndpointId:
          type: integer
          format: int64
        prettyDiscount:
          type: integer
          format: int32
        prettyPrice:
          type: number
          format: bigdecimal
        price:
          type: number
          format: bigdecimal
        priceUpdateSubscribersCount:
          type: integer
          format: int32
        priceWithoutCommission:
          type: number
          format: bigdecimal
        currentPriceInCurrency:
          type: number
          format: bigdecimal
        currentPriceCurrencyId:
          type: integer
          format: int64
        productId:
          type: integer
          format: int64
        productState:
          type: string
          enum:
            - BANED
            - DELETED
            - DRAFT
            - HIDDEN
            - NEED_MODERATION
            - NEED_RETOUCH
            - PUBLISHED
            - REJECTED
            - RETOUCH_DONE
            - SECOND_EDITION
            - SOLD
        productStateTime:
          type: string
          format: date-time
        productStateTimestamp:
          type: integer
          format: int64
        productWasPublishedByNewPublisher:
          type: boolean
        publishTime:
          type: string
          format: date-time
        publishTimestamp:
          type: integer
          format: int64
        purchasePrice:
          type: number
          format: bigdecimal
        purchaseYear:
          type: integer
          format: int32
        rejectReason:
          "$ref": "#/components/schemas/ProductRejectReasonDTO"
        rrpPrice:
          type: number
          format: bigdecimal
        rrpPriceCurrencyId:
          type: integer
          format: int64
        rrpPriceInCurrency:
          type: number
        seasonId:
          type: integer
          format: int64
        season:
          $ref: '#/components/schemas/SeasonDTO'
        seller:
          "$ref": "#/components/schemas/UserDTO"
        sellerRecievesSum:
          type: number
          format: bigdecimal
        sendToModeratorTime:
          type: string
          format: date-time
        sentToModeratorTimestamp:
          type: integer
          format: int64
        serialNumber:
          type: string
        sex:
          type: string
          enum:
            - ADULT
            - BOY
            - CHILD
            - FEMALE
            - GIRL
            - MALE
        sizeType:
          type: string
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        sizes:
          type: array
          items:
            "$ref": "#/components/schemas/SizeValueDTOReq"
        startPrice:
          type: number
          format: bigdecimal
        storeCode:
          type: string
        subscribedOnPriceUpdates:
          type: boolean
        url:
          type: string
        vendorCode:
          type: string
        split:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        tabbySplit:
          $ref: './partial.yaml#/components/schemas/SplitInfo'
        yandexPlus:
          $ref: './partial.yaml#/components/schemas/YandexPlusInfo'
        sourceLink:
          type: string
    ProductImageDTO:
      title: ProductImageDTO
      type: object
      properties:
        comment:
          type: string
        id:
          type: integer
          format: int64
        order:
          type: integer
          format: int32
        path:
          type: string
        alt:
          type: string
    ProductRejectReasonDTO:
      title: ProductRejectReasonDTO
      type: object
      properties:
        descriptionComment:
          type: string
        id:
          type: integer
          format: int64
        imageComment:
          type: string
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductImageDTO'
        oldDescription:
          type: string
        oldPrice:
          type: number
          format: bigdecimal
        otherComment:
          type: string
        price:
          type: number
          format: bigdecimal
        priceComment:
          type: string
        rejectorId:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    PageOfProductDTO:
      title: PageOfProductDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductDTORes'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    PageOfProductDTOIntegrationLite:
      title: PageOfProductDTOIntegrationLite
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductDTOIntegrationLiteRes'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    BankAccountType:
      type: string
      enum:
        - CHECKING
        - SAVING
    CountryCounterpartyType:
      type: string
      enum:
        - UAE_COUNTERPARTY
        - DEFAULT_COUNTERPARTY
    CounterpartyType:
      type: string
      enum:
        - PHYS
        - IP
        - JUR
        - CARD
        - BONUS_12_STOREEZ
        - INTERNATIONAL
        - INTERNATIONAL_LEGAL_ENTITY
    AdminCardDetailsView:
      properties:
        brand:
          type: string
        bank:
          type: string
        number:
          type: string
        expiration:
          type: string
    PrimaryPageType:
      title: PrimaryPageType
      type: string
      enum:
        - MALE
        - FEMALE
        - CHILD
        - LIFESTYLE
        - MALE_RESALE
        - FEMALE_RESALE
        - CHILD_RESALE
        - LIFESTYLE_RESALE
        - MALE_NEW
        - FEMALE_NEW
        - CHILD_NEW
        - LIFESTYLE_NEW

    PageOfProductRequestDTO:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductRequestDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
      title: PageOfProductRequestDTO
    ProductRequestDTO:
      type: object
      properties:
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/AttributeDTO'
        brands:
          type: array
          items:
            $ref: '#/components/schemas/BrandDTO'
        category:
          $ref: '#/components/schemas/CategoryDTO'
        commentsCount:
          type: integer
          format: int32
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/ProductConditionDTO'
        createTime:
          type: string
          format: date-time
        currencyCode:
          type: string
        description:
          type: string
        fromPrice:
          type: number
        id:
          type: integer
          format: int64
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductRequestImageDTO'
        isResponded:
          type: boolean
        lastCommentsTree:
          type: array
          items:
            $ref: '#/components/schemas/CommentDTO'
        like:
          $ref: '#/components/schemas/Like'
        parentCategory:
          $ref: '#/components/schemas/CategoryDTO'
        productModels:
          type: array
          items:
            $ref: '#/components/schemas/ProductModelDTORes'
        progress:
          type: number
          format: double
        responseCount:
          type: integer
          format: int32
        sharingLink:
          type: string
        sizeType:
          $ref: '#/components/schemas/SizeTypeLocalized'
        sizes:
          type: array
          items:
            $ref: '#/components/schemas/SizeValueDTO'
        state:
          type: string
          enum:
            - DRAFT
            - PUBLISHED
            - HIDDEN
            - MODERATION
            - REJECTED
        stateTime:
          type: string
          format: date-time
        title:
          type: string
        toPrice:
          type: number
        user:
          $ref: '#/components/schemas/UserDTO'
        needsTranslateDescription:
          type: boolean
      title: ProductRequestDTO
    ProductConditionDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        description:
          type: string
      title: ProductConditionDTO
    SeasonDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        seasonType:
          type: string
          enum:
            - SS
            - AW
        year:
          type: integer
          format: int32
        name:
          type: string
      required:
        - id
        - seasonType
        - year
        - name
    SizeTypeLocalized:
      type: object
      properties:
        abbreviation:
          type: string
        description:
          type: string
        sizeType:
          type: string
          enum:
            - RU
            - EU
            - US
            - INT
            - UK
            - FR
            - IT
            - DE
            - AU
            - JPN
            - INCHES
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - RING_RUSSIAN
            - RING_EUROPEAN
            - JEANS
            - HEIGHT
            - AGE
            - NO_SIZE
            - BUST
      title: SizeTypeLocalized
    Like:
      type: object
      properties:
        count:
          type: integer
          format: int64
        isLiked:
          type: boolean
      title: Like
    ProductRequestImageDTO:
      type: object
      properties:
        extension:
          type: string
        fileName:
          type: string
        imageURL:
          type: string
        isPrimary:
          type: boolean
      title: ProductRequestImageDTO

    SplitInfoPart:
      type: object
      properties:
        date:
          type: string
          format: offset-date-time
        value:
          type: number
          format: bigdecimal

    SplitInfo:
      type: object
      properties:
        firstPayment:
          type: number
        remainingPayment:
          type: number
        parts:
          type: array
          items:
            $ref: '#/components/schemas/SplitInfoPart'
      required:
        - firstPayment
        - remainingPayment
        - parts

    YandexPlusInfo:
      type: object
      summary: Информация о применимости Yandex plus к товару/заказу
      properties:
        points:
          description: Количество баллов кэшбэка за товар/заказ
          type: number
      required:
        - points

    CardOption:
      type: object
      properties:
        id:
          type: integer
          format: int64
        title:
          type: string
        icon:
          type: string

    PaymentOption:
      title: PaymentOption
      x-stoplight:
        id: gjc1akosmxsd0
      type: object
      properties:
        type:
          type: string
        title:
          type: string
        icon:
          type: string
      required:
        - type
        - title

    #остается для поддержки старых клиентов
    YandexSplitPaymentOption:
      allOf:
        - $ref: '#/components/schemas/PaymentOption'
        - properties:
            firstPayment:
              type: number
            remainingPayment:
              type: number
            parts:
              type: array
              items:
                $ref: '#/components/schemas/SplitInfoPart'
          required:
            - firstPayment
            - remainingPayment
            - parts

    SplitPaymentOption:
      allOf:
        - $ref: '#/components/schemas/PaymentOption'
        - properties:
            firstPayment:
              type: number
            remainingPayment:
              type: number
            parts:
              type: array
              items:
                $ref: '#/components/schemas/SplitInfoPart'
            description:
              type: string
          required:
            - firstPayment
            - remainingPayment
            - parts

    CardPaymentOption:
      allOf:
        - $ref: '#/components/schemas/PaymentOption'
        - properties:
            cards:
              type: array
              items:
                $ref: '#/components/schemas/CardOption'

    YandexPayPaymentOption:
      allOf:
        - $ref: '#/components/schemas/PaymentOption'
        - properties:
            yandexPlus:
              $ref: '#/components/schemas/YandexPlusInfo'
          required:
            - yandexPlus

    ProductTagDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        category:
          $ref: '#/components/schemas/ProductTagCategory'
        code:
          type: string
        shortName:
          type: string
        name:
          type: string
        description:
          type: string
        bannerInfo:
          type: object
          properties:
            title:
              type: string
            description:
              type: string
      required:
        - id
        - category
        - shortName
        - name

    ProductTagCategory:
      type: string
      enum:
        - LOCATION
        - BADGES
        - COMMON

    ProductTagLocationType:
      type: string
      enum:
        - BOUTIQUE

    ProductTagType:
      type: string
      enum:
        - WAREHOUSE
        - BOUTIQUE_STOLESHNIKOV
        - BOUTIQUE_KUZNETSKY_BRIDGE
        - BRAND_NEW
        - NEW_SEASON
        - SALE
        - OUTLET
        - CARRY_OVER
        - BOUTIQUE_ONLY
        - OSKELLY_CHOICE
        - INVESTMENT
        - VINTAGE
        - CELEBRITY_WARDROBE

    OrderSourceInfoDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        type:
          type: string
          enum:
            - WEB
            - ANDROID
            - IOS
            - ANDROID_WEB
            - IOS_WEB
            - BOUTIQUE
            - UNKNOWN
        name:
          type: string
        displayName:
          type: string
        isHidden:
          type: boolean
        isMarketplaceLocation:
          type: boolean
      title: OrderSourceInfoDTO

    LegalEntityDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        onecUuid:
          type: string
        title:
          type: string
      title: LegalEntityDTO
    DictionaryDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string

    PromocodeTypeDTO:
      required:
        - id
      type: object
      properties:
        id:
          type: integer
          format: int32
        name:
          type: string
    Api2ResponseOfListOfPromocodeTypeDTO:
      title: Api2ResponseOfListOfPromocodeTypeDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeTypeDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    Api2ResponseOfListOfProductConditionDTO:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ProductConditionDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
      title: Api2ResponseOfListOfProductConditionDTO
    Api2ResponseOfListOfAddressEndpointDTO:
      title: Api2ResponseOfListOfAddressEndpointDTO
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/AddressEndpointDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        humanMessage:
          type: string
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ProductCrossBorderDTO:
      description: Блок инициативы "Кросс-Бордер"
      type: object
      properties:
        status:
          description: Доступность продукта в рамках инициативы
          type: string
          enum:
            - AVAILABLE
            - NOT_AVAILABLE
    OrderTrackDTO:
      title: OrderTrackDTO
      type: object
      properties:
        orderStages:
          type: array
          items:
            $ref: '#/components/schemas/OrderStageDTO'
    OrderStageDTO:
      title: OrderStageDTO
      type: object
      required:
        - type
        - progressState
        - title
      properties:
        type:
          type: string
          enum:
            - SELLER_CONFIRMATION
            - DELIVERY_TO_OFFICE
            - EXPERTISE
            - DELIVERY_TO_BUYER
            - DELIVERY_TO_BOUTIQUE
            - BUYER_CONFIRMATION
            - SOLD_IN_BOUTIQUE
            - PAYOUT
            - RETURNED_TO_SELLER
          description: determines what should be done with an order during the stage (a set of actions which should be applied to an order), a logic
        progressState:
          type: string
          enum:
            - UPCOMING
            - IN_PROGRESS
            - COMPLETE
          description: shows the progress of the stage
        successState:
          type: string
          enum:
            - SUCCEEDED
            - PARTIALLY_SUCCEEDED
            - FAILED
          description: what the stage ended up, can be filled after the stage is complete
        title:
          type: string
          description: the name of the stage a user sees
        description:
          type: string
          description: describes the actions have being acted with the order at the moment (regular font, a text under the title)
        positions:
          type: array
          items:
            $ref: '#/components/schemas/PositionDetailsDTO'
          description: a list of highlighted positions after confirmation/expertises stage, sometime user can do smth with them
        comment:
          type: string
          description: Something to be highlighted to the user or the expected date of completion (Grey font, a text under the title)
        updatedAt:
          type: string
          format: date-time
          description: the date-time of completion an order
        estimatedDateTime:
          type: string
          format: date-time
          description: the expected date-time of completion an order
        action:
          $ref: '#/components/schemas/OrderTrackActionDTO'
          description: Order action associated with the stage
    OrderTrackActionDTO:
      title: OrderTrackActionDTO
      type: object
      required:
        - type
        - title
      properties:
        type:
          type: string
          enum:
            - CHANGE_PICKUP_DATE
            - PAY_OSKELLY_SERVICE
            - GO_TO_NEGOTIATION
            - GO_TO_DETAILS_INFO
            - CHANGE_DELIVERY_DATE
            - CONFIRM_DELIVERY
            - CONFIRM_PAYOUT_DETAILS
          description: Action type
        title:
          type: string
          description: Action display name (e.g. caption on the button for the action)
    PositionDetailsDTO:
      title: PositionDetailsDTO
      type: object
      properties:
        text:
          type: string
        positionId:
          type: integer
          format: int64
        action:
          $ref: '#/components/schemas/OrderTrackActionDTO'
          description: Action on order position

    SellerType:
      title: SellerType
      type: string
      enum:
        - INDIVIDUAL
        - CONSIGNMENT_SHOP
        - BOUTIQUE
        - BRAND
        - BUYER

paths:
  # it's only for a valid yaml format