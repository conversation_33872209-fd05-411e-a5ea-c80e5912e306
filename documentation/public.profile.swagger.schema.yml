openapi: 3.0.3
info:
  title: Oskelly Main Service
  version: '1.0'
servers:
  - url: http://test.oskelly.me:8080/
    description: Inferred Url
tags:
  - name: public-profile-controller-api-v-2
    description: Public Profile Controller Api V 2
paths:
  /api/v2/publicprofile:
    get:
      tags:
        - public-profile-controller-api-v-2
      summary: Возвращает список пользователей по первичным ключам
      operationId: findByUserIdsUsingGET
      parameters:
        - name: userIds
          in: query
          description: Первичные ключи пользователей
          required: true
          style: form
          explode: true
          schema:
            type: array
            items: {}
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfListOfUserDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
components:
  schemas:
    Api2ResponseOfListOfUserDTO:
      title: Api2Response«List«UserDTO»»
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/UserDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    UserDTO:
      title: UserDTO
      type: object
      properties:
        avatarPath:
          type: string
        birthDate:
          type: integer
          format: int64
        brandLikesCount:
          type: integer
          format: int32
        email:
          type: string
        firstChar:
          type: string
        fullName:
          type: string
        id:
          type: integer
          format: int64
        isFollowed:
          type: boolean
        isPro:
          type: boolean
        isTrusted:
          type: boolean
        likesCount:
          type: integer
          format: int32
        name:
          type: string
        nickname:
          type: string
        productLikesCount:
          type: integer
          format: int32
        productsCount:
          type: integer
          format: int32
        registrationTime:
          type: integer
          format: int64
        sex:
          type: string
          enum:
            - ADULT
            - BOY
            - CHILD
            - FEMALE
            - GIRL
            - MALE
        commonTags:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/UserCommonTagDTO'
        sellerType:
          $ref: './partial.yaml#/components/schemas/SellerType'