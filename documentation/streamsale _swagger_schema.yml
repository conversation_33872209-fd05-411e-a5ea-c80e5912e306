openapi: 3.0.3
info:
  title: Oskelly Main Service
  version: 2.2.1
servers:
  - url: http://localhost:8080
    description: Inferred Url
tags:
  - name: admin-stream-controller
    description: Admin Stream Controller
  - name: stream-controller
    description: Stream Controller
paths:
  /api/v2/admin/streamsale/stream:
    get:
      tags:
        - admin-stream-controller
      summary: Получение эфира
      operationId: getStreamUsingGET
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamAdminPageDetailedResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    delete:
      tags:
        - admin-stream-controller
      summary: finishStream
      operationId: finishStreamUsingDELETE
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOflong'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    patch:
      tags:
        - admin-stream-controller
      summary: updateStream
      operationId: updateStreamUsingPATCH
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StreamCreateOrUpdateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamDetailedResponseDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v2/admin/streamsale/stream/list:
    get:
      tags:
        - admin-stream-controller
      summary: getStreams
      operationId: getStreamsUsingGET
      parameters:
        - name: userIds
          in: query
          description: userIds
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
        - name: statuses
          in: query
          description: statuses
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - ANNOUNCE
              - ARCHIVED
              - BANNED
              - LIVE
        - name: sort
          in: query
          description: sort
          required: true
          style: form
          schema:
            type: string
            enum:
              - ASC
              - DESC
        - name: startTime
          in: query
          description: startTime
          required: false
          style: form
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: endTime
          required: false
          style: form
          schema:
            type: string
            format: date-time
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfPageOfStreamItemDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/streamsale/stream:
    get:
      tags:
        - stream-controller
      summary: Получение эфира
      operationId: getStreamUsingGET_1
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamDetailedResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - stream-controller
      summary: createStream
      operationId: createStreamUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StreamCreateOrUpdateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamDetailedResponseDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    delete:
      tags:
        - stream-controller
      summary: deleteStream
      operationId: deleteStreamUsingDELETE
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOflong'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
    patch:
      tags:
        - stream-controller
      summary: updateStream
      operationId: updateStreamUsingPATCH_1
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StreamCreateOrUpdateRequestDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamDetailedResponseDTO'
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v2/streamsale/stream/finish:
    post:
      tags:
        - stream-controller
      summary: finishStream
      operationId: finishStreamUsingPOST
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamDetailedResponseDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/streamsale/stream/list:
    get:
      tags:
        - stream-controller
      summary: getUserStreamsForOtherUser
      operationId: getUserStreamsForOtherUserUsingGET
      parameters:
        - name: userIds
          in: query
          description: userIds
          required: false
          style: form
          explode: true
          schema:
            type: integer
            format: int64
        - name: statuses
          in: query
          description: statuses
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - ANNOUNCE
              - ARCHIVED
              - BANNED
              - LIVE
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfPageOfStreamItemDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/streamsale/stream/list/my:
    get:
      tags:
        - stream-controller
      summary: getUserStreamsForCurrentUser
      operationId: getUserStreamsForCurrentUserUsingGET
      parameters:
        - name: statuses
          in: query
          description: statuses
          required: true
          style: form
          explode: true
          schema:
            type: string
            enum:
              - ANNOUNCE
              - ARCHIVED
              - BANNED
              - LIVE
        - name: pageNumber
          in: query
          description: pageNumber
          required: false
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: false
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfMyStreamListResponseDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/streamsale/stream/start:
    post:
      tags:
        - stream-controller
      summary: startStream
      operationId: startStreamUsingPOST
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
        - name: broadcastId
          in: query
          description: broadcastId
          required: true
          style: form
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfStreamDetailedResponseDTO'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/streamsale/stream/subscribe:
    post:
      tags:
        - stream-controller
      summary: subscribeToStream
      operationId: subscribeToStreamUsingPOST
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfboolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/streamsale/stream/unsubscribe:
    post:
      tags:
        - stream-controller
      summary: unsubscribeFromStream
      operationId: unsubscribeFromStreamUsingPOST
      parameters:
        - name: streamId
          in: query
          description: streamId
          required: true
          style: form
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2_1ResponseOfboolean'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
components:
  schemas:
    Api2_1ResponseOfMyStreamListResponseDTO:
      title: Api2_1ResponseOfMyStreamListResponseDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/MyStreamListResponseDTO'
        error:
          $ref: '#/components/schemas/ErrorOfMyStreamListResponseDTO'
        executionTimeMillis:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    Api2_1ResponseOfPageOfStreamItemDTO:
      title: Api2_1ResponseOfPageOfStreamItemDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageOfStreamItemDTO'
        error:
          $ref: '#/components/schemas/ErrorOfPageOfStreamItemDTO'
        executionTimeMillis:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    Api2_1ResponseOfStreamAdminPageDetailedResponseDTO:
      title: Api2_1ResponseOfStreamAdminPageDetailedResponseDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/StreamAdminPageDetailedResponseDTO'
        error:
          $ref: '#/components/schemas/ErrorOfStreamAdminPageDetailedResponseDTO'
        executionTimeMillis:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    Api2_1ResponseOfStreamDetailedResponseDTO:
      title: Api2_1ResponseOfStreamDetailedResponseDTO
      type: object
      properties:
        data:
          $ref: '#/components/schemas/StreamDetailedResponseDTO'
        error:
          $ref: '#/components/schemas/ErrorOfStreamDetailedResponseDTO'
        executionTimeMillis:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    Api2_1ResponseOfboolean:
      title: Api2_1ResponseOfboolean
      type: object
      properties:
        data:
          type: boolean
        error:
          $ref: '#/components/schemas/ErrorOfboolean'
        executionTimeMillis:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    Api2_1ResponseOflong:
      title: Api2_1ResponseOflong
      type: object
      properties:
        data:
          type: integer
          format: int64
        error:
          $ref: '#/components/schemas/ErrorOflong'
        executionTimeMillis:
          type: integer
          format: int64
        timestamp:
          type: integer
          format: int64
    Available:
      title: Available
      type: object
      properties:
        description:
          type: string
        howItWorksUrl:
          type: string
        title:
          type: string
    ErrorOfMyStreamListResponseDTO:
      title: ErrorOfMyStreamListResponseDTO
      type: object
      properties:
        code:
          type: string
          enum:
            - BAN
            - LOGIC
            - VALIDATION
        data:
          $ref: '#/components/schemas/MyStreamListResponseDTO'
        humanMessage:
          type: string
    ErrorOfPageOfStreamItemDTO:
      title: ErrorOfPageOfStreamItemDTO
      type: object
      properties:
        code:
          type: string
          enum:
            - BAN
            - LOGIC
            - VALIDATION
        data:
          $ref: '#/components/schemas/PageOfStreamItemDTO'
        humanMessage:
          type: string
    ErrorOfStreamAdminPageDetailedResponseDTO:
      title: ErrorOfStreamAdminPageDetailedResponseDTO
      type: object
      properties:
        code:
          type: string
          enum:
            - BAN
            - LOGIC
            - VALIDATION
        data:
          $ref: '#/components/schemas/StreamAdminPageDetailedResponseDTO'
        humanMessage:
          type: string
    ErrorOfStreamDetailedResponseDTO:
      title: ErrorOfStreamDetailedResponseDTO
      type: object
      properties:
        code:
          type: string
          enum:
            - BAN
            - LOGIC
            - VALIDATION
        data:
          $ref: '#/components/schemas/StreamDetailedResponseDTO'
        humanMessage:
          type: string
    ErrorOfboolean:
      title: ErrorOfboolean
      type: object
      properties:
        code:
          type: string
          enum:
            - BAN
            - LOGIC
            - VALIDATION
        data:
          type: boolean
        humanMessage:
          type: string
    ErrorOflong:
      title: ErrorOflong
      type: object
      properties:
        code:
          type: string
          enum:
            - BAN
            - LOGIC
            - VALIDATION
        data:
          type: integer
          format: int64
        humanMessage:
          type: string
    MyStreamListResponseDTO:
      title: MyStreamListResponseDTO
      type: object
      properties:
        availability:
          type: string
          enum:
            - AVAILABLE
            - UNAVAILABLE
        available:
          $ref: '#/components/schemas/Available'
        items:
          $ref: '#/components/schemas/PageOfStreamItemDTO'
        unavailable:
          $ref: '#/components/schemas/Unavailable'
    PageOfStreamItemDTO:
      title: PageOfStreamItemDTO
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/StreamItemDTO'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    Payload:
      title: Payload
      type: object
      properties:
        author:
          type: string
        created:
          type: integer
          format: int64
        customData:
          type: string
        height:
          type: integer
          format: int32
        id:
          type: string
        ingestChannel:
          type: string
        lat:
          type: number
          format: double
        length:
          type: integer
          format: int32
        lon:
          type: number
          format: double
        positionAccuracy:
          type: integer
          format: int32
        positionType:
          type: string
        preview:
          type: string
        resourceUri:
          type: string
        tags:
          type: array
          items:
            type: object
        title:
          type: string
        type:
          type: string
        width:
          type: integer
          format: int32
    StreamAdminPageDetailedResponseDTO:
      title: StreamAdminPageDetailedResponseDTO
      required:
        - author
        - cover
        - description
        - id
        - products
        - startingDate
        - status
        - title
      type: object
      properties:
        author:
          description: Информация об авторе эфира
          $ref: '#/components/schemas/StreamAuthorDTO'
        cover:
          type: string
          description: Тип обложки эфира
          enum:
            - COVER_1
            - COVER_2
            - COVER_3
            - COVER_4
        description:
          type: string
          description: Описание эфира
        id:
          type: integer
          description: Первичный ключ эфира
          format: int64
        products:
          uniqueItems: true
          type: array
          description: Список товаров которые указанны в эфире
          items:
            $ref: '#/components/schemas/StreamAdminPageProductDTO'
        resourceURI:
          type: string
          description: URI для просмотра эфира
        startingDate:
          type: string
          description: Дата начала прямого эфира
          format: date-time
        status:
          type: string
          description: Статус эфира
          enum:
            - ANNOUNCE
            - ARCHIVED
            - BANNED
            - LIVE
        title:
          type: string
          description: Заголовок эфира
    StreamAdminPageProductBrandDTO:
      title: StreamAdminPageProductBrandDTO
      required:
        - id
        - name
      type: object
      properties:
        id:
          type: integer
          description: Первичный ключ бренда
          format: int64
        name:
          type: string
          description: Наименование бренда
    StreamAdminPageProductCategoryDTO:
      title: StreamAdminPageProductCategoryDTO
      required:
        - displayName
        - id
      type: object
      properties:
        displayName:
          type: string
          description: Наименование категории
        id:
          type: integer
          description: Первичный ключ категории
          format: int64
    StreamAdminPageProductDTO:
      title: StreamAdminPageProductDTO
      required:
        - brand
        - category
        - conditionId
        - id
        - likesCount
        - price
        - primaryImageUrl
        - sizes
      type: object
      properties:
        brand:
          description: Данные о бренде товар
          $ref: '#/components/schemas/StreamAdminPageProductBrandDTO'
        category:
          description: Данные о категории товар
          $ref: '#/components/schemas/StreamAdminPageProductCategoryDTO'
        conditionId:
          type: integer
          description: С биркой ли товар
          format: int64
        discount:
          type: integer
          description: Скидка в процентах на товар
          format: int32
        higherPrice:
          type: integer
          description: Старая цена товара
          format: int32
        id:
          type: integer
          description: Первичный ключ товара
          format: int64
        likesCount:
          type: integer
          description: Количество лайков к данному товару
          format: int32
        price:
          type: integer
          description: Цена товара
          format: int32
        primaryImageUrl:
          type: string
          description: Путь к фотографии товара
        sizes:
          type: array
          description: Данные о размерах товар
          items:
            $ref: '#/components/schemas/StreamAdminPageProductSizeDTO'
    StreamAdminPageProductSizeDTO:
      title: StreamAdminPageProductSizeDTO
      required:
        - id
        - productSizeType
        - productSizeValue
        - purchaseStatus
      type: object
      properties:
        id:
          type: integer
          description: Первичный ключ размера товара
          format: int64
        productSizeType:
          type: string
          description: Тип размера товара
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        productSizeValue:
          type: string
          description: Размер товара
        purchaseStatus:
          type: string
          description: Статус товара по данному размеру
          enum:
            - AVAILABLE
            - IN_CART
            - OUT_OF_STOCK
            - PURCHASED
    StreamAuthorDTO:
      title: StreamAuthorDTO
      required:
        - avatarPath
        - id
        - isSubscribed
        - isTrusted
        - nickName
        - type
      type: object
      properties:
        avatarPath:
          type: string
          description: Путь к фотографии пользователя
        id:
          type: integer
          description: Первичный ключ пользователя
          format: int64
        isSubscribed:
          type: boolean
          description: Подписан ли пользователь, на данного пользователя
          example: false
        isTrusted:
          type: boolean
          description: Доверенный ли пользователь
          example: false
        nickName:
          type: string
          description: Ник нейм пользователя
        type:
          type: string
          description: Тип пользователя / Бутик/Частный продавец
    StreamCreateOrUpdateRequestDTO:
      title: StreamCreateOrUpdateRequestDTO
      required:
        - cover
        - description
        - productIds
        - title
      type: object
      properties:
        cover:
          type: string
          description: Тип обложки эфира
          enum:
            - COVER_1
            - COVER_2
            - COVER_3
            - COVER_4
        description:
          type: string
          description: Описание эфира
        productIds:
          type: array
          description: Первичные ключи продуктов указанные в эфире
          items:
            type: integer
            format: int64
        startingDate:
          type: string
          description: Дата начала прямого эфира
          format: date-time
        title:
          type: string
          description: Заголовок эфира
    StreamDetailedResponseDTO:
      title: StreamDetailedResponseDTO
      required:
        - author
        - cover
        - description
        - id
        - products
        - resourceURI
        - startingDate
        - status
        - title
      type: object
      properties:
        author:
          description: Информация об авторе эфира
          $ref: '#/components/schemas/StreamAuthorDTO'
        canDelete:
          type: boolean
          description: Может ли пользователь удалить эфир, для пользователя который создал эфир
          example: false
        canStart:
          type: boolean
          description: Может ли пользователь начать эфир, для пользователя который создал эфир
          example: false
        canUpdate:
          type: boolean
          description: Может ли пользователь обновить эфир, для пользователя который создал эфир
          example: false
        cover:
          type: string
          description: Тип обложки эфира
          enum:
            - COVER_1
            - COVER_2
            - COVER_3
            - COVER_4
        description:
          type: string
          description: Описание эфира
        id:
          type: integer
          description: Первичный ключ эфира
          format: int64
        isSubscribed:
          type: boolean
          description: Подписан ли пользователь на данный эфир
          example: false
        products:
          uniqueItems: true
          type: array
          description: Список товаров которые указанны в эфире
          items:
            $ref: '#/components/schemas/StreamProductDTO'
        resourceURI:
          type: string
          description: URI для просмотра эфира
        startingDate:
          type: string
          description: Дата начала прямого эфира
          format: date-time
        status:
          type: string
          description: Статус эфира
          enum:
            - ANNOUNCE
            - ARCHIVED
            - BANNED
            - LIVE
        title:
          type: string
          description: Заголовок эфира
    StreamItemDTO:
      title: StreamItemDTO
      required:
        - author
        - brands
        - canDelete
        - canUpdate
        - cover
        - description
        - id
        - productsTotalCount
        - startingDate
        - status
        - title
      type: object
      properties:
        author:
          description: Информация об авторе эфира
          $ref: '#/components/schemas/StreamAuthorDTO'
        brands:
          uniqueItems: true
          type: array
          description: Список брендов указанных в эфире
          items:
            $ref: '#/components/schemas/StreamProductBrandDTO'
        canDelete:
          type: boolean
          description: Может ли пользователь удалить эфир
          example: false
        canUpdate:
          type: boolean
          description: Может ли пользователь обновить эфир
          example: false
        cover:
          type: string
          description: Тип обложки эфира
          enum:
            - COVER_1
            - COVER_2
            - COVER_3
            - COVER_4
        description:
          type: string
          description: Описание эфира
        id:
          type: integer
          description: Первичный ключ эфира
          format: int64
        isSubscribed:
          type: boolean
          description: Подписан ли пользователь на данный эфир
          example: false
        productsTotalCount:
          type: integer
          description: Количество продуктов указанных в эфире
          format: int32
        startingDate:
          type: string
          description: Дата начала прямого эфира
          format: date-time
        status:
          type: string
          description: Статус эфира
          enum:
            - ANNOUNCE
            - ARCHIVED
            - BANNED
            - LIVE
        title:
          type: string
          description: Заголовок эфира
    StreamProductBrandDTO:
      title: StreamProductBrandDTO
      required:
        - id
        - title
      type: object
      properties:
        id:
          type: integer
          description: Первичный ключ бренда
          format: int64
        title:
          type: string
          description: Наименование бренда
    StreamProductCategoryDTO:
      title: StreamProductCategoryDTO
      required:
        - id
        - title
      type: object
      properties:
        id:
          type: integer
          description: Первичный ключ категории
          format: int64
        title:
          type: string
          description: Наименование категории
    StreamProductDTO:
      title: StreamProductDTO
      required:
        - brand
        - category
        - commentsCount
        - id
        - imagePath
        - likesCount
        - price
        - priceUpdateSubscribersCount
        - sizeType
        - sizes
        - title
        - withBadge
      type: object
      properties:
        brand:
          description: Данные о бренде товар
          $ref: '#/components/schemas/StreamProductBrandDTO'
        category:
          description: Данные о категории товар
          $ref: '#/components/schemas/StreamProductCategoryDTO'
        commentsCount:
          type: integer
          description: Количество комментариев к данному товару
          format: int32
        discountInPercent:
          type: integer
          description: Скидка в процентах на товар
          format: int32
        id:
          type: integer
          description: Первичный ключ товара
          format: int64
        imagePath:
          type: string
          description: Путь к фотографии товара
        likesCount:
          type: integer
          description: Количество лайков к данному товару
          format: int32
        oldPrice:
          type: integer
          description: Старая цена товара
          format: int32
        price:
          type: integer
          description: Цена товара
          format: int32
        priceUpdateSubscribersCount:
          type: integer
          description: Количество пользователей подписанные на смену цены данного товара
          format: int32
        sellerRecievesSum:
          type: integer
          description: Сумма которую получит продавец
          format: int32
        sizeType:
          type: string
          description: Тип размера товар
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        sizes:
          type: array
          description: Данные о размерах товар
          items:
            $ref: '#/components/schemas/StreamProductSizeDTO'
        title:
          type: string
          description: Наименование товара
        withBadge:
          type: boolean
          description: С биркой ли товар
          example: false
    StreamProductSizeDTO:
      title: StreamProductSizeDTO
      required:
        - id
        - isNoSize
        - purchaseStatus
        - sizeType
        - title
      type: object
      properties:
        id:
          type: integer
          description: Первичный ключ размера товара
          format: int64
        isNoSize:
          type: boolean
          description: Есть ли размер у товара
          example: false
        purchaseStatus:
          type: string
          description: Статус товара по данному размеру
          enum:
            - AVAILABLE
            - IN_CART
            - OUT_OF_STOCK
            - PURCHASED
        sizeType:
          type: string
          description: Тип размера товара
          enum:
            - AGE
            - AU
            - CENTIMETERS
            - COLLAR_CENTIMETERS
            - COLLAR_INCHES
            - DE
            - EU
            - FR
            - HEIGHT
            - INCHES
            - INT
            - IT
            - JEANS
            - JPN
            - NO_SIZE
            - BUST
            - RING_EUROPEAN
            - RING_RUSSIAN
            - RU
            - UK
            - US
        title:
          type: string
          description: Размер товара
    Unavailable:
      title: Unavailable
      type: object
      properties:
        description:
          type: string
        publishedProductsLeft:
          type: integer
          format: int32
        successfulTradesLeft:
          type: integer
          format: int32
        title:
          type: string
    WebHookDTO:
      title: WebHookDTO
      type: object
      properties:
        action:
          type: string
        eventId:
          type: string
        payload:
          $ref: '#/components/schemas/Payload'
