openapi: 3.0.3
info:
  version: "1.0"
  title: Oskelly Main Service
servers:
  - url: http://localhost:8880
    description: Inferred Url
tags:
  - name: user-ban-controller
    description: User Ban Controller
paths:
  /adminpanel/bans:
    post:
      tags:
        - user-ban-controller
      summary: save
      operationId: saveUsingPOST
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserBanActionDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
#  /adminpanel/bans/all:
#    get:
#      tags:
#        - user-ban-controller
#      summary: getAllUsersBans
#      operationId: getAllUsersBansUsingGET
#      parameters:
#        - name: pageNumber
#          in: query
#          description: pageNumber
#          required: true
#          style: form
#          schema:
#            type: integer
#            format: int32
#        - name: pageSize
#          in: query
#          description: pageSize
#          required: true
#          style: form
#          schema:
#            type: integer
#            format: int32
#      responses:
#        '200':
#          description: OK
#          content:
#            application/json:
#              schema:
#                type: object
#        '401':
#          description: Unauthorized
#        '403':
#          description: Forbidden
#        '404':
#          description: Not Found
#    post:
#      tags:
#        - user-ban-controller
#      summary: getAllByFilter
#      operationId: getAllByFilterUsingPOST
#      parameters:
#        - name: pageNumber
#          in: query
#          description: pageNumber
#          required: true
#          style: form
#          schema:
#            type: integer
#            format: int32
#        - name: pageSize
#          in: query
#          description: pageSize
#          required: true
#          style: form
#          schema:
#            type: integer
#            format: int32
#      requestBody:
#        content:
#          application/json:
#            schema:
#              $ref: '#/components/schemas/AdminPanelBanSearchFilerDTO'
#      responses:
#        '200':
#          description: OK
#          content:
#            application/json:
#              schema:
#                type: object
#        '201':
#          description: Created
#        '401':
#          description: Unauthorized
#        '403':
#          description: Forbidden
#        '404':
#          description: Not Found
  /adminpanel/bans/all/{banType}:
    get:
      tags:
        - user-ban-controller
      summary: getAllBansByType
      operationId: getAllBansByTypeUsingGET
      parameters:
        - name: banType
          in: path
          description: banType
          required: true
          style: simple
          schema:
            $ref: '#/components/schemas/BanType'
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /adminpanel/bans/all/{userId}/{banType}:
    get:
      tags:
        - user-ban-controller
      summary: getUserConcreteAllBans
      operationId: getUserConcreteAllBansUsingGET
      parameters:
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: banType
          in: path
          description: banType
          required: true
          style: simple
          schema:
            $ref: '#/components/schemas/BanType'
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /adminpanel/bans/counts:
    get:
      tags:
        - user-ban-controller
      summary: getBanCounts
      operationId: getBanCountsUsingGET
      parameters:
        - name: isActive
          in: query
          description: isActive
          required: true
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: integer
                  format: int64
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /adminpanel/bans/users:
    post:
      tags:
        - user-ban-controller
      summary: getAllUsersIdByFilterIfExistsBan
      operationId: getAllUsersIdByFilterIfExistsBanUsingPOST
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminPanelBanSearchFilerDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageOfDefaultUserItem'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /adminpanel/bans/{banId}:
    get:
      tags:
        - user-ban-controller
      summary: getUserBanById
      operationId: getUserBanByIdUsingGET
      parameters:
        - name: banId
          in: path
          description: banId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - user-ban-controller
      summary: delete
      operationId: deleteUsingPOST
      parameters:
        - name: banId
          in: path
          description: banId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    patch:
      tags:
        - user-ban-controller
      summary: cancel
      operationId: cancelUsingPATCH
      parameters:
        - name: banId
          in: path
          description: banId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /adminpanel/bans/{userId}/all:
    get:
      tags:
        - user-ban-controller
      summary: getAllUserBansByUserId
      operationId: getAllUserBansByUserIdUsingGET
      parameters:
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

  /adminpanel/bans/data:
    get:
      tags:
        - user-ban-controller
      summary: getBanTypes
      operationId: getBanTypesUsingGET
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

  /api/v2/bans:
    post:
      tags:
        - user-ban-controller
      summary: save2
      operationId: saveUsingPOST2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserBanActionDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/bans/all:
    post:
      tags:
        - user-ban-controller
      summary: getAllBans
      operationId: getAllBansUsingPOST
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminPanelBanSearchFilerDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/bans/all/{banType}:
    get:
      tags:
        - user-ban-controller
      summary: getAllBansByType2
      operationId: getAllBansByTypeUsingGET2
      parameters:
        - name: banType
          in: path
          description: banType
          required: true
          style: simple
          schema:
            $ref: '#/components/schemas/BanType'
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/bans/all/{userId}/{banType}:
    get:
      tags:
        - user-ban-controller
      summary: getUserConcreteAllBans2
      operationId: getUserConcreteAllBansUsingGET2
      parameters:
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: banType
          in: path
          description: banType
          required: true
          style: simple
          schema:
            $ref: '#/components/schemas/BanType'
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/bans/counts:
    get:
      tags:
        - user-ban-controller
      summary: getBanCounts2
      operationId: getBanCountsUsingGET2
      parameters:
        - name: isActive
          in: query
          description: isActive
          required: true
          style: form
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: integer
                  format: int64
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/bans/users:
    post:
      tags:
        - user-ban-controller
      summary: getAllUsersIdByFilterIfExistsBan2
      operationId: getAllUsersIdByFilterIfExistsBanUsingPOST2
      parameters:
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminPanelBanSearchFilerDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageOfDefaultUserItem'
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
  /api/v2/bans/{banId}:
    get:
      tags:
        - user-ban-controller
      summary: getUserBanById2
      operationId: getUserBanByIdUsingGET2
      parameters:
        - name: banId
          in: path
          description: banId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    post:
      tags:
        - user-ban-controller
      summary: delete
      operationId: deleteUsingPOST
      parameters:
        - name: banId
          in: path
          description: banId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
        '201':
          description: Created
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
    patch:
      tags:
        - user-ban-controller
      summary: cancel
      operationId: cancelUsingPATCH
      parameters:
        - name: banId
          in: path
          description: banId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
        '204':
          description: No Content
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  /api/v2/bans/{userId}/all:
    get:
      tags:
        - user-ban-controller
      summary: getAllUserBansByUserId2
      operationId: getAllUserBansByUserIdUsingGET2
      parameters:
        - name: userId
          in: path
          description: userId
          required: true
          style: simple
          schema:
            type: integer
            format: int64
        - name: pageNumber
          in: query
          description: pageNumber
          required: true
          style: form
          schema:
            type: integer
            format: int32
        - name: pageSize
          in: query
          description: pageSize
          required: true
          style: form
          schema:
            type: integer
            format: int32
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminPanelUserBanDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

  /api/v2/bans/data:
    get:
      tags:
        - user-ban-controller
      summary: getBanTypes2
      operationId: getBanTypesUsingGET2
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

components:
  schemas:

    AdminPanelUserBanDTO:
      properties:
        id:
          type: integer
          format: int64
        userId:
          type: integer
          format: int64
        banType:
          $ref: '#/components/schemas/BanType'
        description:
          type: string
        title:
          type: string
        createDate:
          type: integer
          format: int64
        startDate:
          type: integer
          format: int64
        endDate:
          type: integer
          format: int64
        isBaned:
          type: boolean
        isDeleted:
          type: boolean
        statusChangedUserId:
          type: integer
          format: int64
        adminComment:
          type: string
          
    BanType:
      type: string
      enum:
        - USER_BAN
        - COMMENT_BAN
        - PUBLISH_BAN
        - STORIES_BAN
        - BARGAIN_BAN
        - STREAM_BAN
        - WARNING
        - OSOCIAL_POST_BAN
        - OSOCIAL_COMMENT_BAN
        - COMMENT_SHADOW_BAN
        
    AdminPanelBanSearchFilerDTO:
      title: AdminPanelBanSearchFilerDTO
      type: object
      properties:
        banIds:
          type: array
          items:
            type: integer
            format: int64
        banTypes:
          type: array
          items:
            $ref: '#/components/schemas/BanType'
        isBaned:
          type: boolean
        isDeleted:
          type: boolean
        userIds:
          type: array
          items:
            type: integer
            format: int64
    DefaultUserItem:
      title: DefaultUserItem
      type: object
      properties:
        city:
          type: string
        completeProfile:
          type: boolean
        email:
          type: string
        fullName:
          type: string
        id:
          type: integer
          format: int64
        isBaned:
          type: boolean
        nickName:
          type: string
        ordersCount:
          type: integer
          format: int32
        phoneNumber:
          type: string
        productsCount:
          type: integer
          format: int32
        role:
          type: string
        salesCount:
          type: integer
          format: int32
        trusted:
          type: boolean
        type:
          type: string
    Pageable:
      title: Pageable
      type: object
      properties:
        offset:
          type: integer
          format: int64
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        paged:
          type: boolean
        sort:
          $ref: '#/components/schemas/Sort'
        unpaged:
          type: boolean
    PageOfDefaultUserItem:
      title: PageOfDefaultUserIte
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/DefaultUserItem'
        empty:
          type: boolean
        first:
          type: boolean
        last:
          type: boolean
        number:
          type: integer
          format: int32
        numberOfElements:
          type: integer
          format: int32
        pageable:
          $ref: '#/components/schemas/Pageable'
        size:
          type: integer
          format: int32
        sort:
          $ref: '#/components/schemas/Sort'
        totalElements:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    Sort:
      title: Sort
      type: object
      properties:
        empty:
          type: boolean
        sorted:
          type: boolean
        unsorted:
          type: boolean
    UserBanActionDTO:
      title: UserBanActionDTO
      type: object
      properties:
        banType:
          $ref: '#/components/schemas/BanType'
        description:
          type: string
        endDate:
          type: string
          format: date-time
        userId:
          type: integer
          format: int64
        adminComment:
          type: string