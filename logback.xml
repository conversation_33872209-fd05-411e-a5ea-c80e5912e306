<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="json_console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <includeCallerData>true</includeCallerData>
            <customFields>{"appname":"${APP_NAME}"}</customFields>
        </encoder>
    </appender>

    <logger name="org.apache.http" level="ERROR"/>
    <logger name="su.reddot.domain.interceptor" level="DEBUG"/>
    <logger name="su.reddot.infrastructure.cashregister" level="DEBUG"/>
    <logger name="su.reddot.infrastructure.bank" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="json_console"/>
    </root>

</configuration>
