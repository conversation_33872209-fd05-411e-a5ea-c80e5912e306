{"openapi": "3.0.1", "info": {"title": "Oskelly Main Service", "description": "API Documentation", "version": "2.2.1"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "security": [{"cookieAuth": []}], "tags": [{"name": "mercaux-offer-controller", "description": "API для взаимодействия с предложениями"}, {"name": "offer-controller", "description": "API для взаимодействия с предложениями"}, {"name": "sales-mercaux-manager-controller", "description": "API для взаимодействия с предложениями"}, {"name": "Клиентский API консьерж-сервиса (Bitrix + Self)", "description": "Управление заявками консьерж-сервиса"}, {"name": "shipment-controller", "description": "API для работы с товарами в заявках"}, {"name": "manual-purchase-order-assignment-controller", "description": "API ручного назначения сотрудников на заявки"}, {"name": "concierge-personal-shopper-mercaux-controller", "description": "API для управления PersonalShopper"}, {"name": "Bitrix Webhook API", "description": "Эндпоинты для обработки вебхуков от Bitrix24"}, {"name": "Управление комментариями заявок (V2)", "description": "Операции с комментариями для заявок консьерж-сервиса"}, {"name": "concierge-personal-shopper-controller", "description": "API для управления PersonalShopper"}, {"name": "Администрирование консьерж-сервиса через приложение Mercaux (V2)", "description": "Управление заявками на покупку в консьерж-сервисе через приложение Mercaux"}, {"name": "sales-manager-controller", "description": "API для взаимодействия с предложениями"}, {"name": "Справочник причин отклонений для заявок", "description": "API для работы с причинами отклонений"}, {"name": "Администрирование консьерж-сервиса (V2)", "description": "Управление заявками на покупку в консьерж-сервисе"}], "paths": {"/api/v2/admin/mercaux/concierge/shipments/comment": {"put": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Добавление комментария к товару", "description": "Добавля<PERSON>т комментарий к товару в заявке", "operationId": "addCommentToShipmentMercaux", "requestBody": {"description": "Комментарий к товару", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentCommentDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentResponseDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Товар не найден"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/shipments/comment": {"put": {"tags": ["shipment-controller"], "summary": "Добавление комментария к товару", "description": "Добавля<PERSON>т комментарий к товару в заявке", "operationId": "addCommentToShipment", "requestBody": {"description": "Комментарий к товару", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentCommentDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentResponseDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Товар не найден"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/submit-seller-application": {"post": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Bitrix: Отправка заявки продавца", "description": "Отправка заявки продавца", "operationId": "submitSellerApplication", "requestBody": {"description": "Данные заявки", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConciergeApplicationFormDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/submit-application": {"post": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Bitrix: Отправка заявки", "description": "Отправка заявки консьерж-сервиса", "operationId": "submitApplication", "requestBody": {"description": "Данные заявки", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConciergeApplicationFormDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/rejection": {"post": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Создание события отклонения", "description": "Добавляет событие отклонения к заявке.", "operationId": "createRejectionEvent", "parameters": [{"name": "orderId", "in": "query", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 789}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectionEventRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseConciergeOrderDetailsResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректный запрос"}, "404": {"description": "Заявка или причина отклонения не найдена"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/purchase-order": {"post": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Self: Создание заявки на покупку", "description": "Создание заявки на покупку в внутреннем консьерж-сервисе", "operationId": "createPurchaseOrder", "requestBody": {"description": "Данные заявки", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderMobileCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/purchase-order/{orderId}/transition": {"post": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Self: С<PERSON><PERSON>на статуса заявки", "description": "Перевод заявки в новый статус по событию", "operationId": "purchaseOrderEventSend", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 789}, {"name": "eventCode", "in": "query", "description": "Код события", "required": true, "schema": {"type": "string"}, "example": "APPROVE"}], "responses": {"200": {"description": "Статус изменен", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PurchaseOrderFullResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректное событие"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "409": {"description": "Конфлик<PERSON> ста<PERSON><PERSON><PERSON><PERSON>", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}}}}, "/api/v2/concierge/image/upload": {"post": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Bitrix: Загрузка изображений", "description": "Загрузка изображений для заявок консьерж-сервиса", "operationId": "uploadItemImage", "parameters": [{"name": "image", "in": "query", "description": "Изображения для загрузки", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "binary"}}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["image"], "type": "object", "properties": {"image": {"type": "string", "format": "binary"}}}, "encoding": {"data": {"contentType": "text/plain"}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseListString"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/offer/shopperOffer": {"post": {"tags": ["offer-controller"], "summary": "Добавить предложение шопера", "description": "Добавить предложение шопера", "operationId": "addShopperOffer", "requestBody": {"description": "Данные по предложению шоперов", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOffersDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentOffersDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/offer/send": {"post": {"tags": ["offer-controller"], "summary": "Отправка предложений клиенту в WhatsApp", "description": "Отправить несколько предложений клиенту в WhatsApp", "operationId": "sendOffers", "requestBody": {"description": "Данные по предложениям для отправки сообщения", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendOffersToClientRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/offer/addingShoppers": {"post": {"tags": ["offer-controller"], "summary": "Добавление шоперов в предложение", "description": "Добавление шоперов в предложение", "operationId": "addingShoppers", "requestBody": {"description": "Данные шоперов", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOffersDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentOffersDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/offer/addProducts": {"post": {"tags": ["offer-controller"], "summary": "Добавление товара с платформы в предложение", "description": "Добавление товара с платформы в предложение", "operationId": "addingProducts", "requestBody": {"description": "Список товаров с платформы", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOffersDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentOffersDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/offer/shopperOffer": {"post": {"tags": ["mercaux-offer-controller"], "summary": "Добавить предложение шопера", "description": "Добавить предложение шопера", "operationId": "addShopperOffer_1", "requestBody": {"description": "Данные по предложению шоперов", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOffersDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentOffersDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/offer/send": {"post": {"tags": ["mercaux-offer-controller"], "summary": "Отправка предложений клиенту в WhatsApp", "description": "Отправить несколько предложений клиенту в WhatsApp", "operationId": "sendOffers_1", "requestBody": {"description": "Данные по предложениям для отправки сообщения", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendOffersToClientRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/offer/addingShoppers": {"post": {"tags": ["mercaux-offer-controller"], "summary": "Добавление шоперов в предложение", "description": "Добавление шоперов в предложение", "operationId": "addingShoppersMercaux", "requestBody": {"description": "Данные шоперов", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOffersDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentOffersDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/offer/addProducts": {"post": {"tags": ["mercaux-offer-controller"], "summary": "Добавление товара с платформы в предложение", "description": "Добавление товара с платформы в предложение", "operationId": "addingProductsMercaux", "requestBody": {"description": "Список товаров с платформы", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentOffersDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentOffersDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/shipments/purchase-order/{orderId}": {"post": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Добавление нескольких товаров в заявку", "description": "Добавляет несколько новых товаров в указанную заявку", "operationId": "addShipmentsToOrderMercaux", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "Запрос с данными для создания товаров в заявке", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentRequestDTO"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseListShipmentResponseDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Заявка не найдена"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/purchase-order": {"post": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Создание заявки на покупку", "description": "Требуемые роли: CONCIERGE_SALES_ADMIN, SALES, STOLESHNIKOV_ADMIN, STOLESHNIKOV_BOUTIQUE_SALESMAN, KUZNETSKY_BRIDGE_ADMIN, KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN", "operationId": "submitPurchaseOrderMercaux", "requestBody": {"description": "Данные для создания заявки", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderWithShipmentsCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Доступ запрещен"}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/purchase-orders": {"post": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Получение списка заявок по фильтру для приложения Mercaux", "description": "Возвращает список заявок, отфильтрованных по заданным параметрам.  Для администраторов возвращаются все заявки, для обычных пользователей - только их собственные. Поддерживает пагинацию, сортировку и полнотекстовый поиск.", "operationId": "getPurchaseOrders", "parameters": [{"name": "searchText", "in": "query", "description": "Текст для полнотекстового поиска по заявкам", "required": false, "schema": {"type": "string"}, "example": "срочный заказ"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestsFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePageOrdersForConciergeDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Неверные параметры запроса (невалидный фильтр)"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Внутренняя ошибка сервера"}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/purchase-order/{orderId}/transition": {"post": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Смена статуса заявки", "description": "Перевод заявки в новый статус по событию", "operationId": "eventSend", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 789}, {"name": "eventCode", "in": "query", "description": "Код события", "required": true, "schema": {"type": "string"}, "example": "APPROVE"}], "responses": {"200": {"description": "Статус изменен", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/MercauxPurchaseOrderFullResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректное событие"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "409": {"description": "Конфлик<PERSON> ста<PERSON><PERSON><PERSON><PERSON>", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}}}}, "/api/v2/admin/mercaux/concierge/personal-shopper/shoppers": {"post": {"tags": ["concierge-personal-shopper-mercaux-controller"], "summary": "Получение списка PersonalShopper по фильтру", "description": "Возвращает список PersonalShopper, отфильтрованных по заданным параметрам.", "operationId": "getPersonalShoppers", "parameters": [{"name": "searchText", "in": "query", "description": "Текст для полнотекстового поиска по заявкам", "required": false, "schema": {"type": "string"}, "example": "срочный заказ"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalShopperFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePaginatedShoppersResult"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Неверные параметры запроса (невалидный фильтр)"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Внутренняя ошибка сервера"}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/personal-shopper/create": {"post": {"tags": ["concierge-personal-shopper-mercaux-controller"], "summary": "Создание нового PersonalShopper", "description": "Добавляет нового PersonalShopper в систему на основе предоставленных данных", "operationId": "createPersonalShopper", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalShopperCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserBanDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "201": {"description": "PersonalShopper успешно создан", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalShopper"}}}}}}}, "/api/v2/admin/mercaux/concierge/filter": {"post": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Получение данных для фильтрации заявок для приложения Mercaux", "description": "Формирование фильтра для получения заявок", "operationId": "getFilterPurchaseOrders", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFilter"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/{orderId}/{comment}/returnToWork": {"post": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Возврат заявки в работу", "description": "Возврат заявки в работу", "operationId": "returnToWork", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "comment", "in": "path", "description": "Описание причины возврата", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/{orderId}/rejection": {"post": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Создание события отклонения", "description": "Добавляет событие отклонения к заявке.", "operationId": "createRejectionEvent_1", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 789}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectionEventRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректный запрос"}, "404": {"description": "Заявка или причина отклонения не найдена"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/shipments/purchase-order/{orderId}": {"post": {"tags": ["shipment-controller"], "summary": "Добавление товара в заявку", "description": "Добавляет новый пустой товар в указанную заявку", "operationId": "addShipmentToOrder", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentResponseDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Заявка не найдена"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order": {"post": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Создание заявки на покупку", "description": "Требуемые роли: CONCIERGE_SALES_ADMIN, SALES", "operationId": "submitPurchaseOrder", "requestBody": {"description": "Данные для создания заявки", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Доступ запрещен"}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-orders": {"post": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Получение списка заявок по фильтру", "description": "Возвращает список заявок, отфильтрованных по заданным параметрам.  Для администраторов возвращаются все заявки, для обычных пользователей - только их собственные. Поддерживает пагинацию, сортировку и полнотекстовый поиск.", "operationId": "getPurchaseOrders_1", "parameters": [{"name": "searchText", "in": "query", "description": "Текст для полнотекстового поиска по заявкам", "required": false, "schema": {"type": "string"}, "example": "срочный заказ"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestsFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePaginatedResult"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Неверные параметры запроса (невалидный фильтр)"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Внутренняя ошибка сервера"}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/transition": {"post": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Смена статуса заявки", "description": "Перевод заявки в новый статус по событию", "operationId": "eventSend_1", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 789}, {"name": "eventCode", "in": "query", "description": "Код события", "required": true, "schema": {"type": "string"}, "example": "APPROVE"}], "responses": {"200": {"description": "Статус изменен", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PurchaseOrderFullResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректное событие"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "409": {"description": "Конфлик<PERSON> ста<PERSON><PERSON><PERSON><PERSON>", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/comments": {"get": {"tags": ["Управление комментариями заявок (V2)"], "summary": "Получить все комментарии заявки", "description": "Возвращает список всех комментариев для указанной заявки", "operationId": "getComments", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseCommentsAdminListDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Заявка не найдена"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}, "post": {"tags": ["Управление комментариями заявок (V2)"], "summary": "Создать комментарий", "description": "Добавление нового комментария к заявке", "operationId": "createComment", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}, {"name": "message", "in": "query", "description": "Текст комментария (макс. 2000 символов)", "required": true, "schema": {"maxLength": 2000, "minLength": 0, "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseCommentsAdminFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректные данные комментария"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/assign/sourcer": {"post": {"tags": ["manual-purchase-order-assignment-controller"], "summary": "Назначить Сорсера на заявку", "description": "Ручная инициализация процесса назначения Сорсера на заявку", "operationId": "assignOrderToSourcer", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/assign/sales": {"post": {"tags": ["manual-purchase-order-assignment-controller"], "summary": "Назначить Сейлза на заявку", "description": "Ручная инициализация процесса назначения Сейлза на заявку", "operationId": "assignOrderToSales", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/assign/admin/sourcer/{userId}": {"post": {"tags": ["manual-purchase-order-assignment-controller"], "summary": "Назначить Админа на заявку Сорсера", "description": "Назначение Админа Сорсеров на заявку Сорсера", "operationId": "assignOrderToAdminSourcer", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "path", "description": "ID Админа", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/assign/admin/sales/{userId}": {"post": {"tags": ["manual-purchase-order-assignment-controller"], "summary": "Назначить Админа на заявку Сейлза", "description": "Назначение Админа (сейлза или бутиков) на заявку Сейлза", "operationId": "assignOrderToAdminSales", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "userId", "in": "path", "description": "ID Админа", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/personal-shopper/shoppers": {"post": {"tags": ["concierge-personal-shopper-controller"], "summary": "Получение списка PersonalShopper по фильтру", "description": "Возвращает список PersonalShopper, отфильтрованных по заданным параметрам.", "operationId": "getPersonalShoppers_1", "parameters": [{"name": "searchText", "in": "query", "description": "Текст для полнотекстового поиска по заявкам", "required": false, "schema": {"type": "string"}, "example": "срочный заказ"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PersonalShopperFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePaginatedShoppersResult"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Неверные параметры запроса (невалидный фильтр)"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Внутренняя ошибка сервера"}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/filter": {"post": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Получение данных для фильтрации заявок", "description": "Формирование фильтра для получения заявок", "operationId": "getFilterPurchaseOrders_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderFilter"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFilter"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/bitrix/deal/updated": {"post": {"tags": ["Bitrix Webhook API"], "summary": "Обработка обновления сделки из Bitrix", "description": "Принимает вебхук обновления сделки из Bitrix и синхронизирует заявку в консьерж-сервисе", "operationId": "handleBitrixDealUpdate", "requestBody": {"description": "Событие обновления сделки из Bitrix", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealUpdateWebhookDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/purchase-order/{orderId}": {"get": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Self: Получение заявки на покупку", "description": "Получение заявки на покупку в внутреннем консьерж-сервисе", "operationId": "getPurchaseOrderApp", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 123}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseConciergeOrderDetailsResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}, "patch": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Self: Обновление заявки на покупку", "description": "Обновление заявки на покупку в внутреннем консьерж-сервисе", "operationId": "updatePurchaseOrder", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 123}], "requestBody": {"description": "Данные заявки", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseOrderMobileUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/shipments/{shipmentId}": {"delete": {"tags": ["shipment-controller"], "summary": "Удаление товара из заявки", "description": "Удаляет товар из заявки. Доступно только для заявок в статусе 'В работе у Sales'", "operationId": "deleteShipment", "parameters": [{"name": "shipmentId", "in": "path", "description": "ID товара", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserBanDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Невозможно удалить товар (неверный статус заявки)"}, "404": {"description": "Товар или заявка не найдены"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "204": {"description": "Товар успешно удален", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}}}, "patch": {"tags": ["shipment-controller"], "summary": "Обновление информации о товаре", "description": "Частично обновляет информацию о товаре (PATCH запрос)", "operationId": "updateShipment", "parameters": [{"name": "shipmentId", "in": "path", "description": "ID товара", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "Данные для обновления товара", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipmentRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseShipmentResponseDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Товар не найден"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/comments/{id}": {"get": {"tags": ["Управление комментариями заявок (V2)"], "summary": "Получить комментарий по ID", "description": "Возвращает детальную информацию о конкретном комментарии", "operationId": "getComment", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}, {"name": "id", "in": "path", "description": "ID комментария", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 54321}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseCommentsAdminFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Комментарий не существует"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}, "delete": {"tags": ["Управление комментариями заявок (V2)"], "summary": "Удалить комментарий", "description": "Полное удаление комментария из системы", "operationId": "deleteComment", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}, {"name": "id", "in": "path", "description": "ID комментария", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 54321}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Комментарий не существует"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}, "patch": {"tags": ["Управление комментариями заявок (V2)"], "summary": "Обновить комментарий", "description": "Редактирование текста существующего комментария", "operationId": "updateComment", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}, {"name": "id", "in": "path", "description": "ID комментария", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 54321}, {"name": "message", "in": "query", "description": "Новый текст комментария (макс. 2000 символов)", "required": true, "schema": {"maxLength": 2000, "minLength": 0, "type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseCommentsAdminFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректные данные"}, "404": {"description": "Комментарий не существует"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/comments/{id}/pin": {"patch": {"tags": ["Управление комментариями заявок (V2)"], "summary": "Закрепить комментарий", "description": "Помечает комментарий как важный (закрепленный)", "operationId": "pinComment", "parameters": [{"name": "orderId", "in": "path", "description": "ID заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}, {"name": "id", "in": "path", "description": "ID комментария", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 54321}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseCommentsAdminFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Комментарий не существует"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/mercaux/sales-manager/sales-orders": {"get": {"tags": ["sales-mercaux-manager-controller"], "summary": "Получить список товаров на покупку", "description": "Получить список товаров на покупку", "operationId": "getMercauxSalesOrders", "parameters": [{"name": "shipmentId", "in": "query", "description": "ID товара для заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseListSalesManagerProductDto"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректные параметры запроса"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/order-information-callback": {"get": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Bitrix: Получение информации о заявке", "description": "Получение информации о заявке", "operationId": "getOrderInformationCallback", "parameters": [{"name": "orderID", "in": "query", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/OrderInformationDto"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/getPriceWithoutCommissionInBaseCurrency": {"get": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Получение цены без комиссии в базовой валюте", "description": "Конвертация цены в базовую валюту без учета комиссии", "operationId": "getPriceWithoutCommissionInBaseCurrency", "parameters": [{"name": "currencyCode", "in": "query", "description": "ISO код валюты", "required": true, "schema": {"type": "string"}, "example": 1}, {"name": "priceInCurrency", "in": "query", "description": "Цена в указанной валюте", "required": true, "schema": {"type": "number"}, "example": 100.5}, {"name": "shopperId", "in": "query", "description": "Идентификатор шопера", "required": false, "schema": {"type": "integer", "format": "int64"}, "example": 1}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseBigDecimal"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/get-all-rates": {"get": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Получение всех курсов валют", "description": "Получение списка всех курсов валют", "operationId": "getAllRates", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseListCurrencyRateWithEffectiveRateDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/get-all-currencies": {"get": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Получение всех валют", "description": "Получение списка всех доступных валют", "operationId": "getAllCurrencies", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseObject"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/concierge/conversion": {"get": {"tags": ["Клиентский API консьерж-сервиса (Bitrix + Self)"], "summary": "Получение преобразования цены товара", "description": "Возвращает преобразование цены товара на сайте в суму, которую получит продавец", "operationId": "getConversion", "parameters": [{"name": "priceWithoutCommission", "in": "query", "description": "Цена без комиссии", "required": false, "schema": {"type": "number"}, "example": 100}, {"name": "priceWithCommission", "in": "query", "description": "Цена с комиссией", "required": false, "schema": {"type": "number"}, "example": 110}, {"name": "salesChannel", "in": "query", "description": "Канал продаж", "required": false, "schema": {"type": "string", "enum": ["WEBSITE", "BOUTIQUE_AND_WEBSITE", "STOCK_AND_BOUTIQUE_AND_WEBSITE", "BOUTIQUE"]}, "example": "WEBSITE"}, {"name": "sellerId", "in": "query", "description": "Идентификатор продавца", "required": false, "schema": {"type": "integer", "format": "int64"}, "example": 1}, {"name": "currencyCode", "in": "query", "description": "Код валюты", "required": false, "schema": {"type": "string"}, "example": "USD"}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseConversion"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/sales-manager/sales-orders": {"get": {"tags": ["sales-manager-controller"], "summary": "Получить список товаров на покупку", "description": "Получить список товаров на покупку", "operationId": "getSalesOrders", "parameters": [{"name": "shipmentId", "in": "query", "description": "ID товара для заявки", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseListSalesManagerProductDto"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректные параметры запроса"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/offer/proposedOffers": {"get": {"tags": ["offer-controller"], "summary": "Получение ProposedOffer по идентифика<PERSON>о<PERSON><PERSON> Offer", "description": "Возвращает список ProposedOffer со вложенными сущностями по идентификатор<PERSON> Offer.", "operationId": "getProposedOffersByOffer", "parameters": [{"name": "offerId", "in": "query", "description": "ID оффера для поиска предложений", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseListProposedOfferDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Неверные параметры запроса"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/offer/proposedOffers": {"get": {"tags": ["mercaux-offer-controller"], "summary": "Получение ProposedOffer по идентифика<PERSON>о<PERSON><PERSON> Offer", "description": "Возвращает список ProposedOffer со вложенными сущностями по идентификатор<PERSON> Offer.", "operationId": "getProposedOffersByOffer_1", "parameters": [{"name": "offerId", "in": "query", "description": "ID оффера для поиска предложений", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 12345}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseListProposedOfferDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Неверные параметры запроса"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/purchase-order/{orderId}": {"get": {"tags": ["Администрирование консьерж-сервиса через приложение Mercaux (V2)"], "summary": "Получение заявки по ID", "description": "Доступные роли: CONCIERGE_SALES_ADMIN, CONCIERGE_SOURCERS_ADMIN, SALES, SOURCER", "operationId": "getPurchaseOrder", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 123}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseMercauxPurchaseOrderFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Заявка не существует"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/concierge/personal-shopper/filter": {"get": {"tags": ["concierge-personal-shopper-mercaux-controller"], "summary": "Фильтр для списка PersonalShopper", "description": "Возвращает фильтр для PersonalShopper", "operationId": "filterPersonalShopper", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePersonalShopperFilter"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/sources": {"get": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Получение списка источников заявок", "description": "Получение списка источников заявок", "operationId": "getSources", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseOrderSources"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/rejection-reasons": {"get": {"tags": ["Справочник причин отклонений для заявок"], "summary": "Получение списка причин отклонения", "description": "Возвращает список причин отклонения в зависимости от типа объекта и статуса заявки (если применимо).", "operationId": "getReasons", "parameters": [{"name": "objectType", "in": "query", "description": "Тип объекта (заявка или товар)", "required": true, "schema": {"type": "string", "enum": ["PURCHASE_ORDER", "PRODUCT"]}}, {"name": "status", "in": "query", "description": "Статус заявки (только для типа объекта ORDER)", "required": false, "schema": {"type": "string", "enum": ["CREATED", "DRAFT", "NEW", "IN_PROGRESS_SALES", "AWAITING_SOURCER", "IN_PROGRESS_SOURCER", "AWAITING_SEND_TO_CLIENT", "AWAITING_CLIENT_ANSWER", "REPEAT_REQUEST_TO_SALES", "REPEAT_AWAITING_SOURCER", "PAYED_ORDER_IN_PROGRESS", "PAYED_REPEAT_REQUEST", "PAYED_RR_SOURCER", "REJECTED", "DONE", "CANCELLED", "ALL"]}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseListRejectionReason"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Некорректный запрос: статус указан для неверного типа объекта"}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}": {"get": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "Получение заявки по ID", "description": "Доступные роли: CONCIERGE_SALES_ADMIN, CONCIERGE_SOURCERS_ADMIN, SALES, SOURCER", "operationId": "getPurchaseOrder_1", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 123}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponsePurchaseOrderFullDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Заявка не существует"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/purchase-order/{orderId}/history": {"get": {"tags": ["Администрирование консьерж-сервиса (V2)"], "summary": "История статусов заявки", "description": "Возвращает полную историю изменений статусов", "operationId": "getPurchaseOrderHistory", "parameters": [{"name": "orderId", "in": "path", "description": "Идентификатор заявки", "required": true, "schema": {"type": "integer", "format": "int64"}, "example": 456}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2ResponseListPurchaseOrderStateHistoryDTO"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Заявка не найдена"}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/concierge/personal-shopper/filter": {"get": {"tags": ["concierge-personal-shopper-controller"], "summary": "Фильтр для списка PersonalShopper", "description": "Возвращает фильтр для PersonalShopper", "operationId": "filterPersonalShopper_1", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponsePersonalShopperFilter"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/offer/{productId}": {"delete": {"tags": ["offer-controller"], "summary": "Удаление товара из предложения", "description": "Удаление товара из предложения", "operationId": "deleteProduct", "parameters": [{"name": "productId", "in": "path", "description": "ID товара", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}, "/api/v2/admin/mercaux/offer/{productId}": {"delete": {"tags": ["mercaux-offer-controller"], "summary": "Удаление товара из предложения", "description": "Удаление товара из предложения", "operationId": "deleteProductMercaux", "parameters": [{"name": "productId", "in": "path", "description": "ID товара", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Api2ResponseVoid"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2ResponseObject"}, {"$ref": "#/components/schemas/Api2Response"}]}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Api2_1ResponseObject"}, {"$ref": "#/components/schemas/UserBanDTO"}]}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Api2Response"}}}}}}}}, "components": {"schemas": {"UserBanDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "banType": {"type": "string", "enum": ["USER_BAN", "COMMENT_BAN", "PUBLISH_BAN", "STORIES_BAN", "BARGAIN_BAN", "STREAM_BAN", "WARNING", "OSOCIAL_POST_BAN", "OSOCIAL_COMMENT_BAN", "COMMENT_SHADOW_BAN"]}, "title": {"type": "string"}, "subTitle": {"type": "string"}, "timeToEndBan": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "banned": {"type": "boolean"}}}, "Api2Response": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "object"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Api2ResponseObject": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "object"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "BuyerOffers": {"type": "object", "properties": {"proposedOffers": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ProposedOfferDTO"}}}}, "CurrencyDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "sign": {"type": "string"}, "isoCode": {"type": "string"}, "isoNumber": {"type": "integer", "format": "int32"}, "isBase": {"type": "boolean"}, "isActive": {"type": "boolean"}, "selectedByDefault": {"type": "boolean"}}}, "DescriptionStructureEnum": {"type": "object", "properties": {"code": {"type": "string"}, "localizedDescription": {"type": "string"}, "localizedStatusInfo": {"type": "string"}}}, "ImageDTO": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "url": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}}}, "OfferDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "comparisonCriteria": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["PRICE", "DELIVERY_DATE"]}}, "shipmentId": {"type": "integer", "format": "int64"}, "seller": {"$ref": "#/components/schemas/SellerInfoDTO"}, "shopper": {"$ref": "#/components/schemas/ShopperInfoDTO"}, "product": {"$ref": "#/components/schemas/ProductPlatformDTO"}, "type": {"type": "string", "enum": ["PLATFORM_PRODUCT", "BUYER_OFFER"]}, "sellerType": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "buyerOffers": {"$ref": "#/components/schemas/BuyerOffers"}}}, "ProductConditionDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "description": {"type": "string"}}}, "ProductPlatformDTO": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int64"}, "currencyPrice": {"type": "number"}, "productPhoto": {"$ref": "#/components/schemas/ImageDTO"}, "productLocation": {"type": "string"}, "brand": {"type": "string"}, "productCategory": {"type": "string"}, "availableSizes": {"type": "array", "items": {"type": "string"}}, "priceWithoutDiscount": {"type": "number"}, "priceWithDiscount": {"type": "number"}, "discountAmount": {"type": "number"}, "sizeType": {"type": "string"}, "conditionId": {"type": "integer", "format": "int32"}, "conditionName": {"type": "string"}, "productState": {"type": "string"}, "likesCount": {"type": "integer", "format": "int32"}, "isLiked": {"type": "boolean"}, "url": {"type": "string"}, "discount": {"type": "number"}, "currency": {"$ref": "#/components/schemas/CurrencyDTO"}, "isSold": {"type": "boolean"}}}, "ProposedOfferDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "proposedProduct": {"$ref": "#/components/schemas/ProductPlatformDTO"}, "rublePrice": {"type": "number"}, "deliveryDate": {"type": "string", "format": "date-time"}, "validUntil": {"type": "string", "format": "date-time"}, "creationDate": {"type": "string", "format": "date-time"}, "currency": {"type": "string", "enum": ["USD", "AED", "EUR", "KRW", "KGS", "RUB", "TL", "CNY", "JPY"]}, "currencyPrice": {"type": "number"}, "currencyRate": {"type": "number"}, "hasReceipt": {"type": "boolean"}, "isCompleteSet": {"type": "boolean"}, "hasCustomCommission": {"type": "boolean"}, "commission": {"type": "number"}, "comment": {"type": "string"}, "productCondition": {"$ref": "#/components/schemas/ProductConditionDTO"}, "offerId": {"type": "integer", "format": "int64"}}}, "SellerInfoDTO": {"type": "object", "properties": {"sellerId": {"type": "integer", "format": "int64"}, "sellerType": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "sellerFio": {"type": "string"}, "sellerEmail": {"type": "string"}, "sellerNickname": {"type": "string"}, "urlAvatar": {"type": "string"}}}, "ShimpentSizeDTO": {"type": "object", "properties": {"type": {"type": "string"}, "sizeId": {"type": "integer", "format": "int64"}, "availableSizes": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "ShipmentResponseDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "purchaseOrderId": {"type": "integer", "format": "int64"}, "categoryId": {"type": "integer", "format": "int64"}, "categoryName": {"type": "string"}, "brandId": {"type": "integer", "format": "int64"}, "brandName": {"type": "string"}, "materialAttributeId": {"type": "integer", "format": "int64"}, "materialAttributeName": {"type": "string"}, "colorAttributeId": {"type": "integer", "format": "int64"}, "colorAttributeName": {"type": "string"}, "createdAt": {"type": "string"}, "modelId": {"type": "integer", "format": "int64"}, "modelName": {"type": "string"}, "shipmentSize": {"$ref": "#/components/schemas/ShimpentSizeDTO"}, "description": {"type": "string"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "links": {"type": "array", "items": {"type": "string"}}, "comment": {"type": "string"}, "offers": {"type": "array", "items": {"$ref": "#/components/schemas/OfferDTO"}}, "orders": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "ShopperInfoDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "nickName": {"type": "string"}, "fio": {"type": "string"}, "urlAvatar": {"type": "string"}, "paymentFormat": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "interactionTypes": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/DescriptionStructureEnum"}}}}, "ShipmentCommentDTO": {"type": "object", "properties": {"shipmentId": {"type": "integer", "format": "int64"}, "comment": {"type": "string"}}}, "Api2ResponseShipmentResponseDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/ShipmentResponseDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ConciergeApplicationFormDto": {"type": "object", "properties": {"clientName": {"type": "string"}, "phoneNumber": {"type": "string"}, "reference": {"type": "string"}, "comment": {"type": "string"}, "imagesReferences": {"type": "array", "items": {"type": "string"}}, "clientChannel": {"type": "string"}}}, "Api2ResponseString": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "string"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ConciergeOrderDetailsResponse": {"type": "object", "properties": {"statusInfo": {"$ref": "#/components/schemas/StatusInfo"}, "orderDetails": {"$ref": "#/components/schemas/OrderDetails"}, "shipments": {"type": "array", "items": {"$ref": "#/components/schemas/Shipment"}}}}, "OfferApp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "titleTag": {"type": "string"}, "expirationDate": {"type": "string", "format": "date-time"}, "price": {"$ref": "#/components/schemas/Price"}, "deliveredDate": {"type": "string", "format": "date-time"}, "isExpired": {"type": "boolean"}, "isSold": {"type": "boolean"}}}, "Order": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userInfo": {"$ref": "#/components/schemas/UserInfo"}, "price": {"$ref": "#/components/schemas/Price"}, "deliveredDate": {"type": "string", "format": "date-time"}}}, "OrderDetails": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "creationDate": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "attachedPhotos": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}}}, "Price": {"type": "object", "properties": {"amount": {"type": "number", "format": "double"}, "currency": {"type": "string"}}}, "Shipment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "brand": {"type": "string"}, "size": {"$ref": "#/components/schemas/ShimpentSizeDTO"}, "category": {"type": "string"}, "model": {"type": "string"}, "color": {"type": "string"}, "material": {"type": "string"}, "link": {"type": "string"}, "description": {"type": "string"}, "attachedPhotos": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "creationDate": {"type": "string", "format": "date-time"}, "shipmentStatus": {"type": "string", "enum": ["AWAITING_PROCESSING", "IN_PROGRESS", "READY", "REJECTED"]}, "productsPlatform": {"type": "array", "items": {"$ref": "#/components/schemas/OfferApp"}}, "ordersMono": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}}}, "StatusInfo": {"type": "object", "properties": {"status": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}}}, "UserInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "avatarUrl": {"type": "string"}, "nickname": {"type": "string"}, "type": {"type": "string"}}}, "RejectionEventRequest": {"type": "object", "properties": {"objectId": {"type": "integer", "format": "int64"}, "rejectionReason": {"type": "string"}, "additionalText": {"type": "string"}}, "description": "Данные для создания события отклонения"}, "Api2ResponseConciergeOrderDetailsResponse": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/ConciergeOrderDetailsResponse"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CommentFullDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "authorId": {"type": "integer", "format": "int64"}, "isPined": {"type": "boolean"}, "message": {"type": "string"}, "datetime": {"type": "string", "format": "date-time"}}}, "CustomerInfoDTO": {"type": "object", "properties": {"customerId": {"type": "integer", "format": "int64"}, "customerNickName": {"type": "string"}, "lastname": {"type": "string"}, "firstname": {"type": "string"}, "customerPhone": {"type": "string"}, "customerEmail": {"type": "string"}, "urlAvatar": {"type": "string"}, "hasActiveBans": {"type": "boolean"}}}, "PurchaseOrderFullDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "customer": {"$ref": "#/components/schemas/CustomerInfoDTO"}, "source": {"type": "string", "enum": ["TELEGRAM", "WHATSAPP", "INSTAGRAM", "COMMENTS", "SHOWCASE", "CONCIERGE", "CANCELLED_ORDERS_NOT_CONFIRMED", "CANCELLED_ORDERS_NOT_SHIPPED", "CANCELLED_ORDERS_NOT_APPROVED", "PERSONAL_SHOPPING", "PRODUCT_PAGE", "WISHLIST", "CART", "O_TRENDS", "SALES_ADMIN", "SALES_APP", "CS_SUPPORT_CHAT", "PARTNERS_AFFILIATE"]}, "description": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "changeDate": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "sourcerInfo": {"$ref": "#/components/schemas/SourcerInfoDTO"}, "salesInfo": {"$ref": "#/components/schemas/SalesInfoDTO"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "orders": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "comments": {"type": "array", "items": {"$ref": "#/components/schemas/CommentFullDTO"}}, "rejectionReason": {"type": "string"}, "rejectionDescription": {"type": "string"}, "shipments": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentResponseDTO"}}, "link": {"type": "string"}}, "description": "Данные заявки на покупку"}, "PurchaseOrderFullResponse": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PurchaseOrderFullDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}, "description": "Полная информация о заявке"}, "SalesInfoDTO": {"type": "object", "properties": {"salesId": {"type": "integer", "format": "int64"}, "fio": {"type": "string"}, "nickName": {"type": "string"}, "urlAvatar": {"type": "string"}, "salesRole": {"type": "string", "enum": ["SALES", "SOURCER", "CUSTOMER", "STOLESHNIKOV_BOUTIQUE_SALESMAN", "KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN", "STOLESHNIKOV_ADMIN", "KUZNETSKY_BRIDGE_ADMIN", "CONCIERGE_SALES_ADMIN", "CONCIERGE_SOURCERS_ADMIN", "MERCAUX_ADMIN"]}, "email": {"type": "string"}}}, "SourcerInfoDTO": {"type": "object", "properties": {"sourcerId": {"type": "integer", "format": "int64"}, "nickName": {"type": "string"}, "fio": {"type": "string"}, "urlAvatar": {"type": "string"}, "email": {"type": "string"}}}, "PurchaseOrderMobileCreateRequest": {"type": "object", "properties": {"description": {"type": "string"}, "imagesUrl": {"type": "array", "items": {"type": "string"}}, "purchaseToNew": {"type": "boolean"}, "link": {"type": "string"}}}, "Api2ResponsePurchaseOrderFullDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PurchaseOrderFullDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Api2ResponseListString": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"type": "string"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ShipmentOffersDTO": {"type": "object", "properties": {"shipmentId": {"type": "integer", "format": "int64"}, "offers": {"type": "array", "items": {"$ref": "#/components/schemas/OfferDTO"}}}}, "Api2ResponseShipmentOffersDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/ShipmentOffersDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "SendOffersToClientRequest": {"type": "object", "properties": {"orderId": {"type": "integer", "format": "int64"}, "offerIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "proposedOfferIds": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "message": {"type": "string"}}}, "Api2ResponseVoid": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "object"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "ShipmentRequestDTO": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int64"}, "categoryName": {"type": "string"}, "creatorId": {"type": "integer", "format": "int64"}, "brandId": {"type": "integer", "format": "int64"}, "brandName": {"type": "string"}, "brandTransliterateName": {"type": "string"}, "materialAttributeId": {"type": "integer", "format": "int64"}, "materialAttributeName": {"type": "string"}, "colorAttributeId": {"type": "integer", "format": "int64"}, "colorAttributeName": {"type": "string"}, "modelId": {"type": "integer", "format": "int64"}, "modelName": {"type": "string"}, "shipmentSize": {"$ref": "#/components/schemas/ShimpentSizeDTO"}, "description": {"type": "string"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "links": {"type": "array", "items": {"type": "string"}}, "comment": {"type": "string"}}}, "Api2ResponseListShipmentResponseDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentResponseDTO"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "MercauxPurchaseOrderFullResponse": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PurchaseOrderFullDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}, "description": "Полная информация о заявке"}, "PurchaseOrderWithShipmentsCreateRequest": {"type": "object", "properties": {"customerInfo": {"$ref": "#/components/schemas/CustomerInfoDTO"}, "sourcerInfo": {"$ref": "#/components/schemas/SourcerInfoDTO"}, "salesInfo": {"$ref": "#/components/schemas/SalesInfoDTO"}, "source": {"type": "string", "enum": ["TELEGRAM", "WHATSAPP", "INSTAGRAM", "COMMENTS", "SHOWCASE", "CONCIERGE", "CANCELLED_ORDERS_NOT_CONFIRMED", "CANCELLED_ORDERS_NOT_SHIPPED", "CANCELLED_ORDERS_NOT_APPROVED", "PERSONAL_SHOPPING", "PRODUCT_PAGE", "WISHLIST", "CART", "O_TRENDS", "SALES_ADMIN", "SALES_APP", "CS_SUPPORT_CHAT", "PARTNERS_AFFILIATE"]}, "description": {"type": "string"}, "imagesUrl": {"type": "array", "items": {"type": "string"}}, "initialStatus": {"type": "string", "enum": ["CREATED", "DRAFT", "NEW", "IN_PROGRESS_SALES", "AWAITING_SOURCER", "IN_PROGRESS_SOURCER", "AWAITING_SEND_TO_CLIENT", "AWAITING_CLIENT_ANSWER", "REPEAT_REQUEST_TO_SALES", "REPEAT_AWAITING_SOURCER", "PAYED_ORDER_IN_PROGRESS", "PAYED_REPEAT_REQUEST", "PAYED_RR_SOURCER", "REJECTED", "DONE", "CANCELLED", "ALL"]}, "shipments": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentRequestDTO"}}}}, "OrdersForConciergeDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "customerInfo": {"$ref": "#/components/schemas/CustomerInfoDTO"}, "sourcerInfo": {"$ref": "#/components/schemas/SourcerInfoDTO"}, "salesInfo": {"$ref": "#/components/schemas/SalesInfoDTO"}, "source": {"type": "string", "enum": ["TELEGRAM", "WHATSAPP", "INSTAGRAM", "COMMENTS", "SHOWCASE", "CONCIERGE", "CANCELLED_ORDERS_NOT_CONFIRMED", "CANCELLED_ORDERS_NOT_SHIPPED", "CANCELLED_ORDERS_NOT_APPROVED", "PERSONAL_SHOPPING", "PRODUCT_PAGE", "WISHLIST", "CART", "O_TRENDS", "SALES_ADMIN", "SALES_APP", "CS_SUPPORT_CHAT", "PARTNERS_AFFILIATE"]}, "creationDate": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "orders": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "shipments": {"type": "array", "items": {"$ref": "#/components/schemas/ShipmentResponseDTO"}}, "link": {"type": "string"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}}}, "RequestsFilter": {"type": "object", "properties": {"orderStatusEnums": {"type": "array", "items": {"type": "string", "enum": ["CREATED", "DRAFT", "NEW", "IN_PROGRESS_SALES", "AWAITING_SOURCER", "IN_PROGRESS_SOURCER", "AWAITING_SEND_TO_CLIENT", "AWAITING_CLIENT_ANSWER", "REPEAT_REQUEST_TO_SALES", "REPEAT_AWAITING_SOURCER", "PAYED_ORDER_IN_PROGRESS", "PAYED_REPEAT_REQUEST", "PAYED_RR_SOURCER", "REJECTED", "DONE", "CANCELLED", "ALL"]}}, "orderSourceEnums": {"type": "array", "items": {"type": "string", "enum": ["TELEGRAM", "WHATSAPP", "INSTAGRAM", "COMMENTS", "SHOWCASE", "CONCIERGE", "CANCELLED_ORDERS_NOT_CONFIRMED", "CANCELLED_ORDERS_NOT_SHIPPED", "CANCELLED_ORDERS_NOT_APPROVED", "PERSONAL_SHOPPING", "PRODUCT_PAGE", "WISHLIST", "CART", "O_TRENDS", "SALES_ADMIN", "SALES_APP", "CS_SUPPORT_CHAT", "PARTNERS_AFFILIATE"]}}, "brands": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "models": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "fromDate": {"type": "string", "format": "date-time"}, "customerId": {"type": "integer", "format": "int64"}, "toDate": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "typeSorting": {"type": "string", "enum": ["STATUS", "DATE"]}, "possibleSortingTypes": {"$ref": "#/components/schemas/SortingOptionsDto"}}}, "SortingOption": {"type": "object", "properties": {"code": {"type": "string", "enum": ["STATUS", "DATE"]}, "description": {"type": "string"}, "isSelected": {"type": "boolean"}}}, "SortingOptionsDto": {"type": "object", "properties": {"sorting": {"type": "array", "items": {"$ref": "#/components/schemas/SortingOption"}}}}, "Api2ResponsePageOrdersForConciergeDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PageOrdersForConciergeDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PageOrdersForConciergeDTO": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/OrdersForConciergeDTO"}}, "totalPages": {"type": "integer", "format": "int32"}, "totalAmount": {"type": "integer", "format": "int64"}, "itemsCount": {"type": "integer", "format": "int32"}}}, "PaginatedShoppersResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalShopperDTO"}}, "itemsCount": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int64"}, "totalAmount": {"type": "integer", "format": "int64"}}}, "PersonalShopperDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "urlAvatar": {"type": "string"}, "fio": {"type": "string"}, "nickname": {"type": "string"}, "paymentFormat": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "interactionType": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/DescriptionStructureEnum"}}, "countDoneOrders": {"type": "integer", "format": "int64"}}}, "PersonalShopperFilter": {"type": "object", "properties": {"accessSources": {"type": "array", "items": {"$ref": "#/components/schemas/DescriptionStructureEnum"}}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/DescriptionStructureEnum"}}, "brandIds": {"uniqueItems": true, "type": "array", "items": {"type": "object"}}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}}, "Api2ResponsePaginatedShoppersResult": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PaginatedShoppersResult"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Brand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "brandId": {"type": "integer", "format": "int64"}}}, "PersonalShopper": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "interactionType": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["INTERNET_SITES", "MULTIBRAND", "ON_REQUEST", "UNIVERSAL"]}}, "paymentFormat": {"type": "string", "enum": ["PREPAYMENT", "POSTPAYMENT"]}, "priority": {"type": "boolean"}, "categories": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/ShopperCategory"}}, "brands": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/Brand"}}, "accessSource": {"type": "string", "enum": ["PERSONAL_BOUTIQUE_ACCESS", "EXCLUSIVE_SALE_AUCTION_ACCESS"]}, "dateShopperStatus": {"type": "string", "format": "date-time"}, "bitrixId": {"type": "string"}}}, "ShopperCategory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "code": {"type": "string", "enum": ["CLOTHING", "FOOTWEAR", "BAGS_ACCESSORIES", "WATCHES_JEWELRY", "RARE_LIMITED"]}, "name": {"type": "string"}, "personalShoppers": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/PersonalShopper"}}}}, "PersonalShopperCreateRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "nickname": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "interactionType": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["INTERNET_SITES", "MULTIBRAND", "ON_REQUEST", "UNIVERSAL"]}}, "paymentFormat": {"type": "string", "enum": ["PREPAYMENT", "POSTPAYMENT"]}, "categories": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["CLOTHING", "FOOTWEAR", "BAGS_ACCESSORIES", "WATCHES_JEWELRY", "RARE_LIMITED"]}}, "brands": {"uniqueItems": true, "type": "array", "items": {"type": "integer", "format": "int64"}}, "accessSource": {"type": "string", "enum": ["PERSONAL_BOUTIQUE_ACCESS", "EXCLUSIVE_SALE_AUCTION_ACCESS"]}, "priority": {"type": "boolean"}, "dateShopperStatus": {"type": "string", "format": "date-time"}}, "description": "Данные для создания PersonalShopper"}, "GroupingSourceQuantity": {"type": "object", "properties": {"sourceId": {"type": "string", "enum": ["TELEGRAM", "WHATSAPP", "INSTAGRAM", "COMMENTS", "SHOWCASE", "CONCIERGE", "CANCELLED_ORDERS_NOT_CONFIRMED", "CANCELLED_ORDERS_NOT_SHIPPED", "CANCELLED_ORDERS_NOT_APPROVED", "PERSONAL_SHOPPING", "PRODUCT_PAGE", "WISHLIST", "CART", "O_TRENDS", "SALES_ADMIN", "SALES_APP", "CS_SUPPORT_CHAT", "PARTNERS_AFFILIATE"]}, "quantity": {"type": "integer", "format": "int64"}, "description": {"type": "string"}}}, "GroupingStatusQuantity": {"type": "object", "properties": {"statusId": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "quantity": {"type": "integer", "format": "int64"}, "description": {"type": "string"}}}, "PurchaseOrderFilter": {"type": "object", "properties": {"groupingStatusQuantities": {"type": "array", "items": {"$ref": "#/components/schemas/GroupingStatusQuantity"}}, "groupingSourceQuantities": {"type": "array", "items": {"$ref": "#/components/schemas/GroupingSourceQuantity"}}, "brands": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "models": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "fromDate": {"type": "string", "format": "date-time"}, "toDate": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "typesSorting": {"$ref": "#/components/schemas/SortingOptionsDto"}}, "description": "Фильтр для получения заявок"}, "Api2ResponsePurchaseOrderFilter": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PurchaseOrderFilter"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PurchaseOrderCreateRequest": {"type": "object", "properties": {"customerInfo": {"$ref": "#/components/schemas/CustomerInfoDTO"}, "sourcerInfo": {"$ref": "#/components/schemas/SourcerInfoDTO"}, "salesInfo": {"$ref": "#/components/schemas/SalesInfoDTO"}, "source": {"type": "string", "enum": ["TELEGRAM", "WHATSAPP", "INSTAGRAM", "COMMENTS", "SHOWCASE", "CONCIERGE", "CANCELLED_ORDERS_NOT_CONFIRMED", "CANCELLED_ORDERS_NOT_SHIPPED", "CANCELLED_ORDERS_NOT_APPROVED", "PERSONAL_SHOPPING", "PRODUCT_PAGE", "WISHLIST", "CART", "O_TRENDS", "SALES_ADMIN", "SALES_APP", "CS_SUPPORT_CHAT", "PARTNERS_AFFILIATE"]}, "description": {"type": "string"}, "imagesUrl": {"type": "array", "items": {"type": "string"}}, "purchaseToNew": {"type": "boolean"}, "link": {"type": "string"}, "bitrixDealId": {"type": "integer", "format": "int64"}}}, "Api2ResponsePaginatedResult": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PaginatedResult"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PaginatedResult": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/OrdersForConciergeDTO"}}, "totalPages": {"type": "integer", "format": "int64"}, "totalCount": {"type": "integer", "format": "int64"}}}, "CommentsAdminFullDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "authorId": {"type": "integer", "format": "int64"}, "isPined": {"type": "boolean"}, "message": {"type": "string"}, "datetime": {"type": "string", "format": "date-time"}, "author": {"type": "string"}}}, "Api2ResponseCommentsAdminFullDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/CommentsAdminFullDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "AuthDto": {"type": "object", "properties": {"scope": {"type": "string"}, "domain": {"type": "string"}, "status": {"type": "string"}, "access_token": {"type": "string"}, "expires_in": {"type": "string"}, "server_endpoint": {"type": "string"}, "client_endpoint": {"type": "string"}, "member_id": {"type": "string"}, "refresh_token": {"type": "string"}, "application_token": {"type": "string"}}}, "DataDto": {"type": "object", "properties": {"FIELDS": {"$ref": "#/components/schemas/FieldsDto"}}}, "DealUpdateWebhookDto": {"type": "object", "properties": {"event": {"type": "string"}, "data": {"$ref": "#/components/schemas/DataDto"}, "ts": {"type": "string"}, "auth": {"$ref": "#/components/schemas/AuthDto"}, "event_handler_id": {"type": "string"}}}, "FieldsDto": {"type": "object", "properties": {"ID": {"type": "string"}}}, "PurchaseOrderMobileUpdateRequest": {"type": "object", "properties": {"description": {"type": "string"}, "imagesUrl": {"type": "array", "items": {"type": "string"}}, "link": {"type": "string"}}}, "Api2ResponseListSalesManagerProductDto": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SalesManagerProductDto"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "SalesManagerProductDto": {"type": "object", "properties": {"productId": {"type": "integer", "format": "int64"}, "seller": {"$ref": "#/components/schemas/SellerInfoDTO"}, "type": {"type": "string", "enum": ["PRODUCT_PLATFORM", "BAYER"]}, "price": {"type": "number"}, "deliveryDate": {"type": "string", "format": "date-time"}, "validUntil": {"type": "string", "format": "date-time"}, "bestPrice": {"type": "boolean"}, "fastDelivery": {"type": "boolean"}}}, "OrderInformationDto": {"type": "object", "properties": {"text": {"type": "string"}}}, "Api2ResponseBigDecimal": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "number"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Api2ResponseListCurrencyRateWithEffectiveRateDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyRateWithEffectiveRateDTO"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CurrencyRateWithEffectiveRateDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "currencyId": {"type": "integer", "format": "int64"}, "currencyToId": {"type": "integer", "format": "int64"}, "rateValue": {"type": "number"}, "commission": {"type": "number"}, "lastRateUpdateTime": {"type": "string", "format": "date-time"}, "effectiveRate": {"type": "number"}}}, "Conversion": {"type": "object", "properties": {"priceWithCommission": {"type": "number"}, "priceWithoutCommission": {"type": "number"}, "dutiesAmount": {"type": "number"}, "fixedAmount": {"type": "number"}, "commission": {"type": "number", "format": "double"}, "commissionId": {"type": "integer", "format": "int64"}, "commissionScaled": {"type": "number"}, "explanation": {"type": "string"}}, "description": "Данные преобразования цены"}, "Api2ResponseConversion": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/Conversion"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Api2ResponseListProposedOfferDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProposedOfferDTO"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "MercauxBuyerOffers": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "shopperInfo": {"$ref": "#/components/schemas/ShopperInfoDTO"}, "proposedOffers": {"type": "array", "items": {"$ref": "#/components/schemas/MercauxEnhancedProposedOfferDTO"}}}}, "MercauxEnhancedProposedOfferDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "comparisonCriteria": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["PRICE", "DELIVERY_DATE"]}}, "rublePrice": {"type": "number"}, "deliveryDate": {"type": "string", "format": "date-time"}, "validUntil": {"type": "string", "format": "date-time"}, "creationDate": {"type": "string", "format": "date-time"}, "currency": {"$ref": "#/components/schemas/CurrencyDTO"}}}, "MercauxOfferReferenceDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}}, "MercauxProductPlatformDTO": {"type": "object", "properties": {"offer": {"$ref": "#/components/schemas/MercauxOfferReferenceDTO"}, "productId": {"type": "integer", "format": "int64"}, "comparisonCriteria": {"uniqueItems": true, "type": "array", "items": {"type": "string", "enum": ["PRICE", "DELIVERY_DATE"]}}, "currencyPrice": {"type": "number"}, "productPhoto": {"$ref": "#/components/schemas/ImageDTO"}, "productLocation": {"type": "string"}, "brand": {"type": "string"}, "productCategory": {"type": "string"}, "availableSizes": {"type": "array", "items": {"type": "string"}}, "priceWithoutDiscount": {"type": "number"}, "priceWithDiscount": {"type": "number"}, "discountAmount": {"type": "number"}, "sizeType": {"type": "string"}, "conditionId": {"type": "integer", "format": "int32"}, "conditionName": {"type": "string"}, "productState": {"type": "string"}, "url": {"type": "string"}, "discount": {"type": "number"}, "currency": {"$ref": "#/components/schemas/CurrencyDTO"}, "sellerInfo": {"$ref": "#/components/schemas/SellerInfoDTO"}}}, "MercauxPurchaseOrderFullDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "customer": {"$ref": "#/components/schemas/CustomerInfoDTO"}, "source": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "description": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "changeDate": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "sourcerInfo": {"$ref": "#/components/schemas/SourcerInfoDTO"}, "salesInfo": {"$ref": "#/components/schemas/SalesInfoDTO"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "orders": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "comments": {"type": "array", "items": {"$ref": "#/components/schemas/CommentFullDTO"}}, "rejectionReason": {"type": "string"}, "rejectionDescription": {"type": "string"}, "shipments": {"type": "array", "items": {"$ref": "#/components/schemas/MercauxShipmentResponseDTO"}}, "link": {"type": "string"}}}, "MercauxShipmentResponseDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "purchaseOrderId": {"type": "integer", "format": "int64"}, "categoryId": {"type": "integer", "format": "int64"}, "categoryName": {"type": "string"}, "brandId": {"type": "integer", "format": "int64"}, "brandName": {"type": "string"}, "materialAttributeId": {"type": "integer", "format": "int64"}, "materialAttributeName": {"type": "string"}, "colorAttributeId": {"type": "integer", "format": "int64"}, "colorAttributeName": {"type": "string"}, "createdAt": {"type": "string"}, "modelId": {"type": "integer", "format": "int64"}, "modelName": {"type": "string"}, "shipmentSize": {"$ref": "#/components/schemas/ShimpentSizeDTO"}, "description": {"type": "string"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDTO"}}, "links": {"type": "array", "items": {"type": "string"}}, "comment": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/MercauxProductPlatformDTO"}}, "buyerOffers": {"type": "array", "items": {"$ref": "#/components/schemas/MercauxBuyerOffers"}}}}, "Api2ResponseMercauxPurchaseOrderFullDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/MercauxPurchaseOrderFullDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "Api2ResponsePersonalShopperFilter": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/PersonalShopperFilter"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "OrderSources": {"type": "object", "properties": {"sources": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Api2ResponseOrderSources": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/OrderSources"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "RejectionReason": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "objectType": {"type": "string", "enum": ["PURCHASE_ORDER", "PRODUCT"]}, "orderStatus": {"type": "string", "enum": ["CREATED", "DRAFT", "NEW", "IN_PROGRESS_SALES", "AWAITING_SOURCER", "IN_PROGRESS_SOURCER", "AWAITING_SEND_TO_CLIENT", "AWAITING_CLIENT_ANSWER", "REPEAT_REQUEST_TO_SALES", "REPEAT_AWAITING_SOURCER", "PAYED_ORDER_IN_PROGRESS", "PAYED_REPEAT_REQUEST", "PAYED_RR_SOURCER", "REJECTED", "DONE", "CANCELLED", "ALL"]}, "role": {"type": "string", "enum": ["SALES", "SOURCER", "CUSTOMER", "STOLESHNIKOV_BOUTIQUE_SALESMAN", "KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN", "STOLESHNIKOV_ADMIN", "KUZNETSKY_BRIDGE_ADMIN", "CONCIERGE_SALES_ADMIN", "CONCIERGE_SOURCERS_ADMIN", "MERCAUX_ADMIN"]}, "reasonText": {"type": "string"}, "requiresDescription": {"type": "boolean"}}, "description": "Список доступных причин отклонения"}, "Api2ResponseListRejectionReason": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RejectionReason"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "PurchaseOrderStateHistoryDTO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "purchaseOrderId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "userNickName": {"type": "string"}, "sourceState": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "targetState": {"$ref": "#/components/schemas/DescriptionStructureEnum"}, "transitionDate": {"type": "string", "format": "date-time"}, "comment": {"type": "string"}, "reasonReturn": {"type": "string"}}}, "Api2ResponseListPurchaseOrderStateHistoryDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PurchaseOrderStateHistoryDTO"}}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}, "CommentsAdminListDTO": {"type": "object", "properties": {"comments": {"type": "array", "items": {"$ref": "#/components/schemas/CommentsAdminFullDTO"}}}, "description": "Список комментариев заявки с пагинацией"}, "Api2ResponseCommentsAdminListDTO": {"type": "object", "properties": {"message": {"type": "string"}, "humanMessage": {"type": "string"}, "data": {"$ref": "#/components/schemas/CommentsAdminListDTO"}, "errorData": {"type": "object"}, "validationMessages": {"type": "object", "additionalProperties": {"type": "string"}}, "timestamp": {"type": "integer", "format": "int64"}, "executionTimeMillis": {"type": "integer", "format": "int64"}, "success": {"type": "boolean"}}}}, "securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Введите куки в формате: JSESSIONID=your_session_id", "name": "<PERSON><PERSON>", "in": "header"}}}}