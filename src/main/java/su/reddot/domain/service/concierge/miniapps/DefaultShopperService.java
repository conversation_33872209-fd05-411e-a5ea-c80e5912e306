package su.reddot.domain.service.concierge.miniapps;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.concierge.api.ShopperOffersControllerApi;
import su.reddot.domain.service.concierge.model.OfferDetailsDTO;
import su.reddot.domain.service.concierge.model.ShopperFilterItemsRequest;
import su.reddot.domain.service.concierge.model.ShopperAvailableFiltersRequest;
import su.reddot.domain.service.concierge.oskellyconcierge.ConciergeHTTPHandlerService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.concierge.ShopperBrandFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoryFilterDto;
import su.reddot.domain.service.dto.concierge.ShopperAvailableFiltersResponseDto;
import su.reddot.domain.service.filter.processor.filter.impl.BrandFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.CategoryFilterProcessor;
import su.reddot.presentation.api.v2.filter.ProductFilterRequest;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.ProductFilterProcessorEngine;
import su.reddot.domain.service.filter.FiltrationServiceFeaturesService;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.concierge.model.PaginatedOffersResult;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@Slf4j
@Service
public class DefaultShopperService extends ConciergeHTTPHandlerService implements ShopperService {

    private final BrandService brandService;
    private final CategoryService categoryService;
    private final ProductFilterProcessorEngine productFilterProcessorEngine;
    private final FiltrationServiceFeaturesService featuresService;
    private final ShopperOffersControllerApi shopperOffersControllerApi;
    private final SecurityService securityService;

    public DefaultShopperService(MicrometerService micrometerService, BrandService brandService, CategoryService categoryService, ProductFilterProcessorEngine productFilterProcessorEngine, FiltrationServiceFeaturesService featuresService, ShopperOffersControllerApi shopperOffersControllerApi, SecurityService securityService) {
        super(micrometerService);
        this.brandService = brandService;
        this.categoryService = categoryService;
        this.productFilterProcessorEngine = productFilterProcessorEngine;
        this.featuresService = featuresService;
        this.shopperOffersControllerApi = shopperOffersControllerApi;
        this.securityService = securityService;
    }

    @Override
    public ShopperBrandsResponseDto getBrands() {
        log.debug("Получение брендов для шопера");

        try {
            List<BrandDTO> brandDTOs = brandService.getBrandsAvailableForPublishing(null);

            List<ShopperBrandFilterDto> brands = brandDTOs.stream()
                    .map(this::mapBrandToFilterDto)
                    .collect(Collectors.toList());

            return new ShopperBrandsResponseDto()
                    .setBrands(brands)
                    .setTotalCount(brands.size());

        } catch (Exception e) {
            log.error("Ошибка при получении брендов для шопера", e);
            return new ShopperBrandsResponseDto()
                    .setBrands(Collections.emptyList())
                    .setTotalCount(0);
        }
    }

    @Override
    public ShopperCategoriesResponseDto getCategories() {
        log.debug("Получение категорий для шопера");

        try {
            List<CategoryDTO> categoryDTOs = categoryService.getDirectChildrenCategoryDTOsCached(CategoryService.ROOT_CATEGORY_ID, true);

            List<ShopperCategoryFilterDto> categories = categoryDTOs.stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());

            return new ShopperCategoriesResponseDto()
                    .setCategories(categories)
                    .setTotalCount(categories.size());

        } catch (Exception e) {
            log.error("Ошибка при получении категорий для шопера", e);
            return new ShopperCategoriesResponseDto()
                    .setCategories(Collections.emptyList())
                    .setTotalCount(0);
        }
    }
    
    /**
     * Маппинг BrandDTO в ShopperBrandFilterDto
     *
     * @param brandDTO исходный объект бренда
     * @return DTO для фильтра шопера, содержащий информацию о бренде
     */
    private ShopperBrandFilterDto mapBrandToFilterDto(BrandDTO brandDTO) {
        return new ShopperBrandFilterDto()
                .setId(brandDTO.getId())
                .setName(brandDTO.getName())
                .setUrlName(brandDTO.getUrlName())
                .setTransliterateName(brandDTO.getTransliterateName())
                .setProductsCount(brandDTO.getProductsCount())
                .setIsHidden(brandDTO.getIsHidden())
                .setTitle(brandDTO.getTitle())
                .setHiddenDescription(brandDTO.getHiddenDescription());
    }
    /**
     * Маппинг CategoryDTO в ShopperCategoryFilterDto
     *
     * @param categoryDTO исходный объект категории
     * @return DTO для фильтра шопера, содержащий информацию о категории
     */
    private ShopperCategoryFilterDto mapCategoryToFilterDto(CategoryDTO categoryDTO) {
        ShopperCategoryFilterDto dto = new ShopperCategoryFilterDto()
                .setId(categoryDTO.getId())
                .setDisplayName(categoryDTO.getDisplayName())
                .setSingularName(categoryDTO.getSingularName())
                .setFullName(categoryDTO.getFullName())
                .setPluralName(categoryDTO.getPluralName())
                .setUrl(categoryDTO.getUrl())
                .setAlternativeUrl(categoryDTO.getAlternativeUrl())
                .setIcon(categoryDTO.getIcon())
                .setProductsCount(categoryDTO.getProductsCount())
                .setHasChildren(categoryDTO.getHasChildren())
                .setDefaultSizeType(categoryDTO.getDefaultSizeType() != null ? categoryDTO.getDefaultSizeType().name() : null);
        
        // Маппинг дочерних категорий
        if (!CollectionUtils.isEmpty(categoryDTO.getChildren())) {
            List<ShopperCategoryFilterDto> children = categoryDTO.getChildren().stream()
                    .map(this::mapCategoryToFilterDto)
                    .collect(Collectors.toList());
            dto.setChildren(children);
        }
        
        return dto;
    }

    @Override
    public ProductFilter getFilterInfo(ProductFilterInfoRequest request, String code) {
        log.debug("Получение информации о фильтре с кодом: {}", code);

        try {
            return productFilterProcessorEngine.getFilterInfo(
                    request,
                    code,
                    featuresService.getFiltrationServiceFeatures()
            );

        } catch (Exception e) {
            log.error("Ошибка при получении информации о фильтре с кодом: {}", code, e);
            throw new RuntimeException("Не удалось получить информацию о фильтре", e);
        }
    }

    @Override
    public PaginatedOffersResult getFilterItems(ShopperFilterItemsRequest request) {
        log.debug("Получение элементов фильтра для шопера");
        Long userId = securityService.getCurrentAuthorizedUserId();
        return executeHttpCall("ShopperOffersControllerApi.getFilterItems",
                () -> shopperOffersControllerApi.getOffers(userId, request));
    }

    @Override
    public OfferDetailsDTO getOfferDetails(Long offerId) {
        log.debug("Получение деталей оффера {}", offerId);
        Long userId = securityService.getCurrentAuthorizedUserId();
        OfferDetailsDTO offerDetailsDTO = executeHttpCall("ShopperOffersControllerApi.getOfferDetails",
                () -> shopperOffersControllerApi.getOfferDetails(userId, offerId));
        // ToDo Тут надо обогатить как минимум size
        return offerDetailsDTO;
    }

    @Override
    public ProductFilters getAvailableFilters(ShopperFilterItemsRequest request) {
        log.debug("Получение доступных фильтров для шопера");

        try {
            // Преобразуем ShopperFilterItemsRequest в ProductFilterRequest
            ProductFilterRequest productFilterRequest = convertToProductFilterRequest(request);

            // Получаем все доступные фильтры через стандартный механизм
            ProductFilters allFilters = productFilterProcessorEngine.getAvailableFilters(
                    productFilterRequest,
                    featuresService.getFiltrationServiceFeatures(),
                    null // catalogSlotFeaturesService.getCatalogSlotExperimentFeatures() можно добавить при необходимости
            );

            // Фильтруем результат, оставляя только бренды и категории
            return filterBrandsAndCategories(allFilters);

        } catch (Exception e) {
            log.error("Ошибка при получении доступных фильтров для шопера", e);
            // Возвращаем пустой объект ProductFilters в случае ошибки
            return new ProductFilters(
                    Collections.emptyList(), // sorting
                    Collections.emptyList(), // filters
                    Collections.emptyList(), // hotFilters
                    null, // items
                    0L // itemsCount
            );
        }
    }

    /**
     * Преобразование ShopperFilterItemsRequest в ProductFilterRequest
     */
    private ProductFilterRequest convertToProductFilterRequest(ShopperFilterItemsRequest shopperRequest) {
        ProductFilterRequest productRequest = new ProductFilterRequest();

        // Устанавливаем базовые параметры
        productRequest.setWithItems(false); // Нам не нужны товары, только фильтры
        productRequest.setWithAvailableValues(true); // Нужны доступные значения фильтров
        productRequest.setWithFilterSubscriptions(false); // Подписки на фильтры не нужны

        // Преобразуем фильтры из Map<String, Object> в Map<String, JsonNode>
        if (shopperRequest.getFilters() != null) {
            Map<String, JsonNode> filters = new HashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();

            for (Map.Entry<String, Object> entry : shopperRequest.getFilters().entrySet()) {
                JsonNode jsonNode = objectMapper.valueToTree(entry.getValue());
                filters.put(entry.getKey(), jsonNode);
            }
            productRequest.setFilters(filters);
        }

        // Преобразуем пресеты из Map<String, Object> в Map<String, JsonNode>
        if (shopperRequest.getPresets() != null) {
            Map<String, JsonNode> presets = new HashMap<>();
            ObjectMapper objectMapper = new ObjectMapper();

            for (Map.Entry<String, Object> entry : shopperRequest.getPresets().entrySet()) {
                JsonNode jsonNode = objectMapper.valueToTree(entry.getValue());
                presets.put(entry.getKey(), jsonNode);
            }
            productRequest.setPresets(presets);
        }

        return productRequest;
    }

    /**
     * Фильтрация ProductFilters для оставления только брендов и категорий
     */
    private ProductFilters filterBrandsAndCategories(ProductFilters originalFilters) {
        if (originalFilters == null || originalFilters.getFilters() == null) {
            return new ProductFilters(
                    Collections.emptyList(), // sorting
                    Collections.emptyList(), // filters
                    Collections.emptyList(), // hotFilters
                    null, // items
                    0L // itemsCount
            );
        }

        // Фильтруем только бренды и категории
        List<ProductFilter> filteredFilters = originalFilters.getFilters().stream()
                .filter(filter -> BrandFilterProcessor.FILTER_CODE.equals(filter.getCode()) ||
                                CategoryFilterProcessor.FILTER_CODE.equals(filter.getCode()))
                .collect(Collectors.toList());

        // Фильтруем hotFilters, оставляя только коды брендов и категорий
        List<String> filteredHotFilters = originalFilters.getHotFilters() != null ?
                originalFilters.getHotFilters().stream()
                        .filter(hotFilter -> BrandFilterProcessor.FILTER_CODE.equals(hotFilter) ||
                                           CategoryFilterProcessor.FILTER_CODE.equals(hotFilter))
                        .collect(Collectors.toList()) :
                Collections.emptyList();

        return new ProductFilters(
                originalFilters.getSorting(), // Сохраняем сортировку
                filteredFilters, // Только бренды и категории
                filteredHotFilters, // Только горячие фильтры брендов и категорий
                null, // items не нужны
                originalFilters.getItemsCount() // Сохраняем общее количество товаров
        );
    }
}
