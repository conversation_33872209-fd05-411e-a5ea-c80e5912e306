package su.reddot.domain.service.concierge.miniapps;

import su.reddot.domain.service.concierge.model.OfferDetailsDTO;
import su.reddot.domain.service.concierge.model.ShopperFilterItemsRequest;
import su.reddot.domain.service.concierge.model.ShopperAvailableFiltersRequest;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperAvailableFiltersResponseDto;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.concierge.model.PaginatedOffersResult;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;

/**
 * Сервис для работы с фильтрами шопера в консьерж-сервисе
 */
public interface ShopperService {

    /**
     * Получение брендов для шопера
     *
     * @return ответ с брендами
     */
    ShopperBrandsResponseDto getBrands();

    /**
     * Получение категорий для шопера
     *
     * @return ответ с категориями
     */
    ShopperCategoriesResponseDto getCategories();

    /**
     * Получение информации о фильтрах продуктов
     *
     * @param request запрос с параметрами фильтрации
     * @param code код фильтра (например, "category")
     * @return информация о фильтре
     */
    ProductFilter getFilterInfo(ProductFilterInfoRequest request, String code);

    /**
     * Получение элементов фильтра для шопера
     *
     * @param request запрос с параметрами фильтрации элементов
     * @return набор элементов из PaginatedOffersResult
     */
    PaginatedOffersResult getFilterItems(ShopperFilterItemsRequest request);

    /**
     * Получение деталей оффера
     *
     * @param offerId - ID оффера
     * @return - детали оффера
     */
    OfferDetailsDTO getOfferDetails(Long offerId);

    /**
     * Получение доступных фильтров (брендов и категорий) для шопера
     * Аналог метода getAvailableFilters из ProductsFilterControllerApiV2,
     * но специально адаптированный для шопера
     *
     * @param request запрос с параметрами фильтрации
     * @return объединенный ответ с доступными брендами и категориями
     */
    ProductFilters getAvailableFilters(ShopperFilterItemsRequest request);
}
