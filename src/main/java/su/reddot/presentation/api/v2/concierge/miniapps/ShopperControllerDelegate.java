package su.reddot.presentation.api.v2.concierge.miniapps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import su.reddot.domain.service.concierge.model.OfferDetailsDTO;
import su.reddot.domain.service.concierge.model.ShopperFilterItemsRequest;
import su.reddot.domain.service.concierge.model.ShopperAvailableFiltersRequest;
import su.reddot.domain.service.dto.concierge.ShopperBrandsResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperCategoriesResponseDto;
import su.reddot.domain.service.dto.concierge.ShopperAvailableFiltersResponseDto;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.concierge.model.PaginatedOffersResult;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;

@RequestMapping("/api/v2/concierge/miniapps/shopper")
public interface ShopperControllerDelegate {
    @PostMapping("/filter/info")
    @Operation(
            summary = "Получение информации о фильтрах продуктов",
            description = "Получение информации о фильтрах продуктов для шопера",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Информация о фильтре успешно получена",
                            content = @Content(schema = @Schema(implementation = ProductFilterResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Пользователь не авторизован"
                    ),
                    @ApiResponse(
                            responseCode = "403",
                            description = "Доступ запрещен"
                    )
            }
    )
    Api2Response<ProductFilter> getFilterInfo(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Параметры фильтрации",
                    required = true,
                    content = @Content(schema = @Schema(implementation = ProductFilterInfoRequest.class))
            )
            @RequestBody ProductFilterInfoRequest request,
            @Parameter(
                    description = "Код фильтра",
                    example = "category",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam String code
    );

    @PostMapping("/filter/items")
    @Operation(
            summary = "Получение списка элементов offer для шопера по фильтру",
            description = "Получение набора элементов из PaginatedOffersResult для шопера",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Элементы фильтра успешно получены",
                            content = @Content(schema = @Schema(implementation = ShopperFilterItemsResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Пользователь не авторизован"
                    ),
                    @ApiResponse(
                            responseCode = "403",
                            description = "Доступ запрещен"
                    )
            }
    )
    Api2Response<PaginatedOffersResult> getFilterItems(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Параметры фильтрации элементов",
                    required = true,
                    content = @Content(schema = @Schema(implementation = ShopperFilterItemsRequest.class))
            )
            @RequestBody ShopperFilterItemsRequest request
    );

    @PostMapping("/available-filters")
    @Operation(
            summary = "Получение доступных фильтров (брендов и категорий) для шопера",
            description = "Получение объединенного списка доступных брендов и категорий для фильтрации товаров в консьерж-сервисе. Аналог метода getAvailableFilters из ProductsFilterControllerApiV2, но специально адаптированный для шопера.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Доступные фильтры успешно получены",
                            content = @Content(schema = @Schema(implementation = ProductFilters.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Пользователь не авторизован"
                    ),
                    @ApiResponse(
                            responseCode = "403",
                            description = "Доступ запрещен"
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса"
                    )
            }
    )
    Api2Response<ProductFilters> getAvailableFilters(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Параметры запроса доступных фильтров",
                    required = true,
                    content = @Content(schema = @Schema(implementation = ShopperFilterItemsRequest.class))
            )
            @RequestBody ShopperFilterItemsRequest request
    );


    @Operation(
            summary = "Получение деталей оффера",
            description = "Возвращает детальную информацию об оффере для мини-приложений",
            operationId = "getOfferDetails",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = OfferDetailsDTO.class)
                            )),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Оффер не найден"
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса"
                    )
            }
    )
    @GetMapping(
            value = "/item",
            produces = {"application/json"}
    )
    Api2Response<OfferDetailsDTO> getOfferDetails(
            @Parameter(
                    name = "offerId",
                    description = "ID оффера для получения деталей",
                    required = true,
                    in = ParameterIn.QUERY,
                    example = "67890")
            @RequestParam(name = "offerId") Long offerId
    );

    /**
     * Вспомогательный класс для документации ответа с брендами
     */
    @Schema(description = "Ответ с брендами для шопера")
    class ShopperBrandsResponse extends Api2Response<ShopperBrandsResponseDto> {
        @Schema(description = "Данные брендов")
        private ShopperBrandsResponseDto data;
    }

    /**
     * Вспомогательный класс для документации ответа с категориями
     */
    @Schema(description = "Ответ с категориями для шопера")
    class ShopperCategoriesResponse extends Api2Response<ShopperCategoriesResponseDto> {
        @Schema(description = "Данные категорий")
        private ShopperCategoriesResponseDto data;
    }

    /**
     * Вспомогательный класс для документации ответа с информацией о фильтре
     */
    @Schema(description = "Ответ с информацией о фильтре продуктов")
    class ProductFilterResponse extends Api2Response<ProductFilter> {
        @Schema(description = "Данные фильтра")
        private ProductFilter data;
    }

    /**
     * Вспомогательный класс для документации ответа с элементами фильтра
     */
    @Schema(description = "Ответ с элементами фильтра для шопера")
    class ShopperFilterItemsResponse extends Api2Response<PaginatedOffersResult> {
        @Schema(description = "Данные элементов фильтра")
        private PaginatedOffersResult data;
    }

    /**
     * Вспомогательный класс для документации ответа с доступными фильтрами
     */
    @Schema(description = "Ответ с доступными фильтрами (брендами и категориями) для шопера")
    class ShopperAvailableFiltersResponse extends Api2Response<ShopperAvailableFiltersResponseDto> {
        @Schema(description = "Данные доступных фильтров")
        private ShopperAvailableFiltersResponseDto data;
    }
}
