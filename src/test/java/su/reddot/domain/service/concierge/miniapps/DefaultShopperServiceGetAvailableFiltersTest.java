package su.reddot.domain.service.concierge.miniapps;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.concierge.api.ShopperOffersControllerApi;
import su.reddot.domain.service.concierge.model.ShopperFilterItemsRequest;
import su.reddot.domain.service.filter.FiltrationServiceFeaturesService;
import su.reddot.domain.service.filter.ProductFilterProcessorEngine;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.processor.filter.impl.BrandFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.CategoryFilterProcessor;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v2.filter.ProductFilterRequest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DefaultShopperServiceGetAvailableFiltersTest {

    @Mock
    private MicrometerService micrometerService;

    @Mock
    private BrandService brandService;

    @Mock
    private CategoryService categoryService;

    @Mock
    private ProductFilterProcessorEngine productFilterProcessorEngine;

    @Mock
    private FiltrationServiceFeaturesService featuresService;

    @Mock
    private ShopperOffersControllerApi shopperOffersControllerApi;

    @Mock
    private SecurityService securityService;

    @InjectMocks
    private DefaultShopperService shopperService;

    private ShopperFilterItemsRequest request;
    private ProductFilters mockProductFilters;

    @BeforeEach
    void setUp() {
        request = new ShopperFilterItemsRequest();
        
        // Подготовка mock фильтров
        List<ProductFilter> allFilters = Arrays.asList(
                createMockFilter(BrandFilterProcessor.FILTER_CODE, "Бренды"),
                createMockFilter(CategoryFilterProcessor.FILTER_CODE, "Категории"),
                createMockFilter("color", "Цвет"),
                createMockFilter("size", "Размер"),
                createMockFilter("price", "Цена")
        );

        List<String> hotFilters = Arrays.asList(
                BrandFilterProcessor.FILTER_CODE,
                CategoryFilterProcessor.FILTER_CODE,
                "color"
        );

        mockProductFilters = new ProductFilters(
                Collections.emptyList(), // sorting
                allFilters,
                hotFilters,
                null, // items
                1000L // itemsCount
        );
    }

    private ProductFilter createMockFilter(String code, String name) {
        // Создаем mock объект ProductFilter
        // В реальном тесте здесь должен быть конкретный тип фильтра
        return new ProductFilter() {
            @Override
            public String getCode() {
                return code;
            }

            @Override
            public String getName() {
                return name;
            }

            @Override
            public String getType() {
                return "MOCK";
            }
        };
    }

    @Test
    void getAvailableFilters_ShouldReturnOnlyBrandsAndCategories_WhenAllFiltersProvided() {
        // Given
        when(productFilterProcessorEngine.getAvailableFilters(any(ProductFilterRequest.class), any(), any()))
                .thenReturn(mockProductFilters);

        // When
        ProductFilters result = shopperService.getAvailableFilters(request);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertEquals(2, result.getFilters().size());
        
        // Проверяем, что остались только бренды и категории
        Set<String> filterCodes = new HashSet<>();
        for (ProductFilter filter : result.getFilters()) {
            filterCodes.add(filter.getCode());
        }
        
        assertTrue(filterCodes.contains(BrandFilterProcessor.FILTER_CODE));
        assertTrue(filterCodes.contains(CategoryFilterProcessor.FILTER_CODE));
        assertFalse(filterCodes.contains("color"));
        assertFalse(filterCodes.contains("size"));
        assertFalse(filterCodes.contains("price"));

        // Проверяем hot filters
        assertNotNull(result.getHotFilters());
        assertEquals(2, result.getHotFilters().size());
        assertTrue(result.getHotFilters().contains(BrandFilterProcessor.FILTER_CODE));
        assertTrue(result.getHotFilters().contains(CategoryFilterProcessor.FILTER_CODE));
        assertFalse(result.getHotFilters().contains("color"));

        // Проверяем, что itemsCount сохранился
        assertEquals(1000L, result.getItemsCount());
    }

    @Test
    void getAvailableFilters_ShouldReturnEmptyFilters_WhenNoBrandsAndCategoriesAvailable() {
        // Given
        List<ProductFilter> filtersWithoutBrandsAndCategories = Arrays.asList(
                createMockFilter("color", "Цвет"),
                createMockFilter("size", "Размер")
        );

        ProductFilters filtersWithoutTarget = new ProductFilters(
                Collections.emptyList(),
                filtersWithoutBrandsAndCategories,
                Arrays.asList("color", "size"),
                null,
                500L
        );

        when(productFilterProcessorEngine.getAvailableFilters(any(ProductFilterRequest.class), any(), any()))
                .thenReturn(filtersWithoutTarget);

        // When
        ProductFilters result = shopperService.getAvailableFilters(request);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertEquals(0, result.getFilters().size());
        assertNotNull(result.getHotFilters());
        assertEquals(0, result.getHotFilters().size());
        assertEquals(500L, result.getItemsCount());
    }

    @Test
    void getAvailableFilters_ShouldHandleNullProductFilters_AndReturnEmptyResult() {
        // Given
        when(productFilterProcessorEngine.getAvailableFilters(any(ProductFilterRequest.class), any(), any()))
                .thenReturn(null);

        // When
        ProductFilters result = shopperService.getAvailableFilters(request);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertEquals(0, result.getFilters().size());
        assertNotNull(result.getHotFilters());
        assertEquals(0, result.getHotFilters().size());
        assertEquals(0L, result.getItemsCount());
    }

    @Test
    void getAvailableFilters_ShouldHandleException_AndReturnEmptyResult() {
        // Given
        when(productFilterProcessorEngine.getAvailableFilters(any(ProductFilterRequest.class), any(), any()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ProductFilters result = shopperService.getAvailableFilters(request);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertEquals(0, result.getFilters().size());
        assertNotNull(result.getHotFilters());
        assertEquals(0, result.getHotFilters().size());
        assertEquals(0L, result.getItemsCount());
    }

    @Test
    void getAvailableFilters_ShouldConvertRequestCorrectly() {
        // Given
        Map<String, Object> filters = new HashMap<>();
        filters.put("brand", Arrays.asList(1L, 2L));
        filters.put("category", Arrays.asList(10L, 20L));
        request.setFilters(filters);

        Map<String, Object> presets = new HashMap<>();
        presets.put("popularBrands", Arrays.asList(5L, 6L));
        request.setPresets(presets);

        when(productFilterProcessorEngine.getAvailableFilters(any(ProductFilterRequest.class), any(), any()))
                .thenReturn(mockProductFilters);

        // When
        ProductFilters result = shopperService.getAvailableFilters(request);

        // Then
        assertNotNull(result);
        // Проверяем, что метод не упал и вернул результат
        assertEquals(2, result.getFilters().size());
    }
}
