test:
  temp-dir: /home/<USER>/tmp/
  api:
    user-id: 838
    user-nickname: ivanbelykh
    user-email: <EMAIL>
    user-password: 111
    user-phone: "+79202341740"
    user2-id: 920
    user2-nickname: i<PERSON><PERSON><PERSON><PERSON><PERSON>
    user2-email: <EMAIL>
    user2-password: 111
    host: localhost # mars.oskelly.tech or oskelly.ru
    port: 42 # 8080 # 443, -1 => autoLocalPort, specify real port when you want to call real server (e.g. btqe import)
    existed-nickname: blog
    address-endpoint-test-order-id: 1087197
    user3-id: 11
    user4-id: 27
    celebrity-user-id: 19553

#userbandata
    ban-user-id: 838
    ban-user-nickname: ivanbelykh
    ban-user-email: <EMAIL>
    ban-user-password: 111
    ban-seeler-id: 920

    promo-code-absolute: TESTABSOLUTE2000
    promo-code-absolute-value: 2000

  cart:
    amount-limit-category-example-id: 309

  #Режим дебага для тестов. Позволяет локально отлавливать редкие кейсы в тестах.
  debug: false

  receipts:
    order-id-with-1-positions: 1118013
    order-id-with-5-positions: 1109854
    order_id_receipts_b2p: 1114034
    order-id-receipts-tcb: 1108043

    order-id-marking-code: 1110719
    order-id-agent-receipt: 1118761
    order-id-agent-company-form: ООО
    order-id-agent-company-name: "ПРИМЕР КОМ"
    order-id-agent-company-taxn: **********

    send-to-email-1st: <EMAIL>
    send-to-email-2nd: <EMAIL>

    mock-server-host: localhost
    mock-server-tcb-bank-port: 8181
    mock-server-receipts-port: 8182
    mock-server-b2p-bank-port: 8183
    mock-server-non-bank-port: 8184

  usedesk:
    export-emails: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
    export-limits: 3
    api-test-user:
      username: TestUser_for_CRUD_test
      mailaddr: <EMAIL>
      phone-id: 8(800)111-11-11
    phoneDupeUser:
      usermail: <EMAIL>
      phoneId1: '***********'
      phoneId2: '+***********'
      phoneId3: '+7(901)234-56-78'

test-prepayments:
  agent-seller-id: 10919  # Depo seller
  usual-seller-id: 27
  i11ls-seller-id: 99     # International seller (duties test)
  cbord-seller-id: 19183  # CrossBord (Tags & crossborder)
  pickup-id: 1584
  delivery-id: 1585
  agent-seller-counterparty-id: 1674 #
  usual-seller-counterparty-id: 1427 #
  i11ls-seller-counterparty-id: 3484 #
  cbord-seller-counterparty-id: 3514 #

test-boutique:
  buyer-id: 12 # <EMAIL>

test-product-item:
  product-id: 1710
  size-id: 112
  size-name: 95

app:
  queue:
    producer:
      change:
        product:
          enabled: true

  productPriceDiscount:
    schedule: "-"
    period: 2
    periodChronoUnit: SECONDS
    maxCount: 2
    percent: 10